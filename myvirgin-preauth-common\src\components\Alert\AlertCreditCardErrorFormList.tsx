import * as React from "react";
import {Alert, Heading, Text} from "@virgin/virgin-react-ui-library";
import AlertNotificationListItem from "./AlertNotificationListItem";
import AlertNotificationList from "./AlertNotificationList";
import { injectIntl } from "react-intl";

export interface InputRefs {
  inputCreditCardNumber: React.RefObject<HTMLInputElement>;
  inputCreditCardHolderName: React.RefObject<HTMLInputElement>;
  inputCreditCardSecurityCode: React.RefObject<HTMLInputElement>;
  inputCreditCardExpiryYear: React.RefObject<HTMLSelectElement>;
  inputCreditCardExpiryMonth: React.RefObject<HTMLSelectElement>;
  inputBankAccountNumber: React.RefObject<HTMLInputElement>;
  inputTransitNumber: React.RefObject<HTMLInputElement>;
  inputBankAccountHolder: React.RefObject<HTMLInputElement>;
  inputBankName: React.RefObject<HTMLSelectElement>;
}


interface ErrorFormListProps {
  intl: any;
  isErrorCardNumber?: boolean;
  isErrorCardName?: boolean;
  isErrorExpiryDate?: boolean;
  isErrorSecurityCode?: boolean;
  isErrorBankAccountHolderName?: boolean; 
  isErrorBankAccountNumber ?: boolean;
  isErrorBankName?: boolean; 
  iserrorBankTransit?: boolean;
  inputRefs: InputRefs;
}

const AlertCreditCardErrorFormListComponent = ({intl, isErrorCardNumber, isErrorCardName, isErrorExpiryDate, isErrorSecurityCode, isErrorBankAccountHolderName, isErrorBankAccountNumber, isErrorBankName, iserrorBankTransit, inputRefs}: ErrorFormListProps) => (
  <Alert 
    variant="error" 
    className="payment-block sm:payment-flex payment-p-16 sm:payment-p-24 payment-relative payment-rounded-16"
    iconSize="32"
    id="alert-3">
    <Text elementType="div" className="payment-pl-0 payment-pt-12 sm:payment-pl-16 sm:payment-pt-0">
      <Heading level="h3" variant="xs" className=" sm:vrui-mt-7 vrui-mb-12 vrui-font-sans vrui-leading-20">
        <span aria-hidden="true">{intl.formatMessage({id: "ALERT_ERROR_HEADING"})}</span>
        <span className="payment-sr-only">{intl.formatMessage({id: "ALERT_ERROR_HEADING_SR"})}</span>
      </Heading>
      <div>
        <AlertNotificationList>
          {isErrorCardNumber && inputRefs && (
            <AlertNotificationListItem 
              id="error-2"
              label={intl.formatMessage({id: "CREDIT_CARD_NUMBER_LABEL"})} 
              labelDescription={intl.formatMessage({id: "ALERT_ERROR_VALID_CC_NUMBER"})}
              variant="errorList"
              inputRef={inputRefs.inputCreditCardNumber}/>
          )}
              
          {isErrorCardName && inputRefs && (
            <AlertNotificationListItem 
              id="error-3"
              label={intl.formatMessage({id: "CREDIT_CARD_NAME_LABEL"})} 
              labelDescription={intl.formatMessage({id: "ALERT_ERROR_GENERAL_DESC"})} 
              variant="errorList"
              inputRef={inputRefs.inputCreditCardHolderName}/>
          )}

          {isErrorExpiryDate && inputRefs && (
            <AlertNotificationListItem 
              id="error-4"
              label={intl.formatMessage({id: "CREDIT_CARD_EXPIRY_LABEL"})} 
              labelDescription={intl.formatMessage({id: "ALERT_ERROR_GENERAL_DESC"})} 
              variant="errorList"
              inputRef={inputRefs.inputCreditCardExpiryMonth}/>
          )}

          {isErrorSecurityCode && inputRefs && (
            <AlertNotificationListItem 
              id= "error-5"
              label={intl.formatMessage({id: "CREDIT_CARD_SECURITY_CODE_INPUT_LABEL"})} 
              labelDescription={intl.formatMessage({id: "ALERT_ERROR_GENERAL_DESC"})} 
              variant="errorList"
              inputRef={inputRefs.inputCreditCardSecurityCode}/>
          )}

          {isErrorBankName && inputRefs && (
            <AlertNotificationListItem
              id="error-2"
              label={intl.formatMessage({ id: "BANK_NAME_LABEL" })}
              labelDescription={intl.formatMessage({ id: "ALERT_ERROR_GENERAL_DESC" })}
              variant="errorList"
              inputRef={inputRefs.inputBankName} />
          )}

          {isErrorBankAccountHolderName && inputRefs && (
            <AlertNotificationListItem
              id="error-3"
              label={intl.formatMessage({ id: "BANK_HOLDER_NAME_LABEL" })}
              labelDescription={intl.formatMessage({ id: "ALERT_ERROR_GENERAL_DESC" })}
              variant="errorList"
              inputRef={inputRefs.inputBankAccountHolder} />
          )}

          {iserrorBankTransit && inputRefs && (
            <AlertNotificationListItem
              id="error-4"
              label={intl.formatMessage({ id: "BANK_TRANSIT_NUMBER_LABEL" })}
              labelDescription={intl.formatMessage({ id: "ALERT_ERROR_GENERAL_DESC" })}
              variant="errorList"
              inputRef={inputRefs.inputTransitNumber} />
          )}

          {isErrorBankAccountNumber && inputRefs && (
            <AlertNotificationListItem
              id="error-5"
              label={intl.formatMessage({ id: "BANK_ACCOUNT_NUMBER_LABEL" })}
              labelDescription={intl.formatMessage({ id: "ALERT_ERROR_GENERAL_DESC" })}
              variant="errorList"
              inputRef={inputRefs.inputBankAccountNumber} />
          )}
              
        </AlertNotificationList>
      </div>
    </Text>
  </Alert>
);


export const AlertCreditCardErrorFormList = injectIntl(AlertCreditCardErrorFormListComponent);
