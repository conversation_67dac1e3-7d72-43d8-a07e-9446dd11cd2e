import React, { forwardRef } from "react";
import { FormControl, Text,Icon, InputText, Label, FormGroup, Select, SelectOption, ListItem } from "@virgin/virgin-react-ui-library";
import { injectIntl } from "react-intl";
import { LightBoxNoname, LightBoxSecurityCode } from "../LightBox";
import { getMonthList, getYearList, numberOnly } from "../../utils";
import PaymentRadioCard from "./PaymentMethodRadio";
import { CreditCardPaymentProps } from "../../models/CreditCardPaymentPropsModel";
import {NotifCard} from "../NotifCard";

const CreditCardPaymentComponent = forwardRef<HTMLInputElement, CreditCardPaymentProps>(function CreditCardPaymentComponent(
  {
    intl,
    Checked,
    onChange,
    isPreauth,
    hasCreditCardDetails,
    bankitems,
    radioRef,
    handleBankRadioChange,
    isBankChecked,
    cardNumber,
    handleCreditCardChange,
    inputRefs,
    cardIcons,
    cardType,
    errorCardNumber,
    errorCardName,
    errorExpiryDate,
    errorSecurityCode,
    handleMaskCVV,
    CVV,
    creditCardAutopayOffers,
    debitCardAutopayOffers,
    checkedBillItems,
    language,
    managePreauth = null,
    IsAutopayCreditEnabled
  },
  ref
) {
  
  
  const checkedBanOffers = () => {
    const filteredOffer: any = [];
    creditCardAutopayOffers && creditCardAutopayOffers?.map((item) => {
      checkedBillItems && checkedBillItems?.map((billItem) => {
        if (item.Ban === billItem.BillName) {
          filteredOffer.push(item);
        }
      });
    });
    return filteredOffer;
  };
  const checkedBanDebitOffers = () => {
    const filteredOffer: any = [];
    debitCardAutopayOffers && debitCardAutopayOffers?.map((item) => {
      checkedBillItems && checkedBillItems?.map((billItem) => {
        if (item.Ban === billItem.BillName) {
          filteredOffer.push(item);
        }
      });
    });
    return filteredOffer;
  };
  const CreditOffers =
  {
    label: intl.formatMessage({ id: "PAYMENT_METHOD" }),
    credits: bankitems && bankitems.length > 1 ? checkedBanOffers() : creditCardAutopayOffers
  };
  const getTotalOffers = () => bankitems && bankitems.length > 1 ? checkedBanOffers().reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {
      
    const offersCount = ban.AutopayEligibleSubscribers.reduce(
      (count, subscriber) => count + subscriber.autopayOffers.length,
      0
    );
    return total + offersCount;
  }, 0): creditCardAutopayOffers && creditCardAutopayOffers.reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {
      
    const offersCount = ban.AutopayEligibleSubscribers.reduce(
      (count, subscriber) => count + subscriber.autopayOffers.length,
      0
    );
    return total + offersCount;
  }, 0);
  const getTotalDebitOffers = () => bankitems && bankitems.length > 1 ? checkedBanDebitOffers().reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {
     
    const offersCount = ban.AutopayEligibleSubscribers.reduce(
      (count, subscriber) => count + subscriber.autopayOffers.length,
      0
    );
    return total + offersCount;
  }, 0): debitCardAutopayOffers && debitCardAutopayOffers.reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {
     
    const offersCount = ban.AutopayEligibleSubscribers.reduce(
      (count, subscriber) => count + subscriber.autopayOffers.length,
      0
    );
    return total + offersCount;
  }, 0);
  const isTrueAutopayOffer = (): boolean => 
    CreditOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => credit.eligiblePaymentMethods === ";D;C;")
      )
    ) || false // Return false if no match found
  ;
  const offerImpactRemoved = (): boolean => 
    CreditOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => credit.offerImpact === "REMOVE")
      )
    ) || false // Return false if no match found
  ;
  const offerImpactGain = (): boolean => 
    CreditOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => (credit.offerImpact === "GAIN" ||credit.offerImpact === "" || credit.offerImpact === null)&&(credit.offerImpact !== "RETAIN")) 
      )
    ) || false // Return false if no match found
  ;
  const offerImpactIncrease = (): boolean => 
    CreditOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => credit.offerImpact === "INCREASE")
      )
    ) || false // Return false if no match found
  ;
  const offerImpactReduce = (): boolean => 
    CreditOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => credit.offerImpact === "REDUCE")
      )
    ) || false // Return false if no match found
  ;
  const groupedGainOffers = CreditOffers?.credits?.reduce((acc: any, debit: any) => {
    debit.AutopayEligibleSubscribers.forEach((item: any) => {
      if (!acc[debit.banInfo.nickName]) {
        acc[debit.banInfo.nickName] = [];
      }
      item.autopayOffers.forEach((credit: any) => {
        if (credit.offerImpact !== "RETAIN") {
          acc[debit.banInfo.nickName].push({
            phoneNumber: item.subscriberTelephoneNumber,
            discountAmount: credit.discountAmount,
          });
        }
      });
    });
    return acc;
  }, {});

  const groupedIncreasedOffers = CreditOffers?.credits?.reduce((acc: any, debit: any) => {
    debit.AutopayEligibleSubscribers.forEach((item: any) => {
      if (!acc[debit.banInfo.nickName]) {
        acc[debit.banInfo.nickName] = [];
      }
      item.autopayOffers.forEach((credit: any) => {
        if (credit.offerImpact !== "RETAIN") {
          acc[debit.banInfo.nickName].push({
            phoneNumber: item.subscriberTelephoneNumber,
            currentDiscountAmount: credit.currentdiscountAmount,
            discountAmount: credit.discountAmount,
          });
        }
      });
    });
    return acc;
  }, {});

  const groupedReducedOffers = CreditOffers?.credits?.reduce((acc: any, debit: any) => {
    debit.AutopayEligibleSubscribers.forEach((item: any) => {
      if (!acc[debit.banInfo.nickName]) {
        acc[debit.banInfo.nickName] = [];
      }
      item.autopayOffers.forEach((credit: any) => {
        if (credit.offerImpact !== "RETAIN") {
          acc[debit.banInfo.nickName].push({
            phoneNumber: item.subscriberTelephoneNumber,
            currentDiscountAmount: credit.currentdiscountAmount,
            discountAmount: credit.discountAmount,
          });
        }
      });
    });
    return acc;
  }, {});

  const groupedRemovedOffers = CreditOffers?.credits?.reduce((acc: any, debit: any) => {
    debit.AutopayEligibleSubscribers.forEach((item: any) => {
      if (!acc[debit.banInfo.nickName]) {
        acc[debit.banInfo.nickName] = [];
      }
      item.autopayOffers.forEach((credit: any) => {
        if (credit.offerImpact !== "RETAIN") {
          acc[debit.banInfo.nickName].push({
            phoneNumber: item.subscriberTelephoneNumber,
            discountAmount: credit.discountAmount,
          });
        }
      });
    });
    return acc;
  }, {});
  const CREDITCARD_PAYMENT_LABEL = managePreauth !== null ? managePreauth === "Creditcard" ? 
    intl.formatMessage({ id: "UPDATE_CREDITCARD_PAYMENT" }) : intl.formatMessage({ id: "SWITCH_TO_CREDIT_CARD" }) 
    : 
    intl.formatMessage({ id: "CREDIT_CARD_LABEL" });   

  const LABEL_LOADED_OFFERS = isTrueAutopayOffer()
    ? (Object.keys(groupedGainOffers).length > 1
      ? intl.formatMessage({ id: "LABEL_LOADED_OFFERS_TRUE_AUTOPAY_TITLE" })
      : intl.formatMessage({ id: "LABEL_LOADED_OFFER_TRUE_AUTOPAY_TITLE" }))
    : (Object.keys(groupedGainOffers).length > 1
      ? intl.formatMessage({ id: "LABEL_LOADED_OFFERS_CREDIT_TITLE" })
      : intl.formatMessage({ id: "LABEL_LOADED_OFFER_CREDIT_TITLE" }));
            
  return (
    <div className="vrui-mb-15">
      <PaymentRadioCard
        id="payment-radio-credit"
        name="payment-radio"
        label={CREDITCARD_PAYMENT_LABEL}
        headingLevel="h3"
        defaultChecked={Checked ? true : undefined}
        ref={ref}
        onChange={onChange}
      >
        { IsAutopayCreditEnabled && <div>
          {/* Gain -usecase */}
          {offerImpactGain() ? (
            <div role="alert">
              <NotifCard 
                id = "Credit-discount-offer"
                hasNotifCard={Object.keys(groupedGainOffers).length > 0}
                variant="greatNews"
                label={intl.formatMessage({ id: "GREAT_NEWS" })}
                label1={LABEL_LOADED_OFFERS}
                label2={intl.formatMessage({ id: "MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE_HEADING" })}
                label3={Object.keys(groupedGainOffers).length > 1
                  ? intl.formatMessage({ id: "MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE" })
                  : intl.formatMessage({ id: "MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE" })}
              >
                {Object.entries(groupedGainOffers).map(([ban, offers]: [string, any]) => (
                  <div key={ban}>
                    {bankitems && bankitems.length > 1 ? <p className="payment-text-12 payment-text-darkblue payment-mb-5">{ban}:</p> : "" }
                    <ul className="payment-list-disc payment-list-inside payment-mb-10">
                      {offers.map((offer: any, index: number) => (
                        <ListItem key={index} className="payment-text-12 payment-text-darkblue payment-mb-5 payment-leading-16">
                          {offer.phoneNumber} -&nbsp;
                          <div className="payment-text-darkblue payment-inline payment-lowercase !payment-text-12 payment-font-bold" aria-hidden="true">
                            <span>{intl.formatMessage({ id: "DEBIT_PAYMENT_OFFER_AMOUNT" }, { amount: offer.discountAmount })}</span>
                          </div>
                          <span className="vrui-sr-only">{intl.formatMessage({ id: "DEBIT_PAYMENT_OFFER_AMOUNT_SR" }, { amount: offer.discountAmount })}</span>
                        </ListItem>
                      ))}
                    </ul>
                  </div>
                ))}
              </NotifCard>
            </div>
          ) : null}
          {/* increase -usecase */}
          {offerImpactIncrease() ? (
            <div role="alert">
              <NotifCard 
                id = "Credit-discount-offer"
                hasNotifCard={Object.keys(groupedIncreasedOffers).length > 0}
                variant="greatNews"
                label={intl.formatMessage({ id: "GREAT_NEWS" })}
                label1={Object.keys(groupedIncreasedOffers).length > 1
                  ? intl.formatMessage({ id: "LABEL_LOADED_OFFERS_INCREASED_CREDIT_TITLE" })
                  : intl.formatMessage({ id: "LABEL_LOADED_OFFER_INCREASED_CREDIT_TITLE" })}
              >
                {Object.entries(groupedIncreasedOffers).map(([ban, offers]: [string, any]) => (
                  <div key={ban}>
                    {bankitems && bankitems.length > 1 ? <p className="payment-text-12 payment-text-darkblue payment-mb-5">{ban}:</p> : "" }
                    <ul className="payment-list-disc payment-list-inside payment-mb-10">
                      {offers.map((offer: any, index: number) => (
                        <ListItem key={index} className="payment-text-12 payment-text-darkblue payment-mb-5 payment-leading-18">
                          {offer.phoneNumber} -&nbsp;from
                          <div className="payment-text-darkblue payment-inline payment-lowercase !payment-text-12 payment-font-bold" aria-hidden="true">
                            <span>{intl.formatMessage({ id: "DEBIT_PAYMENT_OFFER_AMOUNT" }, { amount: offer.currentDiscountAmount })}</span>
                          </div>
                          <span className="vrui-sr-only">{intl.formatMessage({ id: "DEBIT_PAYMENT_OFFER_AMOUNT_SR" }, { amount: offer.currentDiscountAmount })}</span>
                          to
                          <div className="payment-text-darkblue payment-inline payment-lowercase !payment-text-14 payment-font-bold" aria-hidden="true">
                            <span>{intl.formatMessage({ id: "DEBIT_PAYMENT_OFFER_AMOUNT" }, { amount: offer.discountAmount })}</span>
                          </div>
                          <span className="vrui-sr-only">{intl.formatMessage({ id: "DEBIT_PAYMENT_OFFER_AMOUNT_SR" }, { amount: offer.discountAmount })}</span>
                        </ListItem>
                      ))}
                    </ul>
                  </div>
                ))}
              </NotifCard>
            </div>
          ) : null}
          {/* reduce -usecase */}
          {offerImpactReduce() ? (
            <div role="alert">
              <NotifCard 
                id = "Credit-discount-offer"
                hasNotifCard={Object.keys(groupedReducedOffers).length > 0}
                variant="notifCardWarning"
                label={Object.keys(groupedReducedOffers).length > 1
                  ? intl.formatMessage({ id: "LABEL_LOADED_OFFERS_REDUCED_DEBIT_TITLE" })
                  : intl.formatMessage({ id: "LABEL_LOADED_OFFERS_REDUCED_DEBIT_TITLE" })}
              >
                {Object.entries(groupedReducedOffers).map(([ban, offers]: [string, any]) => (
                  <div key={ban}>
                    {bankitems && bankitems.length > 1 ? <p className="payment-text-12 payment-text-darkblue payment-mb-5">{ban}:</p> : "" }
                    <ul className="payment-list-disc payment-list-inside payment-mb-10">
                      {offers.map((offer: any, index: number) => (
                        <ListItem key={index} className="payment-text-12 payment-text-darkblue payment-mb-5 payment-leading-18">
                          {offer.phoneNumber} -&nbsp;from
                          <div className="payment-text-darkblue payment-inline payment-lowercase !payment-text-12 payment-font-bold" aria-hidden="true">
                            <span>{intl.formatMessage({ id: "DEBIT_PAYMENT_OFFER_AMOUNT" }, { amount: offer.currentDiscountAmount })}</span>
                          </div>
                          <span className="payment-sr-only">{intl.formatMessage({ id: "DEBIT_PAYMENT_OFFER_AMOUNT_SR" }, { amount: offer.currentDiscountAmount })}</span>
                          to
                          <div className="payment-text-darkblue payment-inline payment-lowercase !payment-text-12 payment-font-bold" aria-hidden="true">
                            <span>{intl.formatMessage({ id: "DEBIT_PAYMENT_OFFER_AMOUNT" }, { amount: offer.discountAmount })}</span>
                          </div>
                          <span className="vrui-sr-only">{intl.formatMessage({ id: "DEBIT_PAYMENT_OFFER_AMOUNT_SR" }, { amount: offer.discountAmount })}</span>
                        </ListItem>
                      ))}
                    </ul>
                  </div>
                ))}
              </NotifCard>
            </div>
          ) : null} 
          {/* remove -usecase */}
          {offerImpactRemoved()?
            <div role="alert">
              <NotifCard
                id = "Credit-discount-offer"
                hasNotifCard={Object.keys(groupedRemovedOffers).length > 0}
                variant="notifCardWarning"
                label={getTotalOffers()> 1
                  ? intl.formatMessage({ id: "LABEL_LOADED_OFFERS_REMOVED_CREDIT" })
                  : intl.formatMessage({ id: "LABEL_LOADED_OFFER_REMOVED_CREDIT" })}
                label2={intl.formatMessage({ id: "MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE_HEADING" })}
                label3={getTotalOffers()> 1
                  ? intl.formatMessage({ id: "MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE" })
                  : intl.formatMessage({ id: "MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE" })}
              >
                {Object.entries(groupedRemovedOffers).map(([ban, offers]: [string, any]) => (
                  <div key={ban}>
                    {bankitems && bankitems.length > 1 ? <p className="payment-text-12 payment-text-darkb;ue payment-mb-5">{ban}:</p> : "" }
                    <ul className="payment-list-disc payment-list-inside payment-mb-10">
                      {offers.map((offer: any, index: number) => (
                        <ListItem key={index} className="payment-text-12 payment-text-darkblue payment-mb-5 payment-leading-18">
                          {offer.phoneNumber} -&nbsp;
                          <div className="payment-text-darkblue payment-inline payment-lowercase !payment-text-12 payment-font-bold" aria-hidden="true">
                            <span>{intl.formatMessage({ id: "DEBIT_PAYMENT_OFFER_AMOUNT" }, { amount: offer.discountAmount })}</span>
                          </div>
                          <span className="vrui-sr-only">{intl.formatMessage({ id: "DEBIT_PAYMENT_OFFER_AMOUNT_SR" }, { amount: offer.discountAmount })}</span>
                        </ListItem>
                      ))}
                    </ul>
                  </div>
                ))}
              </NotifCard>
            </div>:null }
          {/* sorry -usecase */}
          {debitCardAutopayOffers && debitCardAutopayOffers.length > 0 && getTotalOffers() === 0 && getTotalDebitOffers() > 0 ?
            <Text role="alert" className="payment-bg-gray-5 payment-flex payment-flex-col sm:payment-flex-row payment-p-15 payment-gap-15 payment-rounded-[16px] payment-mb-10" elementType="div">
              <div className="payment-flex payment-size-20 payment-items-center payment-justify-center">
                <Icon className="payment-text-20" iconClass="vi_vrui" iconName="vi_info_c_tk"></Icon>
              </div>
              <Text id={getTotalOffers() === 0 ? "Credit-discount-offer":""} className="payment-text-14 payment-leading-20 payment-text-darkblue" elementType="p"> {intl.formatMessage({ id: "SORRY_MESSAGE" })}</Text>
            </Text> : ""}
           
        </div>}
        <div>
          <form noValidate>
            {(isBankChecked || (!isPreauth && !isBankChecked) || (isPreauth)) && (
              <>
                {/* card number */}
                <FormControl className="sm:vrui-flex-row payment-mt-30">
                  <div className="sm:payment-w-[174px] sm:payment-min-w-[174px] vrui-flex vrui-flex-col sm:payment-self-start sm:payment-text-right payment-mr-0 sm:payment-mr-30 vrui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0">
                    <div>
                      <Label htmlFor="card-number" isError={errorCardNumber} required={true}>
                        {intl.formatMessage({ id: "CREDIT_CARD_NUMBER_LABEL" })}
                      </Label>
                      <span id="cc-number-label" className="vrui-sr-only">{intl.formatMessage({ id: "CREDIT_CARD_NUMBER_SR_LABEL" })}</span>
                    </div>
                    <Text id="cc-number-desc" elementType="div" className="vrui-text-12 leading-14 sm:vrui-ml-14 vrui-text-gray payment-mt-5">
                      {intl.formatMessage({ id: "CREDIT_CARD_NUMBER_DESC_INPUT_LABEL" })}
                    </Text>
                  </div>

                  <div className="vrui-flex vrui-flex-col sm:vrui-flex-row vrui-flex-wrap">
                    <InputText
                      value={cardNumber}
                      onChange={handleCreditCardChange}
                      onInput={(e: any) => numberOnly(e)}
                      className="sm:!payment-w-[280px] payment-mr-12"
                      id="card-number"
                      required={true}
                      isError={errorCardNumber}
                      errorMessage={intl.formatMessage({ id: "ERROR_CREDIT_CARD_NUMBER_INPUT_LABEL" })}
                      ref={inputRefs.inputCreditCardNumber}
                      aria-labelledby="cc-number-label cc-number-desc"               
                    />

                    <div className="sm:payment-h-44 payment-ml-0 payment-mt-10 sm:payment-mt-[6px] vrui-flex payment-items-baseline vrui-gap-15"
                      aria-label={intl.formatMessage({ id: "CC_IMAGE_SR_LABEL" })}
                      role="img"
                    >
                      {Object.entries(cardIcons).map(([type, iconSrc]) => (
                        <img
                          key={type}
                          src={iconSrc}
                          alt={`${type} card`}
                          className="vrui-h-32 payment-mr-15 vrui-object-contain"
                          aria-hidden="true"
                          style={{
                            opacity: cardType === type ? 1 : 0.5, // Highlight detected card type
                            transition: "opacity 0.3s ease-in-out",
                          }}
                        />
                      ))}
                    </div>
                  </div>
                </FormControl>

                {/* card name */}
                <FormControl className="sm:vrui-flex-row payment-mt-30">
                  <div className="sm:payment-w-[174px] sm:payment-min-w-[174px] vrui-flex sm:payment-self-start vrui-flex-col sm:payment-text-right payment-mr-0 sm:payment-mr-30 vrui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0">
                    <div>
                      <Label htmlFor="text-2" isError={errorCardName} required={true}>
                        {intl.formatMessage({ id: "CREDIT_CARD_NAME_LABEL" })}
                      </Label>
                      <span id="cc-name-label" className="vrui-sr-only">{intl.formatMessage({ id: "CREDIT_CARD_NAME_SR_LABEL" })}</span>
                    </div>
                    <Text id="cc-name-desc" elementType="div" className="vrui-text-12 leading-14 sm:vrui-ml-14 vrui-text-gray">
                      {intl.formatMessage({ id: "CREDIT_CARD_NAME_DESC_INPUT_LABEL" })}
                    </Text>
                  </div>
                  <div className="vrui-flex vrui-flex-col sm:vrui-flex-row vrui-flex-wrap">
                    <InputText
                      // onBlur={(e) => onCardHolderNameChange(e.target.value)}
                      className="sm:!payment-w-[280px]"
                      id="text-2"
                      aria-labelledby="cc-name-label cc-name-desc"
                      required={true}
                      isError={errorCardName}
                      errorMessage={intl.formatMessage({ id: "ERROR_CREDIT_CARD_NAME_INPUT_LABEL" })}
                      minLength={5}
                      maxLength={70}
                      ref={inputRefs.inputCreditCardHolderName}
                    />
                    <div className="vrui-flex payment-items-baseline sm:payment-h-44 sm:payment-ml-10 payment-mt-5 sm:payment-mt-7">
                      <LightBoxNoname />
                    </div>
                  </div>
                </FormControl>
                {/* expiration date */}
                <FormControl className="sm:vrui-flex-row payment-mt-30">
                  <div className="sm:payment-w-[174px] sm:payment-min-w-[174px] vrui-flex vrui-flex-col sm:payment-text-right payment-mr-0 sm:payment-mr-30 vrui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0">
                    <Label id="label-3" isError={errorExpiryDate} required={true} >
                      {intl.formatMessage({ id: "CREDIT_CARD_EXPIRY_LABEL" })}
                    </Label>
                    <span id="expiry-month-label" className="vrui-sr-only">{intl.formatMessage({ id: "CREDIT_CARD_EXPIRY_SR_MONTH_LABEL" })}</span>
                  </div>

                  <div className="vrui-flex-col">
                    <FormGroup className="vrui-flex vrui-flex-wrap" hasError={errorExpiryDate} errorMessage={intl.formatMessage({ id: "ERROR_CREDIT_CARD_EXPIRY_INPUT_LABEL" })}
                    >
                      <div className="payment-w-[75px]">
                        <Select
                          name="month"
                          id="select-12"
                          defaultValue=""
                          placeHolder={intl.formatMessage({ id: "CREDIT_CARD_MONTH_PLACEHOLDER" })}
                          onChange={() => { }}
                          disableDropdownIcon
                          className="vrui-text-gray payment-select-box"
                          ref={inputRefs.inputCreditCardExpiryMonth}
                          aria-required={true}
                          aria-labelledby="expiry-month-label"
                        >
                          {getMonthList().map((item, index) => (
                            <SelectOption value={item.toString()} id={"option-" + index} displayName={item.toString()} />
                          ))}
                        </Select>
                      </div>
                      <div className="payment-w-[75px] payment-ml-10">
                        <Select
                          name="year"
                          id="select-2"
                          defaultValue=""
                          placeHolder={intl.formatMessage({ id: "CREDIT_CARD_YEAR_PLACEHOLDER" })}
                          onChange={() => { }}
                          disableDropdownIcon
                          className="vrui-text-gray payment-select-box"
                          ref={inputRefs.inputCreditCardExpiryYear}
                          aria-required={true}
                          aria-labelledby="expiry-year-label"
                        >
                          {getYearList().map((item, index: number) => (
                            <SelectOption value={item.toString()} id={"option-year-" + index} displayName={item.toString()} />
                          ))}
                        </Select>
                        <span id="expiry-year-label" className="vrui-sr-only">{intl.formatMessage({ id: "CREDIT_CARD_EXPIRY_SR_YEAR_LABEL" })}</span>
                      </div>
                    </FormGroup>
                  </div>
                </FormControl>
                {/* security code */}
                <FormControl className="sm:vrui-flex-row payment-mt-30">
                  <div className="sm:payment-w-[174px] sm:payment-min-w-[174px] vrui-flex vrui-flex-col sm:payment-text-right payment-mr-0 sm:payment-mr-30 vrui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0">
                    <Label htmlFor="text-4" isError={errorSecurityCode} required={true}>
                      {intl.formatMessage({ id: "CREDIT_CARD_SECURITY_CODE_INPUT_LABEL" })}
                    </Label>
                    <span id="cc-security-code-label" className="vrui-sr-only">{intl.formatMessage({ id: "CREDIT_CARD_SECURITY_CODE_INPUT_SR_LABEL" })}</span>
                  </div>
                  <div className="vrui-flex sm:vrui-flex-row vrui-flex-wrap">
                    <InputText
                      onInput={(e: any) => numberOnly(e)}
                      className="!payment-w-[75px] payment-mr-10"
                      id="text-4"
                      required={true}
                      isError={errorSecurityCode}
                      errorMessage={intl.formatMessage({ id: "ERROR_CREDIT_CARD_SECURITY_CODE_INPUT_LABEL" })}
                      errorMessageClassName="payment-w-[75px] payment-text-nowrap"
                      type="password"
                      onChange={handleMaskCVV}
                      ref={inputRefs.inputCreditCardSecurityCode}
                      aria-labelledby="cc-security-code-label"
                    />
                    <InputText type="hidden" id="text-hidden" value={CVV} />
                    <div className="vrui-flex vrui-items-center vrui-h-44">
                      <LightBoxSecurityCode />
                    </div>
                  </div>
                </FormControl>
              </>
            )}
          </form>
          <Text className="vrui-text-12 vrui-leading-14 vrui-text-gray payment-mt-44 sm:payment-mt-44 vrui-inline-block">
            {intl.formatMessage({ id: "REQUIRED_LABEL" })}
          </Text>

          {/* {(isBankChecked || (!isPreauth && !isBankChecked) || (isPreauth && !hasCreditCardDetails)) && (
            <Text className="vrui-text-14 vrui-leading-18 vrui-text-gray payment-mt-45 sm:payment-mt-45 vrui-inline-block">
              {intl.formatMessage({ id: "REQUIRED_LABEL" })}
            </Text>
          )} */}
        </div>
      </PaymentRadioCard>
    </div>
  );
});

export const CreditCardPayment = injectIntl(CreditCardPaymentComponent);
