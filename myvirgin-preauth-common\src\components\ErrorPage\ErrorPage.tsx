import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ing,Icon, Text } from "@virgin/virgin-react-ui-library";

export interface ErrorPageProps {
  iconVariant: "error" | "warning";
  errorHeaderText: string;
  errorHeaderStyle?: string;
  errorHeaderTextSR?: string;
  errorText: React.ReactNode;
  errorTextStyle?: string;
  buttonText: string;
  onButtonClick: () => void;
  suggestionList?: {
    label: string,
    items: string[]
  }
}

const variant = {
  error: "payment-text-red payment-text-[60px] payment-box-border payment-static payment-text-center",
  warning: "payment-text-yellow payment-text-[60px] payment-box-border payment-static payment-text-center"
};

const ErrorPage = ({
  iconVariant,
  errorHeaderText,
  errorHeaderTextSR,
  errorHeaderStyle,
  errorTextStyle,
  errorText,
  buttonText,
  onButtonClick,
  suggestionList
}: ErrorPageProps) => (
  <div className="payment-text-center payment-mt-[60px] sm:payment-max-w-[543px] md:payment-max-w-[548px] payment-mx-auto">
    <span aria-label="Warning">
      <span role="img" aria-hidden="true">
        <Icon
          iconClass={"vi_vrui"}
          iconName={"vi_warning_c"}
          className={variant[iconVariant]}
        />
      </span>
    </span>
    <Heading level="h2" variant="default" className={["payment-text-26 payment-leading-28 sm:payment-text-30 sm:payment-leading-36 md:payment-text-32 md:payment-leading-38 payment-text-black payment-mb-24 payment-mt-30", errorHeaderStyle].join(" ").trim()}>
      <span aria-hidden="true">{errorHeaderText}</span>
      <span aria-hidden="false" className="payment-sr-only">{errorHeaderTextSR}</span>
    </Heading>
    <p className={["payment-text-gray payment-overflow-x-hidden payment-font-sans payment-text-[16px] payment-leading-[25px]", errorTextStyle].join(" ").trim()}>{errorText}</p>

    {suggestionList && 
        <>
          <Divider direction="horizontal" width={1} className="payment-my-32 payment-bg-gray-3" />
          <div className="payment-mb-32">
            <Text className="payment-text-14 payment-text-black payment-font-bold">{suggestionList.label}</Text>
            <div className="payment-mt-24 payment-text-left sm:payment-text-center">
              {suggestionList.items.map((item) => (
                <div className="payment-flex sm:!payment-inline-block sm:payment-justify-center payment-mb-5 last:payment-mb-0 payment-gap-5">
                  <span className="payment-mr-8" role="img" aria-hidden="true">
                    <Icon
                      iconClass={"vi_vrui"}
                      iconName={"vi_small_check_v2"}
                      className="payment-text-16 payment-text-[#131C35]"
                    />
                  </span>
                  <Text className="payment-leading-18 payment-text-14 payment-mt-3 payment-text-[#444D63]">{item}</Text>
                </div>
              ))}
            </div>
          </div>
        </>
    }
      
    <div className="payment-rounded-lg payment-m-20 payment-mb-32 payment-[focus-within:ring-4]">
      <Button onClick={onButtonClick}>
        {buttonText}
      </Button>
    </div>
  </div>
);

export default ErrorPage;
