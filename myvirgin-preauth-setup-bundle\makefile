build:
	git clone https://gitlab.int.bell.ca/uxp/myvirgin-preauth-setup.git
	cd myvirgin-preauth-setup;git checkout ${CI_COMMIT_REF_NAME} || git checkout Release; rm package-lock.json; ls; npm install --legacy-peer-deps --prefer-offline --no-audit --progress=false; npm run build || true 
	git clone https://gitlab.int.bell.ca/uxp/myvirgin-preauth-common.git
	cd myvirgin-preauth-common; git checkout ${CI_COMMIT_REF_NAME} || git checkout Release;rm package-lock.json;  npm install --legacy-peer-deps --prefer-offline --no-audit --progress=false; npm run build || true
	git clone https://gitlab.int.bell.ca/uxp/myvirgin-preauth-setup-bundle.git
	cd myvirgin-preauth-setup-bundle; git checkout ${CI_COMMIT_REF_NAME} || git checkout Release;rm package-lock.json;  npm install --legacy-peer-deps --prefer-offline --no-audit --progress=false; npm run build || true; npm install --package-lock-only --legacy-peer-deps
