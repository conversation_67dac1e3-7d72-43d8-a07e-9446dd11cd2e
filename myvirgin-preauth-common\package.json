{"name": "myvirgin-preauth-common", "version": "1.0.0", "description": "", "main": "./dist/myvirgin-preauth-common.js", "types": "./dist/@types/index.d.ts", "private": true, "author": "VIRGIN", "license": "MIT", "scripts": {"linklocal": "linklocal", "dev": "webpack -w", "build": "npm run build:tailwind && webpack", "build:dev": "webpack --env -d", "build:prod": "webpack --env -p", "test": "echo \"Error: no test specified\" && exit 1", "start": "http-server --cors ./", "build:tailwind": "npx tailwindcss build -i ./src/css/styles.css -o ./src/css/virgin-payment-flow.css --minify", "watch:tailwind": "npx tailwindcss -i ./src/css/styles.css -o ./src/css/virgin-payment-flow.css --watch"}, "devDependencies": {"@types/react-router-dom": "*", "@virgin/virgin-react-ui-library": "^3.11.12", "bwtk": "git+https://gitlab.int.bell.ca/uxp/bwtk.git#v6.1.0", "husky": "4.3.8", "tailwindcss": "^3.4.1"}, "husky": {"hooks": {"pre-commit": "node pre-commit.js"}}}