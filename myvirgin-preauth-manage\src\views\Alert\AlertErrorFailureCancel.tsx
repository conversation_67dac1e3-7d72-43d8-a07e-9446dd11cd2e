import * as React from "react";
import { Alert, Heading, Text, Button } from "@virgin/virgin-react-ui-library";
import { DivProps } from "@virgin/virgin-react-ui-library/dist/types/src/_types";
import { injectIntl } from "react-intl";

interface AlertErrorFailureCancelProps extends DivProps {
  intl: any;
  hasMultipleFailure: boolean;
}

const AlertErrorFailureCancelComponent = ({intl, hasMultipleFailure, children }: AlertErrorFailureCancelProps) => {
  const ALERT_CANCEL_PAYMENT_FAILURE = hasMultipleFailure ? intl.formatMessage({id: "ALERT_CANCEL_PAYMENT_FAILURES"}) : intl.formatMessage({id: "ALERT_CANCEL_PAYMENT_FAILURE"});
  const TRY_AGAIN = intl.formatMessage({id: "ALERT_CANCEL_TRY_AGAIN"});

  const handleOnClick = () => {
    window.location.reload();
  };

  return (
    <Alert
      variant="error"
      className="payment-block sm:payment-flex payment-border-none payment-relative payment-p-16 sm:payment-p-24 payment-rounded-16"
      iconSize="32"
      id="alert-cancel-confirm-fail">
      <Text elementType="div" className="payment-pl-0 payment-pt-12 sm:payment-pl-16 sm:payment-pt-0 payment-flex-1">
        <Heading level="h2" variant="xs" className="payment-mb-5 payment-font-poppins-Semibold payment-text-darkblue ">
          <strong>
            {ALERT_CANCEL_PAYMENT_FAILURE}
          </strong>
        </Heading>
        <div className="payment-mt-12">
          {children}
        </div>
        <Button 
          onClick={handleOnClick} 
          className="payment-mt-24" 
          variant="solidRed" 
          size="regular">
          {TRY_AGAIN}
        </Button>
      </Text>
    </Alert>
  );
};

export const AlertErrorFailureCancel = injectIntl(AlertErrorFailureCancelComponent);
