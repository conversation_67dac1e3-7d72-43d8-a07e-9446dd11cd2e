import React, {useRef, useState} from "react";
import { But<PERSON>, HeadingStep, Icon, Text } from "@virgin/virgin-react-ui-library";
import { FormattedMessage, injectIntl } from "react-intl";
import { IStoreState, State } from "../../store/Store";
import { connect } from "react-redux";
import { createPaymentAction, createMultiPaymentAction, OmnitureOnCurrentBalance, OmnitureOnLoad } from "../../store/Actions";
import { getBanSpecificTransactionId ,
  AlertErrorForm,
  AlertNotificationList,
  AlertNotificationListItem,
  BillSelected,
  CheckboxCardBill,
  CheckboxCardCurrentBalance,
  CurrentBalancedSelected
  ,
  AccountInputValues,
  CurrentSection,
  PaymentItem,
  PaymentItemAccountType,
  PaymentItemAccountTypeName,
  TransactionIdItems
  , 
  getItemAccountTypeName,
  getPaymentItemCardType
} from "myvirgin-preauth-common";

// import { number } from "prop-types";






import { IBankInfoRes, InputBankAccountDetail } from "../../models";
import { hasInteracValueChanged, IsDetailsValid } from "../../utils/Omniture";
import { IRequestStatus } from "../../models/App";

interface SelectBillsComponentProps {
  intl: any;
  paymentItem: PaymentItem[];
  isShow?: boolean;
  onCurrentSteps: (step:any) => void;
  // showCurrentSteps: (number);
  setCurrentSection: (section: CurrentSection) => void;
  currentSection: CurrentSection;
  setCheckedBillItems: Function;
  paymentItems: PaymentItem[];
  accountInputValues: AccountInputValues[];
  setAccountValues: Function;
  transactionIds: TransactionIdItems[];
}

interface MapDispatchToProps {
  createPaymentData: (ban: string, type: boolean,  sub?: string| null) => void;
  createMultiPaymentData: (ban: string, type: boolean,  details: any ,sub?: string| null) => void;
  createOmnitureOnLoad: Function;
}


const SelectBillsComponent = (
  { intl,
    paymentItem,
    isShow, 
    onCurrentSteps,
    setCurrentSection,
    currentSection,
    createPaymentData, 
    setCheckedBillItems,
    paymentItems,
    createMultiPaymentData,
    accountInputValues,
    setAccountValues,
    transactionIds,
    createOmnitureOnLoad
  }:SelectBillsComponentProps & MapDispatchToProps) => {
  // const [selectedBills, setSelectedBills] = useState<string[]>([]);
  const [showError, setShowError] = useState(false);
  const [checkedBan, setCheckedBan] =  useState<string[]>();
  const [isCheckedItems, setIsCheckedItems] = useState<PaymentItem[]>([]);
  const [ tablePaymentItems, setTablePaymentItems] = useState<PaymentItem[]>([]);
  const [ isSessionPresent, setSessionPresent ] = useState(false);
    
  // const [accountValues, setAccountValues] = useState<AccountInputValues[]>([]);
    
  // For local storage reload of checked items
  // });

  // Save state to localStorage whenever it changes
  // useEffect(() => {
  //     localStorage.setItem('checkboxState', JSON.stringify(isChecked));
  // }, [isChecked]);

  // Create a ref array to store refs for each CheckboxCardBill
  const checkboxRefs = useRef<Array<HTMLInputElement | null>>([]);
  // const handleCheckboxRef = (index: number) => (el: HTMLInputElement | null) => {
  //     checkboxRefs.current[index] = el;
  // }

  const checkAllCheckboxes = () => {
    let status = false;
    if (tablePaymentItems && tablePaymentItems.length > 0) {
      const checkedItems = checkboxRefs.current.filter((checkbox) => checkbox?.checked);
      const CheckedBandetailsString = checkedItems
        .map((checkbox) => checkbox?.getAttribute("data-banDetail"))
        .filter((item): item is string => item !== null && item !== undefined);
      setCheckedBan(CheckedBandetailsString);

      if (checkedItems.length <= 0) {
        status = false;
      } else {
        status = true;
      }
            
    }
    return status;
  };


  const OnClickNext = () => {
    if (isCheckedItems !== null && isCheckedItems.length > 0) {
      // isCheckedItems.map((item) => {
      //     createPaymentData(item.BanID, item.AccountType === PaymentItemAccountType.OneBill, item.subscriberId);
      // })
            
      createMultiPaymentData(isCheckedItems[0].BanID, 
        isCheckedItems[0].AccountType === PaymentItemAccountType.OneBill, 
        accountInputValues,
        isCheckedItems[0].subscriberId);
    }
    if(checkAllCheckboxes() === false){
      setShowError(true); 
    }else{
      setShowError(false);
      setCurrentSection(CurrentSection.PaymentMethod);
            
    }
  };

  // Callback to handle Edit icon
  const handleIconLinkClick = (steps:any) => {
    setCurrentSection(CurrentSection.SelectBills);
  };

  React.useEffect(() => {
    const items = sessionStorage.getItem("itemsChecked");
    const storedArray = items && JSON.parse(items);
    if (storedArray !== null && storedArray.length > 0) {
      setSessionPresent(true);
      paymentItems.map((item) => {
        storedArray.map((storedItem: string) => {
          if (!item.IsOnPreauthorizedPayments) {
            item.IsChecked = false;
            // item.isChecked = false;
          }

          if (item.BanID === storedItem) {
            item.IsChecked = true;
          }
        });
      });

      setTablePaymentItems(paymentItems);
      // make API call with the selected BANs
      const accountValuesArr = getAccountInputValues(paymentItems);
      createMultiPaymentData(paymentItems[0].BanID, 
        paymentItems[0].AccountType === PaymentItemAccountType.OneBill, 
        accountValuesArr,
        paymentItems[0].subscriberId);
    } else {
            
      // let isCheckedOnce = false;
      // paymentItems.map((item, index) => {
      //     if (index === 0) {
                   
      //         item.IsChecked = true;
      //         if (!item.IsOnPreauthorizedPayments) {
      //             isCheckedOnce = true;
      //         }
      //     } else {
                   
      //         if (!item.IsOnPreauthorizedPayments && !isCheckedOnce) {
      //             item.IsChecked = true;
      //             isCheckedOnce = true;
      //         } else {
      //             item.IsChecked = false; 
      //         }
      //     }
      // });
      setTablePaymentItems(paymentItems);
    }
  }, []);

  React.useEffect(() => {
    if (isSessionPresent && tablePaymentItems?.length > 0)
    {
      OnClickNext();
      sessionStorage.removeItem("itemsChecked");
      setSessionPresent(false);
    }
  }, [isSessionPresent]);

  const getCheckboxCCText = (paymentItem: PaymentItem) => {
    if(paymentItem.IsOnPreauthorizedPayments && paymentItem.CreditCardDetails){
      return (
        <>
          <FormattedMessage 
            id="SELECT_BILLS_CC_DESC" 
            values={{ 
              CreditCardType: getPaymentItemCardType(paymentItem.CreditCardDetails.CreditCardType),
              CCFourDigits: paymentItem.CreditCardDetails.CreditCardNumber.slice(-4),
              ExpiryDate: paymentItem.CreditCardDetails.ExpireMonth + "/" + paymentItem.CreditCardDetails.ExpireYear 
            }}
          />
        </>
      );
    }
    else if(paymentItem.IsOnPreauthorizedPayments && paymentItem.BankAccountDetails) {
      return (
        <>
          <FormattedMessage 
            id="SELECT_BILLS_BANK_DESC" 
            values={{ 
              BankName: paymentItem.BankAccountDetails.BankName,
              Code: paymentItem.BankAccountDetails.TransitCode,
              BankMaskedDigits: paymentItem.BankAccountDetails.AccountNumberMaskedDisplayView,                   
            }}
          />
        </>
      );
      // return bankDetails Here 
        
    } else {
      return (
        <>
          <span className="payment-text-gray-4" aria-hidden={true}>
            {intl.formatMessage({id: "ACCOUNT_BALANCE"})}&nbsp;<span className="payment-font-poppins-Semibold payment-text-darkblue"><FormattedMessage id="CHECKBOX_BALANCE"  values={{balance: paymentItem.DueStr}}/></span>
          </span>
          <span className="payment-sr-only">
            <FormattedMessage id="CHECKBOX_BALANCE_SR"  values={{balance: paymentItem.Due}} />
          </span>
        </>
      );
    }
  };

  // For disabling next button for all accounts on registered pre auth NOTe: need to remove once manage flow was develop
  const disabledButtonIsPreauth:boolean =  paymentItem?.filter((x) => x.IsOnPreauthorizedPayments).length === paymentItem.length;
    
  // set to monitor current steps for dynamic tile (Start)
  // if(isShow){ 
  //     let  pageTitle = paymentItem.filter((x) => !x?.IsOnPreauthorizedPayments).length > 1 ? 
  //                     intl.formatMessage({id:"SELECT_BILLS_HEADING"}) : 
  //                     intl.formatMessage({id:"SELECT_BILLS_HEADING_SINGULAR"})
  //     onCurrentSteps(pageTitle);
  // }
  // set to monitor current steps for dynamic tile (END)
  const getAccountInputValues = (accountInputArr: PaymentItem[]) => {
    const transformedData: AccountInputValues[] = accountInputArr.map(item => ({
      accountNumber: item.BanID,
      subNumber: item.subscriberId,
      transactionID: getBanSpecificTransactionId(item.BanID, transactionIds),
      payBalanceAmnt: 0
    }));
    return transformedData;
      
  };

  React.useEffect(() => {
    if (paymentItem.length > 1) {
      setCheckedBillItems(isCheckedItems);
      const transformedData = getAccountInputValues(isCheckedItems);
      setAccountValues(transformedData);
      if (isCheckedItems.length === 0)
      {
        setCheckedBillItems([]);
        setAccountValues([]);
      } 
    }  
  }, [isCheckedItems]);
    
  const accountTypename : PaymentItemAccountTypeName = {
    MyBill: intl.formatMessage({id: "ACCOUNT_TYPENAME_MYBILL"}),
    Mobility: intl.formatMessage({id: "ACCOUNT_TYPENAME_MOBILITY"}),
    OneBill: intl.formatMessage({id: "ACCOUNT_TYPENAME_ONEBILL"}),
    TV: intl.formatMessage({id: "ACCOUNT_TYPENAME_TV"}),
    Internet: intl.formatMessage({id: "ACCOUNT_TYPENAME_INTERNET"}),
    HomePhone: intl.formatMessage({id: "ACCOUNT_TYPENAME_HOMEPHONE"}),
    MobilityAndOneBill: intl.formatMessage({id: "ACCOUNT_TYPENAME_MOBILITY_ONEBILL"}),
    SingleBan: intl.formatMessage({id: "ACCOUNT_TYPENAME_SINGLEBAN"})
  };

  const isShowLabel = (item: PaymentItem): boolean | undefined => {
    if(!item.IsNM1Account && item.AccountType === PaymentItemAccountType.OneBill && !item.IsOnPreauthorizedPayments) {
      return false;
    } else if ((item.Due === 0 || item.Due === undefined) && !item.IsOnPreauthorizedPayments) {
      return false;
    } else {
      return true;
    }        
  };
  React.useEffect(() => {
    const items = sessionStorage.getItem("itemsChecked");
    const storedArray = items && JSON.parse(items);
    if (currentSection === CurrentSection.SelectBills) {
      if (storedArray !== null && storedArray.length > 0) { }
      else {
        if (!showError) {
          createOmnitureOnLoad("BAN_SELECT");
        }
        else {
          createOmnitureOnLoad("BAN_SELECT_ERROR");
        }
      }
    }
  }, [currentSection, showError]);
    

  return (
    <div className={["payment-border-b payment-divide-gray-3", isShow ? "" : "payment-hidden"].join(" ").trim()}>
             
      <div id="checkboxgroup-label" className={["payment-flex payment-flex-col", currentSection === CurrentSection.SelectBills ? "" : "payment-hidden"].join(" ").trim()}>
        <HeadingStep
          autoScrollActiveStep={false}
          disableSrOnlyText={true}
          status="active"
          subtitle=""
          hideSubtitle
          variant="leftAlignNoStep"
          title={paymentItem.filter((x) => !x?.IsOnPreauthorizedPayments).length > 1 ? 
            intl.formatMessage({id: "SELECT_BILLS_HEADING"}) : 
            intl.formatMessage({id: "SELECT_BILLS_HEADING_SINGULAR"})
          }
          // SELECT_BILLS_HEADING_SINGULAR french needs to update once
        />
        <p className="payment-text-gray payment-text-14 payment-mt-5">                
          {intl.formatMessage({id: "SELECT_BILLS_HEADING_DESC"})}
        </p>
      </div>
      {showError && (
        <>
          <div className="payment-mt-30"></div>
          <AlertErrorForm>
            <AlertNotificationList>
              <AlertNotificationListItem 
                label={intl.formatMessage({id: "ALERT_ERROR_SELECT_BILL_INFO"})}
                labelDescription={intl.formatMessage({id: "ALERT_ERROR_SELECT_BILL_DESC"})}
                variant="errorList"
                id="error-alert-2"
              />
            </AlertNotificationList>
          </AlertErrorForm>

              
        </>
      )}
      <div className={["payment-pt-0", currentSection === CurrentSection.SelectBills  ? "" : "payment-hidden" ].join(" ").trim()}>
        <div className="payment-mt-30 payment-flex payment-flex-col sm:payment-flex-row payment-flex-wrap payment-items-stretch" role="list" aria-labelledby="checkboxgroup-label">
            
          {tablePaymentItems && tablePaymentItems.length > 0 && tablePaymentItems.map((item, index) => (
            <div role="listitem" className="payment-basis-0 sm:payment-basis-[47%] md:payment-basis-[35%] lg:payment-basis-[35%] payment-w-full sm:payment-w-auto">
              <CheckboxCardBill 
                className={["payment-group/checkboxcard payment-mr-15 payment-mb-15", item.IsOnPreauthorizedPayments ? "payment-bg-gray-5 payment-border-gray-10" : "" ,].join(" ").trim()}
                id={`checkboxbill-${index}`}
                idIndex={index}
                label={`checkboxBill${index}-label-${index} checkboxBillBalance-${index}-label-${index}`}
                // isChecked={ index == 0 && !item.IsOnPreauthorizedPayments ? true : item.IsOnPreauthorizedPayments}
                isChecked={item.IsChecked}
                // isChecked={} //todo: check on acceptance criteria on check state
                isDisabled={item.IsOnPreauthorizedPayments}
                billType={getItemAccountTypeName(item.AccountType,item.IsNM1Account,accountTypename)}
                billAccountNumber={item.NickName ?? item.BillName}
                // text={} //todo: check what model object to use for this props
                text={getCheckboxCCText(item)}
                priceSettings={!item.IsOnPreauthorizedPayments? {price: item.Due} : undefined}
                ref={(el) => { checkboxRefs.current[index] = el; }}
                // ref={handleCheckboxRef(index)}
                item={item}
                isCheckedItems={isCheckedItems}
                setIsCheckedItems={setIsCheckedItems}
                isShowLabel={isShowLabel(item)}
                paymentItems={tablePaymentItems}/>
            </div>

          ))}

        </div>
        {showError && (
          <Text elementType="div" className="payment-pb-15 payment-flex payment-items-center" id="error-alert-3">
            <Icon className="payment-text-15 payment-text-red payment-mr-10" iconClass="vi_vrui" iconName="vi_small_warning"></Icon>
            <Text elementType="div" className="payment-text-red payment-text-12">{intl.formatMessage({id: "ALERT_ERROR_ONE_SELECT_BILL"})}</Text>
          </Text>
        )}          
        <div className="payment-pt-15 payment-pb-40">
          <Button variant="solidRed" className="vrui-py-13 vrui-px-30" onClick={OnClickNext} disabled={disabledButtonIsPreauth}>{intl.formatMessage({id: "CTA_NEXT"})}</Button>
        </div>
      </div>
            
      <BillSelected 
        isActive={currentSection > CurrentSection.SelectBills} 
        onIconLinkClick={handleIconLinkClick} 
        banDetails={checkedBan || []}
        isCheckedItems={isCheckedItems}
      />
    </div>
  );
};

const mapStateToProps = (state: IStoreState): MapStateToProps => (
  {
    createPayment: state.createPayment
  }
);
interface MapStateToProps {
  createPayment: any;
}

const mapDispatchToProps = (dispatch: any): MapDispatchToProps => ({
  createPaymentData: (ban: string, type: boolean , sub?: string | null) => dispatch(createPaymentAction({ ban, type, sub })),
        
  createMultiPaymentData: (ban: string, type: boolean , details: any, sub?: string | null) => dispatch(createMultiPaymentAction({ ban, type, details,sub })),

  createOmnitureOnLoad: (data: string) => dispatch(OmnitureOnLoad({payload: data})) ,
});

export const SelectBills  = connect(mapStateToProps, mapDispatchToProps)(injectIntl(SelectBillsComponent));

interface CurrentBalanceComponentProps {
  intl: any;
  paymentItem: PaymentItem[];
  checkedBillItems: PaymentItem[];
  checkedCurrentBalanceItems: PaymentItem[];
  setCheckedCurrentBalanceItems: Function;
  isShow?: boolean;
  onCurrentSteps: (step: any) => void;
  setCurrentSection: (section: CurrentSection) => void;
  currentSection: CurrentSection;
  language: "en" | "fr";
  accountInputValues: AccountInputValues[];
  setAccountValues: Function;
  transactionIds: TransactionIdItems[];
  isBankPaymentSelected: boolean;
  setNotOptedBalanceItems: Function;
  createOmnitureOnCurrentBalance: any;
  interacCode: string;
  inputBankValue: InputBankAccountDetail;
  interacBankInfo: IBankInfoRes;
  apiSatusIsFailed: boolean;
  validateMultiOrderFormStatus: IRequestStatus;
  validateMultiOrderPayment: any;

}


const CurrentBalanceComponent = ({ 
  intl, 
  paymentItem,
  checkedBillItems,
  setCheckedCurrentBalanceItems,
  setCurrentSection,
  currentSection,
  language,
  accountInputValues,
  setAccountValues,
  transactionIds,
  isBankPaymentSelected,
  setNotOptedBalanceItems,
  checkedCurrentBalanceItems,
  createOmnitureOnCurrentBalance,
  interacCode,
  inputBankValue,
  interacBankInfo,
  apiSatusIsFailed,
  validateMultiOrderFormStatus,
  validateMultiOrderPayment

}:CurrentBalanceComponentProps) => {
  const [isCheckedBalanceItems, setIsCheckedBalanceItems] = useState<PaymentItem[]>([]);

  const OnClickNext = () => {
    setCurrentSection(CurrentSection.TermsAndCondition);
  };

  //  Callback to handle Edit icon
  const handleIconLinkClick = (steps:any) => {
    setCurrentSection(CurrentSection.CurrentBalance);
  };

  const accountTypename : PaymentItemAccountTypeName = {
    MyBill: intl.formatMessage({id: "ACCOUNT_TYPENAME_MYBILL"}),
    Mobility: intl.formatMessage({id: "ACCOUNT_TYPENAME_MOBILITY"}),
    OneBill: intl.formatMessage({id: "ACCOUNT_TYPENAME_ONEBILL"}),
    TV: intl.formatMessage({id: "ACCOUNT_TYPENAME_TV"}),
    Internet: intl.formatMessage({id: "ACCOUNT_TYPENAME_INTERNET"}),
    HomePhone: intl.formatMessage({id: "ACCOUNT_TYPENAME_HOMEPHONE"}),
    MobilityAndOneBill: intl.formatMessage({id: "ACCOUNT_TYPENAME_MOBILITY_ONEBILL"}),
    SingleBan: intl.formatMessage({id: "ACCOUNT_TYPENAME_SINGLEBAN"})
  };
    
  React.useEffect(() => {
    
    const updatedAccountInputValues: AccountInputValues[] = [...accountInputValues]; // Create a copy of the array

    if (isCheckedBalanceItems.length > 0)
    {
      setCheckedCurrentBalanceItems(isCheckedBalanceItems);

      const transformedData: AccountInputValues[] = updatedAccountInputValues.map(existing => {
        // Find a matching item from isCheckedBalanceItems
        const matchingItem = isCheckedBalanceItems.find(item =>
          item.BanID === existing.accountNumber && 
                  item.subscriberId === existing.subNumber
        );
              
        // If a matching item is found, update the payBalanceAmnt
        if (matchingItem) {
          existing.payBalanceAmnt = matchingItem.Due;
        }else{
          existing.payBalanceAmnt = 0;
        }
              
        // Return the modified (or unmodified) object
        return existing;
      });
      setAccountValues(transformedData);
      setNotOptedBalanceItems(checkedBillItems.filter((item) => item.Due > 0 && item.AccountType !== PaymentItemAccountType.OneBill && !isCheckedBalanceItems.includes(item)));

    }  else {
      setCheckedCurrentBalanceItems([]);
      setAccountValues(updatedAccountInputValues);
      setNotOptedBalanceItems(checkedBillItems.filter((item) => item.Due > 0 && item.AccountType !== PaymentItemAccountType.OneBill));
    }
  }, [isCheckedBalanceItems, checkedBillItems]);

  React.useEffect(() => {
    if(currentSection === CurrentSection.CurrentBalance && !apiSatusIsFailed && (validateMultiOrderFormStatus === IRequestStatus.COMPLETED)){
      const IsCurrentBalance = IsDetailsValid(validateMultiOrderPayment) ? false : true;
      if(IsCurrentBalance)
      {
        if(isBankPaymentSelected)
          createOmnitureOnCurrentBalance(!hasInteracValueChanged(interacBankInfo ,inputBankValue) ? interacCode : "");
        else {
          createOmnitureOnCurrentBalance("");
        }
      }
    }
  },[currentSection , validateMultiOrderFormStatus]);


    
  const PAY_CURRENT_BALANCE_DESC = intl.formatMessage({id: "PAY_CURRENT_BALANCE_DESC"});
  const PAY_CURRENT_BALANCE_DESC_CC = intl.formatMessage({id: "PAY_CURRENT_BALANCE_DESC_CC"});

  // const SR_TEXT_BALANCE  = intl.formatMessage(
  //     {id: "PAY_MY_BALANCE_SR"},
  //     {balance: item.Due }// Passing dynamic values for account
  //   );


  return (
          
    <>
      <div className={[currentSection === CurrentSection.CurrentBalance ? "payment-border-b payment-divider-gray-3 payment-mt-15" : "payment-border-b payment-divider-gray-3", currentSection > CurrentSection.CurrentBalance ? "payment-hidden" :  "" ].join(" ").trim()}>
                
        <div>
          <HeadingStep
            autoScrollActiveStep={false}
            disableSrOnlyText={currentSection === CurrentSection.CurrentBalance? true : false}
            status={currentSection === CurrentSection.CurrentBalance? "active" : "inactive"}
            subtitle=""
            hideSubtitle
            variant="leftAlignNoStep"
            id="pre-auth-pay_curr_bal"
            title={checkedBillItems.filter((x) => !x?.IsOnPreauthorizedPayments).length > 1 ? 
              intl.formatMessage({id: "PAY_CURRENT_BALANCE_HEADING"}) : 
              intl.formatMessage({id: "PAY_CURRENT_BALANCE_HEADING"})
            }
                        
          />
          {currentSection === CurrentSection.CurrentBalance &&
                        <div className="payment-mt-10 payment-flex">
                          <Icon
                            className="payment-text-darkblue payment-text-16 payment-inline-block payment-mt-[2px]"
                            iconClass="vi_vrui"
                            iconName="vi_error_c_tk"
                          />
                          <p 
                            className="payment-text-darkblue payment-text-14 payment-leading-19 payment-ml-10"
                            dangerouslySetInnerHTML={isBankPaymentSelected ? {__html: PAY_CURRENT_BALANCE_DESC} : {__html: PAY_CURRENT_BALANCE_DESC_CC}}
                          />
                        </div>
          }
        </div>
        <div className={[ currentSection === CurrentSection.CurrentBalance ? "" : "payment-hidden" ].join(" ").trim()}>
          <div className="payment-mt-30 payment-flex payment-flex-col sm:payment-flex-row payment-flex-wrap" role="group" aria-labelledby="checkboxgroup-label">
            {checkedBillItems.map((item, index) => (
              item.Due > 0 && item.AccountType !== PaymentItemAccountType.OneBill ? (
                <CheckboxCardCurrentBalance
                  className="sm:payment-px-30 payment-mb-15"
                  id={`checkboxBill-${index}`}
                  idIndex={index}
                  label={`checkboxBalance-${index}-label-${index} checkboxBalance-${index}-label-${index}-info`}
                  isDisabled={item.IsOnPreauthorizedPayments}
                  billType={getItemAccountTypeName(item.AccountType,item.IsNM1Account,accountTypename)}
                  billAccountNumber={item.NickName ?? item.BillName}
                  text={intl.formatMessage({id: "PAY_MY_BALANCE"})}
                  priceSettings={  {price: item.Due, language} }
                  priceSettingsSrText={intl.formatMessage(
                    {id: "PAY_MY_BALANCE_SR"},
                    {balance: item.Due })}
                  currentItem={item}
                  isCheckedBalanceItems={isCheckedBalanceItems}
                  setIsCheckedBalanceItems={setIsCheckedBalanceItems}
                                
                />
              ) : null
            ))}
          </div> 
          <div className="payment-text-gray-4 payment-text-12 payment-my-15">
            <p>{isBankPaymentSelected ? intl.formatMessage({id: "PAY_CURRENT_BALANCE_NOTE_1"}) : intl.formatMessage({id: "PAY_CURRENT_BALANCE_NOTE_1_CC"}) }</p>     
            <p className="payment-mt-5">{intl.formatMessage({id: "PAY_CURRENT_BALANCE_NOTE_2"})}</p>     
          </div>
          <div className="payment-pt-15 payment-pb-40 sm:payment-pb-48">
            <Button variant="solidRed" className="vrui-py-13 vrui-px-30"
              onClick={OnClickNext} >{intl.formatMessage({id: "CTA_NEXT"})}
            </Button>
          </div>
        </div>


                
      </div>

      <CurrentBalancedSelected
        isActive={currentSection > CurrentSection.CurrentBalance} 
        onIconLinkClick={handleIconLinkClick} 
        paymentItem={paymentItem}
        isCheckedBalanceItems={isCheckedBalanceItems}
        checkedBillItems={checkedBillItems}
        isBankPaymentSelected={isBankPaymentSelected}
        currentSection={currentSection}
        language={language}/> 
    </>
  
       
  );
};

const mapStateToPropsForCurrentBalance = (state: State) => ({
        
  interacBankInfo: state.interacBankInfo,
  validateMultiOrderFormStatus: state.validateMultiOrderFormStatus,
  validateMultiOrderPayment: state.validateMultiOrderPayment

});

const OmnDispatchtoProps = (dispatch: React.Dispatch<any>) => ({ 
  createOmnitureOnCurrentBalance: (data?:any) => dispatch(OmnitureOnCurrentBalance({data}))
});

export const CurrentBalance  = connect(mapStateToPropsForCurrentBalance, OmnDispatchtoProps)(injectIntl(CurrentBalanceComponent));

