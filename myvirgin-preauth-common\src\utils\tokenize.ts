import { Observable, Observer } from "rxjs";
import { DTSTokenization } from "../models/DTSTokenization";

export interface IDTSTokenizationPluginResponse {
  cardType: string;
  dtsRefId: string;
  isoCountryName: string;
  statusCode: string;
  token: string;
}

declare class DTSTokenizationPlugin {
  setConsumerID(consumer: string): any;
  setApplicationID(application: string): any;
  setSystemTransactionID(transactionID: string): any;
  setUserID(userId: string): any;
  setPassKey(passKey: string): any;
  setPanElementID(elementId: string): any;
  setTimeout(timeout: number): any;
  setSuccessHandler(onSuccess: Function): any;
  setErrorHandler(onFalure: Function): any;
  tokenize(): any;
}

export default function Tokenize(elId: string, dtsTokenization: DTSTokenization, passKey: string): Observable<IDTSTokenizationPluginResponse> {

  const tp = new DTSTokenizationPlugin();
  const {
    consumerId,
    applicationId,
    systemTransactionID,
    userID,
    timeout
  } = dtsTokenization;

  tp.setUserID(userID);
  tp.setSystemTransactionID(systemTransactionID);
  tp.setApplicationID(applicationId);
  tp.setConsumerID(consumerId);
  tp.setPassKey(passKey);
  tp.setPanElementID(elId);
  tp.setTimeout(timeout);

  return new Observable<IDTSTokenizationPluginResponse>((observer: Observer<IDTSTokenizationPluginResponse>) => {
    tp.setSuccessHandler((response: any, dtsRefId: any) => {
      observer.next(response);
      observer.complete();
    });
    tp.setErrorHandler((error: any) => {
      observer.error(error);
      observer.complete();
    });

    tp.tokenize();
  });
}
