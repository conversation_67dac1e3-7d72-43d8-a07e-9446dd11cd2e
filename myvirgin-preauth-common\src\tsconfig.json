{"extends": "../node_modules/webpack-common/lib/tsconfig/lib.json", "compilerOptions": {"baseUrl": "../node_modules/@types", "declarationDir": "../dist/@types", "allowSyntheticDefaultImports": true, "skipLibCheck": true, "strict": false, "experimentalDecorators": true, "noImplicitAny": false, "target": "es2016", "lib": ["es2016", "dom"], "types": ["node"]}, "exclude": ["../dist", "../node_modules", "../test"], "include": ["./**/*.tsx", "./**/*.ts", "../../sample/PaymentMethod/index.tsx", "../../virgin-preauth-setup/src/views/Form/index.tsx"]}