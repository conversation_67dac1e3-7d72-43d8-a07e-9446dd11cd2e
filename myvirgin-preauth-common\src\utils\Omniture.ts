// s_oPYM:bank, Credit
// s_oCCDT:credit card type
import { PaymentItem } from "../models"; // Ensure this path is correct or update it to the correct path

export const getCardType = (cardtype: string) => {
  if(cardtype === "MASTERCARD") return "MC";
  if(cardtype === "AMEX") return "AX";
  if(cardtype === "VISA") return "VI";
  return cardtype;
};

export const getConfirmationSetUpOmniture = (
  isBankpaymentSelscted: boolean,
  showCurrentBalance: boolean,
  checkedCurrentBalanceItems: PaymentItem[],
  submitMultiOrderPayment: any,
  interacCode: string,
  CreditCardType?: string,
) => {
  
  if (isBankpaymentSelscted) {
    if (showCurrentBalance) {
      if (checkedCurrentBalanceItems.length > 0) {
        return {
          s_oPYM: "Bank payment:optin",
          s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
          s_oILI: interacCode
        };
      } else if (checkedCurrentBalanceItems.length === 0) {
        return {
          s_oPYM: "Bank payment:optout",
          s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
          s_oILI: interacCode
        };
      }
    } else {
      return {
        s_oPYM: "Bank payment",
        s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
        s_oILI: interacCode
      };
    }
  } else if (!isBankpaymentSelscted) {
    if (showCurrentBalance) {
      if (checkedCurrentBalanceItems.length > 0) {
        return {
          s_oPYM: "Credit card:optin",
          s_oCCDT: {CreditCardType},
          s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
          s_oILI: interacCode
        };
      } else if (checkedCurrentBalanceItems.length === 0) {
        return {
          s_oPYM: "Credit card:optout",
          s_oCCDT: {CreditCardType},
          s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
          s_oILI: interacCode
        };
      }
    } else {
      return {
        s_oPYM: "Credit card",
        s_oCCDT: {CreditCardType},
        s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
        s_oILI: interacCode
      };
    }
  }

  return {
    s_oPYM: "",
    s_oCCDT: "",
  };
};

const getItems = (item: PaymentItem, count: number) => {
  let newCount = count;
  if (newCount === 0 && (item.BankAccountDetails || item.CreditCardDetails)) {
    newCount = newCount + 1;
    return item;
  }
  return;
};

const getPreauthBanDetails = (checkedBillItems: PaymentItem[], paymentItem?: PaymentItem[]) => {
  const count = 0;
  let filterItems;

  filterItems = checkedBillItems?.length > 0 && checkedBillItems.filter((item: PaymentItem) => getItems(item, count));

  if (filterItems.length === 0 && paymentItem && paymentItem.length > 0) {
    filterItems = paymentItem.filter((item: PaymentItem) => getItems(item, count));
  }

  return filterItems && filterItems.length > 0 ? filterItems[0] : null;
}; 

export const getConfirmationManageOmniture = (
  isBankpaymentSelscted: boolean,
  checkedBillItems: PaymentItem[],
  submitMultiOrderPayment: any,
  paymentItem?: PaymentItem[],
  CreditCardType?: string,
  interacCode?: string,
) => {

  const billItemsOnPreauth = getPreauthBanDetails(checkedBillItems, paymentItem);

  if (isBankpaymentSelscted) {
    if (billItemsOnPreauth?.BankAccountDetails) {
      return {
        s_oPYM: "Bank payment",
        s_oAPT: "327-2-1",
        s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
        s_oILI: interacCode
      };
    } else if (billItemsOnPreauth?.CreditCardDetails) {
      return {
        s_oPYM: "Bank payment",
        s_oAPT: "331-2-1",
        s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
        s_oILI: interacCode
      };
    }
  } else if (!isBankpaymentSelscted) {
    if (billItemsOnPreauth?.BankAccountDetails) {
      return {
        s_oPYM: "Credit card",
        s_oAPT: "328-2-1",
        s_oCCDT: { CreditCardType },
        s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
      };
    } else if (billItemsOnPreauth?.CreditCardDetails) {
      return {
        s_oPYM: "Credit card",
        s_oAPT: "330-2-1",
        s_oCCDT: { CreditCardType },
        s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
      };
    }
  }

  return {
    s_oPYM: "",
    s_oCCDT: "",
  };
};

export const getPaymentSelectOmniture = (
  isError: boolean,
  isBankChecked: boolean,
  checkedBillItems: PaymentItem[],
  isCanceledPreauth?:boolean,
  InteracCode?: string
) => {
  if(isCanceledPreauth){
    return {
      s_oPYM: "Cancel payment",
      error: "",
      s_oAPT: "332-1-0",
    };
  }
  else{
    if (isError) {
      if (isBankChecked) {
        if (checkedBillItems.length >0 && checkedBillItems[0].BankAccountDetails) {
          return {
            s_oPYM: "Bank payment",
            error: "BANKERROR",
            s_oAPT: "327-2-2",
            s_oILI: InteracCode
          };
        } else if (checkedBillItems.length >0 && checkedBillItems[0].CreditCardDetails) {
          return {
            s_oPYM: "Bank payment",
            error: "BANKERROR",
            s_oAPT: "331-2-2",
            s_oILI: InteracCode
          };
        }
      } else {
        if (checkedBillItems.length >0 && checkedBillItems[0].BankAccountDetails) {
          return {
            s_oPYM: "Credit card",
            error: "CREDITERROR",
            s_oAPT: "328-2-2",
            s_oILI: InteracCode
          };
        } else if (checkedBillItems.length >0 && checkedBillItems[0].CreditCardDetails) {
          return {
            s_oPYM: "Credit card",
            error: "CREDITERROR",
            s_oAPT: "330-2-2",
            s_oILI: InteracCode
          };
        }
      }
    } else {
      if (isBankChecked) {
        if (checkedBillItems.length >0 && checkedBillItems[0].BankAccountDetails) {
          return {
            s_oPYM: "Bank payment",
            error: "",
            s_oAPT: "327-1-0",
            s_oILI: InteracCode
          };
        } else if (checkedBillItems.length >0 && checkedBillItems[0].CreditCardDetails) {
          return {
            s_oPYM: "Bank payment",
            error: "",
            s_oAPT: "331-1-0",
            s_oILI: InteracCode
          };
        }
      } else {
        if (checkedBillItems.length >0 && checkedBillItems[0].BankAccountDetails) {
          return {
            s_oPYM: "Credit card",
            error: "",
            s_oAPT: "328-1-0",
            s_oILI: InteracCode
          };
        } else if (checkedBillItems.length >0 && checkedBillItems[0].CreditCardDetails) {
          return {
            s_oPYM: "Credit card",
            error: "",
            s_oAPT: "330-1-0",
            s_oILI: InteracCode
          };
        }
      }
    }
  }
  return {
    s_oPYM: "",
    error: "",
    s_oAPT: "",
    s_oILI: ""  
  };
};

export const getOmnitureOnSetUpConfirmationFailure = (
  isBankpaymentSelscted: boolean,
  showCurrentBalance: boolean,
  checkedCurrentBalanceItems: PaymentItem[],
  CreditCardType?: string,
) => {

  if (isBankpaymentSelscted) {
    if (showCurrentBalance) {
      if (checkedCurrentBalanceItems.length > 0) {
        return {
          s_oPYM: "Bank payment:optin",
          s_OPID: ""
        };
      } else if (checkedCurrentBalanceItems.length === 0) {
        return {
          s_oPYM: "Bank payment:optout",
          s_OPID: ""
        };
      }
    } else {
      return {
        s_oPYM: "Bank payment",
        s_OPID: ""
      };
    }
  } else if (!isBankpaymentSelscted) {
    if (showCurrentBalance) {
      if (checkedCurrentBalanceItems.length > 0) {
        return {
          s_oPYM: "Credit card:optin",
          s_oCCDT: {CreditCardType},
          s_OPID: ""
        };
      } else if (checkedCurrentBalanceItems.length === 0) {
        return {
          s_oPYM: "Credit card:optout",
          s_oCCDT: {CreditCardType},
          s_OPID: ""
        };
      }
    } else {
      return {
        s_oPYM: "Credit card",
        s_oCCDT: {CreditCardType},
        s_OPID: ""
      };
    }
  }

  return {
    s_oPYM: "",
    s_oCCDT: "",
    s_OPID: ""
  };
};

export const getOmnitureOnManageConfirmationFailure = (
  isBankpaymentSelscted: boolean,
  checkedBillItems: PaymentItem[],
  paymentItem: PaymentItem[],
  CreditCardType?: any
) => {
  const billItemsOnPreauth = getPreauthBanDetails(checkedBillItems, paymentItem);
  if (isBankpaymentSelscted) {
    if (checkedBillItems.length >0 && billItemsOnPreauth?.BankAccountDetails) {
      return {
        s_oPYM: "Bank payment",
        s_oAPT: "327-2-2",
      };
    } else if (checkedBillItems.length >0 && billItemsOnPreauth?.CreditCardDetails) {
      return {
        s_oPYM: "Bank payment",
        s_oAPT: "331-2-2",
      };
    }
  } else if (!isBankpaymentSelscted) {
    if (checkedBillItems.length >0 && billItemsOnPreauth?.BankAccountDetails) {
      return {
        s_oPYM: "Credit card",
        s_oAPT: "328-2-2",
        s_oCCDT: { CreditCardType },
      };
    } else if (checkedBillItems.length >0 && billItemsOnPreauth?.CreditCardDetails) {
      return {
        s_oPYM: "Credit card",
        s_oAPT: "330-2-2",
        s_oCCDT: { CreditCardType },
      };
    }
  }

  return {
    s_oPYM: "",
    s_oAPT: "",
    s_oCCDT: "",
  };

};

