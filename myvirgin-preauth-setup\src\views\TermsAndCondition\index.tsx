import * as React from "react";
import {TermsAndConditions, 
  AccountInputValues, 
  CurrentSection, 
  PaymentItem,
  submitMultiOrderPaymentAction, 
  State , PaymentItemAccountType } from "myvirgin-preauth-common";
import { HeadingStep, Loader } from "@virgin/virgin-react-ui-library";
import { injectIntl } from "react-intl";

import { OmnitureOnApiFailure, OmnitureOnReview } from "../../store/Actions";
import { connect } from "react-redux";


import { IRequestStatus } from "../../models/App";
import { InputBankAccountDetail, IBankInfoRes } from "../../models";
import { hasInteracValueChanged, IsDetailsValid } from "../../utils/Omniture";

export interface TermsAndConditionInt {
  isActive: boolean;
  intl: any;
  onCurrentSteps: (step: any) => void;
  setCurrentSection: (section: CurrentSection) => void;
  currentSection: CurrentSection;
  checkedBillItems: PaymentItem[];
  paymentItem: PaymentItem[];
  submitFormOrder: Function;
  province: string;
  language: string;
  accountInputValues: AccountInputValues[];
  submitMultiOrderPayment: any;
  setOmnitureOnReview: Function;
  isBankSelected: boolean;
  validateMultiOrderFormStatus: IRequestStatus;
  tokenizeAndPropagateFormValuesStatus: IRequestStatus;
  setApiSatusIsFailed: Function;
  userProfileProv?: string;
  setOmnitureOnValidationFailure: Function;
  interacCode: string;
  inputBankValue: InputBankAccountDetail;
  interacBankInfo: IBankInfoRes;
  validateMultiOrderPayment: any;
}

const TermsAndConditionComponent = ({ 
  isActive, 
  intl, 
  onCurrentSteps,
  setCurrentSection,
  currentSection,
  checkedBillItems,
  submitFormOrder,
  paymentItem,
  province,
  language,
  userProfileProv,
  accountInputValues,
  setOmnitureOnReview,
  isBankSelected,
  validateMultiOrderFormStatus,
  tokenizeAndPropagateFormValuesStatus,
  setApiSatusIsFailed,
  setOmnitureOnValidationFailure,
  interacCode,
  interacBankInfo,
  inputBankValue,
  validateMultiOrderPayment
}: TermsAndConditionInt) => {

  const [PADValidateStatus,setPADValidateStatus] = React.useState(IRequestStatus.IDLE);
  const [PACCValidateStatus,setPACCValidateStatus] = React.useState(IRequestStatus.IDLE);
    
  const handleSubmitClick = () => {
    if (paymentItem.length > 1 && checkedBillItems && checkedBillItems.length > 0)
    {
      submitFormOrder(checkedBillItems[0].BanID, checkedBillItems[0].AccountType === PaymentItemAccountType.OneBill, accountInputValues,checkedBillItems[0].subscriberId);
    }
    if (paymentItem && paymentItem.length === 1)
    {
      submitFormOrder(checkedBillItems[0].BanID, checkedBillItems[0].AccountType === PaymentItemAccountType.OneBill, accountInputValues,checkedBillItems[0].subscriberId);            
    }
    setCurrentSection(CurrentSection.Confirmation);
  };
  const handleCancelClick = () => {
    setCurrentSection(CurrentSection.PaymentMethod);
  };
  // set to monitor current steps for dynamic tile (End)
  //     if(isActive){ 
  //     let pageTitle = intl.formatMessage({id:"TERMS_AND_CONDITION_HEADING"})
  //     onCurrentSteps(pageTitle);           
  // }
  // set to monitor current steps for dynamic tile (End)

  const containerRef = React.useRef<HTMLDivElement | null>(null);

  // React.useEffect(() => {
  //     const timer = setTimeout(() => {
  //         if (containerRef.current && isActive && (PADValidateStatus === IRequestStatus.COMPLETED || PACCValidateStatus === IRequestStatus.COMPLETED)) {
  //             const firstFocusableElement = containerRef.current.querySelector('h2');
  //             if (firstFocusableElement) {
  //             firstFocusableElement.scrollIntoView({ behavior: 'smooth' });
  //             (firstFocusableElement as HTMLElement).focus();
  //             }
  //         }
  //     }, 500); 

  //     return () => clearTimeout(timer);  
  // }, [PADValidateStatus,PACCValidateStatus]);

  React.useEffect(() => {
    if (validateMultiOrderFormStatus === IRequestStatus.COMPLETED) {
      const isReview = IsDetailsValid(validateMultiOrderPayment) ? false : true ;
      if (currentSection === CurrentSection.TermsAndCondition && isReview) {
        if (isBankSelected) {
          setOmnitureOnReview(!hasInteracValueChanged(interacBankInfo, inputBankValue) ? interacCode: "");
        }else {
          setOmnitureOnReview("");
        }
      }
      else if (!isReview){
        setOmnitureOnValidationFailure();
        setApiSatusIsFailed(true);
      }
    }
  },[currentSection, validateMultiOrderFormStatus]);

  React.useEffect(()=>{
    if (!isBankSelected){
      if(tokenizeAndPropagateFormValuesStatus !== IRequestStatus.FAILED) {
        setPACCValidateStatus(IRequestStatus.PENDING);
      } else {
        setPACCValidateStatus(tokenizeAndPropagateFormValuesStatus);
      }
    }
  },[tokenizeAndPropagateFormValuesStatus]);

  React.useEffect(()=>{
    if (isBankSelected) {
      setPADValidateStatus(validateMultiOrderFormStatus);
      if (validateMultiOrderFormStatus === IRequestStatus.FAILED) {
        setApiSatusIsFailed(true);
        setOmnitureOnValidationFailure();
      }
    } else {
      setPACCValidateStatus(validateMultiOrderFormStatus);
    }
  },[validateMultiOrderFormStatus]);
     
  React.useEffect(()=>{
    if (PACCValidateStatus === IRequestStatus.FAILED) {
      setApiSatusIsFailed(true);
      setOmnitureOnValidationFailure();
    }
  },[PACCValidateStatus]);

  const LOADER_PAYMENT = intl.formatMessage({ id: "LOADER_PAYMENT" });
  const LOADER_PAYMENT_DESC = intl.formatMessage({ id: "LOADER_PAYMENT_DESC" });

  return (
        
    <>
           
      {((PADValidateStatus === IRequestStatus.PENDING) ||
            (PACCValidateStatus === IRequestStatus.PENDING)) && (() => {
        window.scrollTo({ top: 0, behavior: "auto" });
        return <Loader text={"<div className='vrui-font-bold'>"+LOADER_PAYMENT+"</div><div>"+LOADER_PAYMENT_DESC+"</div>"} />;
      })()}


      <>
        {/* Terms and condition */}
        {/* NOTE: when Terms and condition section is active, remove border (brui-border-b brui-border-gray-4) */}
        {/* and update margin (brui-mb-15 to sm:brui-mb-60) */}
        <div ref={containerRef} className={"payment-mb-[32px]"}>
          <div id="termsAndCondDivID" className={isActive ? "focus-visible:payment-outline-none" : ""}>
            <HeadingStep
              autoScrollActiveStep={false}
              disableSrOnlyText={isActive ? true : false}
              tabIndex={-1}
              className="focus-visible:payment-outline-none"
              status={ isActive ? "active" : "inactive"}
              subtitle=""
              hideSubtitle
              variant="leftAlignNoStep"
              title={(province === "QC" && language === "en") ?  intl.formatMessage({id: "TERMS_AND_CONDITION_HEADING_QC"}) : intl.formatMessage({id: "TERMS_AND_CONDITION_HEADING"})}
            />
          </div>
                        
          {/* Terms and condition content */}
          <div className={["payment-pt-24",isActive ? "" : "payment-hidden"].join(" ").trim()}>
            <TermsAndConditions 
              onSubmitClick={handleSubmitClick}
              onCancelClick={handleCancelClick}
              collapseHeightDynamic={{
                mobile: { height: "262px" },
                tablet: { height: "126px" },
                desktop: { height: "107px" }
              }}
              expandHeightDynamic={{
                mobile: { height: "415px" },
                tablet: { height: "460px" },
                desktop: { height: "460px" }
              }}
              province = {province}
              language = {language}
              userProfileProv = {userProfileProv}
            />
          </div>
        </div>
      </>
    </>

  );
};

const mapStateToProps = (state: State) => ({
  submitMultiOrderPayment: state.submitMultiOrderPayment,
  validateMultiOrderFormStatus: state.validateMultiOrderFormStatus,
  tokenizeAndPropagateFormValuesStatus: state.tokenizeAndPropagateFormValuesStatus,
  interacBankInfo: state.interacBankInfo,
  validateMultiOrderPayment: state.validateMultiOrderPayment
});

const mapDispatchToProps = (dispatch: React.Dispatch<any>) => ({
  submitFormOrder: (ban: string, type: boolean, details: any, sub?: string) => {dispatch(submitMultiOrderPaymentAction({ban, type, details,sub}));},

  setOmnitureOnReview: (data: any) => {dispatch(OmnitureOnReview({data}));},
  setOmnitureOnValidationFailure: (data? : any) => dispatch(OmnitureOnApiFailure({data})),
});


export const TermsAndCondition = connect(mapStateToProps, mapDispatchToProps)(injectIntl(TermsAndConditionComponent));
