import { CreditCardType, PaymentItemAccountType, CreditCardDetails, PaymentItemAccountTypeName } from "../models";


export function getPaymentItemAccountType(accountType: PaymentItemAccountType, isNM1: boolean): string {
  if (accountType === PaymentItemAccountType.Mobility) {
    if (isNM1){
      return "My Bill";
    } else {
      return "Mobility";
    }
  } else { 
    switch (accountType) {
      case PaymentItemAccountType.OneBill:
        return "One Bill";
      case PaymentItemAccountType.TV:
        return "TV";
      case PaymentItemAccountType.Internet:
        return "Internet";
      case PaymentItemAccountType.HomePhone:
        return "Home Phone";
      case PaymentItemAccountType.MobilityAndOneBill:
        return "Mobility and One Bill";
      case PaymentItemAccountType.SingleBan:
        return "Single Ban";
      default:
        return "Unknown";
    }
  } 
}

export function getItemAccountTypeName(accountType: PaymentItemAccountType, isNM1: boolean, paymentItemName: PaymentItemAccountTypeName): string {
  if (accountType === PaymentItemAccountType.Mobility) {
    if (isNM1){
      return paymentItemName.MyBill;
    } else {
      return paymentItemName.Mobility;
    }
  } else { 
    switch (accountType) {
      case PaymentItemAccountType.OneBill:
        return paymentItemName.OneBill;
      case PaymentItemAccountType.TV:
        return paymentItemName.TV;
      case PaymentItemAccountType.Internet:
        return paymentItemName.Internet;
      case PaymentItemAccountType.HomePhone:
        return paymentItemName.HomePhone;
      case PaymentItemAccountType.MobilityAndOneBill:
        return paymentItemName.MobilityAndOneBill;
      case PaymentItemAccountType.SingleBan:
        return paymentItemName.SingleBan;
      default:
        return "Unknown";
    }
  } 
}

export function getPaymentItemCardTypeShort(cardType: CreditCardType): string {
  switch (cardType) {
    case CreditCardType.VI:
      return "VI";
    case CreditCardType.MC:
      return "MC";
    case CreditCardType.AX:
      return "AX";
    default:
      return "";
  }
}

export function getPaymentItemCardType(cardType: CreditCardType): string {
  switch (cardType) {
    case CreditCardType.VI:
      return "American Express";
    case CreditCardType.MC:
      return "Mastercard";
    case CreditCardType.AX:
      return "VISA";
    case CreditCardType.DC:
      return "Diners Club";
    default:
      return "Unknown";
  }
}
  
// Card Type
export const getCardTypeShortForm = (number: string) => {
  if (cardPatterns.VISA.test(number)) return "VI";
  if (cardPatterns.MASTERCARD.test(number)) return "MC";
  if (cardPatterns.AMEX.test(number)) return "AX";
  return "default"; // Return default for unknown patterns
};

export const GetCreditCardTypeFromMaskedCreditCardNumber = (number: string) => {
  let re = new RegExp("^4");
  if (number.match(re) != null) {
    return ("VI");
  }
  re = new RegExp("^(34|37)");
  if (number.match(re) != null) {
    return ("AX");
  }
  re = new RegExp("^(5[1-5]|2[2-7])");
  if (number.match(re) != null) {
    return ("MC");
  }
    
  return "";
};

export const GetCreditCardTypeFromCreditCardNumber = (number: string) => {
  let re;
  if (number.length === 13 || number.length === 16) {
    re = new RegExp("^4");
    if (number.match(re) != null) {
      return "VI";
    }
        
    re = new RegExp("^(5[1-5]|2(22[1-9]|2[3-9][0-9]|[3-6][0-9]{2}|7[01][0-9]|720))");
    if (number.match(re) != null && number.length === 16) {
      return "MC";
    }
  }
  else if (number.length === 15) {
    re = new RegExp("^(34|37)");
    if (number.match(re) != null) {
      return "AX";
    }
  }
  return "";
};


export function getCreditCardType(cardType: string): any {
  switch (cardType) {
    case "visa":
      return CreditCardType.VI;
    case "mastercard":
      return CreditCardType.MC;
    case "amex":
      return CreditCardType.AX;
    default:
      return "Unknown";
  }
}

export function maskCCNumer(creditCardInfo: CreditCardDetails): CreditCardDetails {
  // NOTE: uncomment this part when TOKEN is available
  if (creditCardInfo.Token && creditCardInfo.Token.length > 0) {
    if (creditCardInfo.Token.length > 16){
      creditCardInfo.CreditCardNumberMasked = creditCardInfo.CreditCardNumber? creditCardInfo.CreditCardNumber.replace(/\d(?=\d{4})/g, "*") : "";
    }
    else{
      creditCardInfo.CreditCardNumberMasked = creditCardInfo.Token.replace(/\d(?=\d{4})/g, "*");
    }
  }
  else {
    creditCardInfo.CreditCardNumberMasked = "";
  }
  return creditCardInfo;
}


// Creditcard Fuctions HERE
export const cardPatterns = {
  VISA: /^4/,
  MASTERCARD: /^(5[1-5]|2[2-7])/,
  AMEX: /^3[47]/
};


export const validationString = /^(?:[A-Za-zÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇàâäèéêëîïôœùûüÿç'-]{1,}\.?\s)+(?:[A-Za-zÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇàâäèéêëîïôœùûüÿç'-]{1}\.?\s)*[A-Za-zÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇàâäèéêëîïôœùûüÿç'-]{2,}\.?$/;
// regex rules
// -Minimum if 5 Characters (2 first name, 2 lastname and 1 space)
// -web will accept all alternate characters e.g è,é,à,â,ê,ë
// -web will accept hyphen "-" and " ' "

// Card Type
export const getCardType = (number: string) => {
  if (cardPatterns.VISA.test(number)) return "VISA";
  if (cardPatterns.MASTERCARD.test(number)) return "MASTERCARD";
  if (cardPatterns.AMEX.test(number)) return "AMEX";
  return "default"; // Return default for unknown patterns
};

export const dropDownHeight = {
  mobile: { maxHeight: "374px" },
  tablet: { maxHeight: "259px" },
  desktop: { maxHeight: "259px" },
};

export const INTERACTBANKSERVERFAILURE = "INTERNAL_SERVER_ERROR";
export const INTERACTBANKTOKENFAILURE = "OAUTH_TOKEN_EMPTY";


export function formatNumber(num) {
  return num % 1 === 0 ? num.toFixed(0) : num.toFixed(2);
}
