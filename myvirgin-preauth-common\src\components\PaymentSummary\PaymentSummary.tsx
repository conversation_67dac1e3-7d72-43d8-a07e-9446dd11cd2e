import * as React from "react";
import { injectIntl } from "react-intl";
import { SingleRowInformation } from "../SingleRowInformation/SingleRowInformation";
// import { SummaryInformationHeading } from "../SummaryInformationHeading";
import { HeadingStep, Icon, IconLink, Text} from "@virgin/virgin-react-ui-library";
import { CreditCardInputValue, PaymentItem, InputBankAccountDetail, SelectListItem, SubscriberOffersWithBan} from "../../models";
import { getPaymentItemCardType } from "../../utils/PaymentItemUtils";

interface PaymentSummaryComponentProps {
  intl: any;
  className: any;
  paymentItem: PaymentItem[];
  isPreauth: boolean;
  isNewbank: boolean;
  isSingleClickEnable?: boolean;
  inputValue: CreditCardInputValue
  inputBankValue?: InputBankAccountDetail;
  isBankPaymentSelected: boolean;
  showHeading: boolean;
  onEditClick?: () => void;
  bankList: SelectListItem[];
  checkedBillItems: PaymentItem[];
  creditCardAutopayOffers: SubscriberOffersWithBan[];
  debitCardAutopayOffers: SubscriberOffersWithBan[];
  bankitems: any[];
  isConfirmation ?: boolean;
  isOnManagePreauth?: boolean;
  IsAutopayCreditEnabled?: boolean;
}

const PaymentSummaryComponent = ({ intl, className ,paymentItem, isPreauth, inputValue, inputBankValue, isBankPaymentSelected, isNewbank, onEditClick, showHeading,isSingleClickEnable,bankList, debitCardAutopayOffers, creditCardAutopayOffers,
  checkedBillItems,bankitems,isConfirmation, isOnManagePreauth, IsAutopayCreditEnabled}:PaymentSummaryComponentProps) => {
  const checkedBanOffers = () => {
    const filteredOffer: any = [];
    !isBankPaymentSelected ?
      creditCardAutopayOffers && creditCardAutopayOffers?.map((item) => {
        checkedBillItems && checkedBillItems?.map((billItem) => {
          if (item.Ban === billItem.BillName) {
            filteredOffer.push(item);
          }
        });
      }):
      debitCardAutopayOffers && debitCardAutopayOffers?.map((item) =>{
        checkedBillItems && checkedBillItems.map((billItem)=>{
          if(item.Ban === billItem.BillName){
            filteredOffer.push(item);
          }
        });
      });
    return filteredOffer;
  };
  const getTotalOffers = () => bankitems && bankitems.length > 1 ? checkedBanOffers().reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {
       
    const offersCount = ban.AutopayEligibleSubscribers.reduce(
      (count, subscriber) => count + subscriber.autopayOffers.length,
      0
    );
    return total + offersCount;
  }, 0): !isBankPaymentSelected ? creditCardAutopayOffers && creditCardAutopayOffers.reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {
       
    const offersCount = ban.AutopayEligibleSubscribers.reduce(
      (count, subscriber) => count + subscriber.autopayOffers.length,
      0
    );
    return total + offersCount;
  }, 0): debitCardAutopayOffers && debitCardAutopayOffers.reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {
       
    const offersCount = ban.AutopayEligibleSubscribers.reduce(
      (count, subscriber) => count + subscriber.autopayOffers.length,
      0
    );
    return total + offersCount;
  }, 0);
  const debitOffers =      
   {
     label: intl.formatMessage({id: "PAYMENT_METHOD"}),            
     credits: bankitems && bankitems.length > 1 ? checkedBanOffers() : debitCardAutopayOffers
   };
  const CreditOffers =
   {
     label: intl.formatMessage({ id: "PAYMENT_METHOD" }),
     credits: bankitems && bankitems.length > 1 ? checkedBanOffers() : creditCardAutopayOffers
   };
  const offerImpactRemoved = (): boolean => 
    debitOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => credit.offerImpact === "REMOVE")
      )
    ) || false // Return false if no match found
   ;
  const offerImpactGain = (): boolean => debitOffers?.credits?.some((debit: any) =>
    debit.AutopayEligibleSubscribers?.some((item: any) =>
      item?.autopayOffers?.some((credit: any) => (credit.offerImpact === "GAIN" ||credit.offerImpact === "" || credit.offerImpact === null)&&(credit.offerImpact !== "RETAIN")) 
    )
  ) || false;
  const offerImpactReduce = (): boolean => 
    debitOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => credit.offerImpact === "REDUCE")
      )
    ) || false // Return false if no match found
   ;
  const offerImpactIncrease = (): boolean => 
    CreditOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => credit.offerImpact === "INCREASE")
      )
    ) || false // Return false if no match found
   ;
  const filteredItems = paymentItem.filter(
    item => item.IsOnPreauthorizedPayments === true && item.CreditCardDetails
  );
  const CreditCardDetails =  filteredItems.length > 0 ? filteredItems[0].CreditCardDetails : null;

  React.useEffect(()=>{
    
  },[]);
    
  const PAYMENT_METHOD_HEADING = isOnManagePreauth ? intl.formatMessage({id: "UPDATE_PAYMENT_METHOD_HEADING"}) : intl.formatMessage({id: "PAYMENT_SUMMARY_TITLE"});
  const PaymentInformationItems = () => {
    if (isNewbank || isPreauth === false){
      if (isBankPaymentSelected === true) {
        return [
          {
            label: intl.formatMessage({id: "PAYMENT_METHOD"}),
            value: inputBankValue?.PaymentMethod,
          },
          {
            label: intl.formatMessage({id: "ACCOUNT_HOLDER"}),
            value: inputBankValue?.AccountHolder,
          },
          {
            label: intl.formatMessage({id: "BANK_NAME"}),
            value: bankList.filter(x=> x.Value === inputBankValue?.BankName)[0]?.Text,
          },
          {
            label: intl.formatMessage({id: "TRANSIT_NUMER"}),
            value: inputBankValue?.TransitNumber,
          },
          {
            label: intl.formatMessage({id: "ACCOUNT_NUMBER"}),
            value: inputBankValue?.AccountNumber ? `*****${String(inputBankValue.AccountNumber).slice(-3)}`
              : inputBankValue?.AccountNumber,
          },
                
        ];
      } else {
        return [
          {
            label: intl.formatMessage({ id: "CREDIT_CARD_TYPE_LABEL" }),
            value: inputValue.cardType,
          },
          {
            label: intl.formatMessage({ id: "CARD_NUMBER_LABEL" }),
            value:
                    isNewbank && inputValue.cardNumber
                      ? `*******${String(inputValue.cardNumber).slice(-4)}`
                      : inputValue.cardNumber,
          },
          {
            label: intl.formatMessage({ id: "CARD_NAME_LABEL" }),
            value: inputValue.cardName,
          },
          {
            label: intl.formatMessage({ id: "CREDIT_CARD_EXPIRY_LABEL" }),
            value: inputValue.expiryDate,
          },
        ];
      }
            
    } else if (isNewbank === false || isPreauth === true) { // New Creditcard bank Details
      if(isBankPaymentSelected === true) {
        return [
          {
            label: intl.formatMessage({id: "PAYMENT_METHOD"}),
            value: inputBankValue?.PaymentMethod,
          },
          {
            label: intl.formatMessage({id: "ACCOUNT_HOLDER"}),
            value: inputBankValue?.AccountHolder,
          },
          {
            label: intl.formatMessage({id: "BANK_NAME"}),
            value: bankList.filter(x=> x.Value === inputBankValue?.BankName)[0]?.Text,
          },
          {
            label: intl.formatMessage({id: "TRANSIT_NUMER"}),
            value: inputBankValue?.TransitNumber,
          },
          {
            label: intl.formatMessage({id: "ACCOUNT_NUMBER"}),
            value: inputBankValue?.AccountNumber ? `*****${String(inputBankValue.AccountNumber).slice(-3)}`
              : inputBankValue?.AccountNumber,
          },
              
        ];
      } else if(inputValue){
        return [
          {
            label: intl.formatMessage({ id: "CREDIT_CARD_TYPE_LABEL" }),
            value: inputValue.cardType,
          },
          {
            label: intl.formatMessage({ id: "CARD_NUMBER_LABEL" }),
            value:
                  isNewbank && inputValue.cardNumber
                    ? `*******${String(inputValue.cardNumber).slice(-4)}`
                    : inputValue.cardNumber,
          },
          {
            label: intl.formatMessage({ id: "CARD_NAME_LABEL" }),
            value: inputValue.cardName,
          },
          {
            label: intl.formatMessage({ id: "CREDIT_CARD_EXPIRY_LABEL" }),
            value: inputValue.expiryDate,
          },
        ];
      }else if(CreditCardDetails){ // From Preauth Credicard Details
        return [
          {
            label: intl.formatMessage({id: "CREDIT_CARD_TYPE_LABEL"}),
            value: getPaymentItemCardType(CreditCardDetails.CreditCardType)
          },
          {
            label: intl.formatMessage({id: "CARD_NUMBER_LABEL"}),
            value: CreditCardDetails?.CreditCardNumberMasked
          },
          {
            label: intl.formatMessage({id: "CARD_NAME_LABEL"}),
            value: CreditCardDetails?.CardholderName
          },
          {
            label: intl.formatMessage({id: "CREDIT_CARD_EXPIRY_LABEL"}),
            value: CreditCardDetails?.ExpirationDateDisplayViewA
          },
        ];
      }
    }
    return [];

  };
  const offerImpactIncreaseDebit = (): boolean => 
    debitOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => credit.offerImpact === "INCREASE")
      )
    ) || false // Return false if no match found
    ;
  const offerImpactReduceCredit = (): boolean => 
    CreditOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => credit.offerImpact === "REDUCE")
      )
    ) || false // Return false if no match found
    ;
  const gain =      
    {
      
      label: getTotalOffers() >1 ?intl.formatMessage({id: "REVIEW_PAGE_AUTOPAY_CREDITS"}): intl.formatMessage({id: "REVIEW_PAGE_AUTOPAY_CREDIT"})          
      
    };
  const remove =      
    {
      
      label: getTotalOffers() >1 ?intl.formatMessage({id: "LABEL_LOADED_OFFERS_REMOVED_DEBIT_TITLE"}): intl.formatMessage({id: "LABEL_LOADED_OFFER_REMOVED_DEBIT_TITLE"})          
      
    };
  const reduce =      
    {
      
      label: getTotalOffers() >1 ?intl.formatMessage({id: "LABEL_LOADED_OFFERS_REDUCED_DEBIT_TITLE"}): intl.formatMessage({id: "LABEL_LOADED_OFFER_REDUCED_DEBIT_TITLE"})          
      
    };
  const increase =      
    {
      
      label: getTotalOffers() >1 ?intl.formatMessage({id: "LABEL_LOADED_OFFERS_INCREASED_CREDIT_TITLE"}): intl.formatMessage({id: "LABEL_LOADED_OFFER_INCREASED_CREDIT_TITLE"})          
      
    };
   
  const offerImpactGainCredit = (): boolean => 
    CreditOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => (credit.offerImpact === "GAIN" ||credit.offerImpact === "" || credit.offerImpact === null)&&(credit.offerImpact !== "RETAIN")) 
      )
    ) || false // Return false if no match found
    ;
  const offerImpactRemovedCredit = (): boolean => 
    CreditOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => credit.offerImpact === "REMOVE")
      )
    ) || false // Return false if no match found
    ;
  const stacked =      
    {
    
      label: getTotalOffers() >1 ?intl.formatMessage({id: "STACKED_AUTOPAY_CREDITS"}): intl.formatMessage({id: "STACKED_AUTOPAY_CREDIT"})          
      
    };
  const stackedconditionDebit = (offerImpactGain() && offerImpactRemoved())||(offerImpactReduce() && offerImpactRemoved()) ||(offerImpactIncreaseDebit() && offerImpactRemoved()); 
  const stackedcondition = (offerImpactGainCredit() && offerImpactRemovedCredit())||(offerImpactIncrease() && offerImpactRemovedCredit()) || (offerImpactReduceCredit() && offerImpactRemovedCredit());
  return (
    <div className={className} >
      <div className="payment-border-gray-3">
        <div className={showHeading ? "payment-flex payment-items-center payment-justify-between payment-mb-15" : "payment-hidden"} >
          <HeadingStep
            autoScrollActiveStep={false}
            disableSrOnlyText={true}
            status="complete"
            subtitle=""
            hideSubtitle
            variant="leftAlignNoStep"
            title={PAYMENT_METHOD_HEADING}  
            id="pre-auth-payment-summary"
          />
          {/* show IconLink component on Review */}
          <div className="payment-pt-44">
            <IconLink
              icon={<Icon iconClass="vi_vrui" iconName="vi_edit" className="vrui-text-16"></Icon>}
              text={intl.formatMessage({id: "CTA_EDIT"})}
              variant="textBlue"
              size="regular"
              role="button"
              href="javascript:void(0);"
              position="right"
              className={["payment-flex payment-items-center !payment-text-14 !payment-leading-18"].join(" ").trim()}
              aria-label={`${intl.formatMessage({ id: "CTA_EDIT" })} ${intl.formatMessage({ id: "PAYMENT_METHOD_TITLE" })}`}
              onClick={onEditClick} // Pass the click event
            >
            </IconLink>
          </div>
        </div>
        {/* autopaychanges */}
        {!isConfirmation && !isBankPaymentSelected && IsAutopayCreditEnabled && offerImpactReduceCredit() ?
          <Text elementType="div" className="payment-flex payment-items-center payment-mt-5 payment-pb-15">
            <Icon className="payment-text-yellow-1 payment-text-16 payment-mr-5" iconClass="vi_vrui" iconName="vi_small_warning"></Icon>
            <p className="payment-text-darkblue payment-text-14">{reduce.label} </p>
          </Text>
          :null}
				
				
				
        {!isConfirmation && isBankPaymentSelected && IsAutopayCreditEnabled && offerImpactIncreaseDebit() ?
          <Text elementType="div" className="payment-flex payment-items-center payment-mt-5 payment-pb-15">
            <Icon className="payment-text-24 payment-text-darkblue payment-mr-5" iconClass="vi_vrui" iconName="vi_deals"></Icon>
            <p className="payment-text-gray payment-text-14">{increase.label} </p>
          </Text>
          :null}
        {!isConfirmation && isBankPaymentSelected && IsAutopayCreditEnabled && offerImpactGain() ?
          <Text elementType="div" className="payment-flex payment-items-center payment-mt-5 payment-pb-15">
            <Icon className="payment-text-24 payment-text-darkblue payment-mr-5" iconClass="vi_vrui" iconName="vi_deals"></Icon>
            <p className="payment-text-gray payment-text-14">{gain.label} </p>
          </Text>
          :null}
        {!isConfirmation && !isBankPaymentSelected && IsAutopayCreditEnabled && offerImpactGainCredit() ?
          <Text elementType="div" className="payment-flex payment-items-center payment-mt-5 payment-pb-15">
            <Icon className="payment-text-24 payment-text-darkblue payment-mr-5" iconClass="vi_vrui" iconName="vi_deals"></Icon>
            <p className="payment-text-gray payment-text-14">{gain.label} </p>
          </Text>
          :null}
        
        {!isConfirmation && isBankPaymentSelected && IsAutopayCreditEnabled && offerImpactReduce() ?
          <Text elementType="div" className="payment-flex payment-items-center payment-mt-5 payment-pb-15">
            <Icon className="payment-text-yellow-1 payment-text-16 payment-mr-5" iconClass="vi_vrui" iconName="vi_small_warning"></Icon>
            <p className="payment-text-darkblue payment-text-14">{reduce.label} </p>
          </Text>
          :null}
        {!isConfirmation && !isBankPaymentSelected && IsAutopayCreditEnabled && offerImpactIncrease() ?
          <Text elementType="div" className="payment-flex payment-items-center payment-mt-5 payment-pb-15">
            <Icon className="payment-text-24 payment-text-darkblue payment-mr-5" iconClass="vi_vrui" iconName="vi_deals"></Icon>
            <p className="payment-text-gray payment-text-14">{increase.label} </p>
          </Text>
          :null}
        {!isConfirmation && isBankPaymentSelected && IsAutopayCreditEnabled && offerImpactRemoved()  ?
          <Text elementType="div" className="payment-flex payment-items-center payment-mt-5 payment-pb-15">
            <Icon className="payment-text-yellow-1 payment-text-16 payment-mr-5" iconClass="vi_vrui" iconName="vi_small_warning"></Icon>
            <p className="payment-text-darkblue payment-text-14">{remove.label} </p>
          </Text>
          :null}
        {!isConfirmation && !isBankPaymentSelected && IsAutopayCreditEnabled && offerImpactRemovedCredit() ?
          <Text elementType="div" className="payment-flex payment-items-center payment-mt-5 payment-pb-15">
            <Icon className="payment-text-yellow-1 payment-text-16 payment-mr-5" iconClass="vi_vrui" iconName="vi_small_warning"></Icon>
            <p className="payment-text-darkblue payment-text-14">{remove.label} </p>
          </Text>
          :null}
        {!isConfirmation && isBankPaymentSelected && IsAutopayCreditEnabled && (offerImpactRemoved()||offerImpactGain()||offerImpactReduce() ||offerImpactIncreaseDebit()) && stackedconditionDebit ?
          <Text elementType="div" className="payment-flex payment-items-center payment-mt-5 payment-pb-15">
            <Icon className="payment-text-yellow-1 payment-text-16 payment-mr-5" iconClass="vi_vrui" iconName="vi_small_warning"></Icon>
            <p className="payment-text-darkblue payment-text-14">{stacked.label} </p>
          </Text>
          :null}
        {!isConfirmation && !isBankPaymentSelected && IsAutopayCreditEnabled && (offerImpactRemovedCredit()||offerImpactGainCredit()||offerImpactIncrease()|| offerImpactReduceCredit()) && stackedcondition ?
          <Text elementType="div" className="payment-flex payment-items-center payment-mt-5 payment-pb-15">
            <Icon className="payment-text-yellow-1 payment-text-16 payment-mr-5" iconClass="vi_vrui" iconName="vi_small_warning"></Icon>
            <p className="payment-text-darkblue payment-text-14">{stacked.label} </p>
          </Text>
          :null}
        {isBankPaymentSelected ?
          <div className="payment-block payment-relative">
            <div className="brui-mt-15">
              {/* NOTE: add role=list when items is more than one */}
              {PaymentInformationItems().map((item, index) => 
                <SingleRowInformation 
                  className=""
                  label={item.label} 
                  value={item.value}
                  data-index={index} 
                  srText={item.label === intl.formatMessage({ id: "ACCOUNT_NUMBER"}) ? intl.formatMessage({ id: "BANK_ACCOUNT_SR_TEXT"},{Account: String(item.value).slice(-3).split('').join(' ')}) : item.label === intl.formatMessage({ id: "TRANSIT_NUMER"}) ? item.value?.split('').join(' ') : "" }
                  needSRText={item.label === intl.formatMessage({ id: "ACCOUNT_NUMBER"}) ? true : item.label === intl.formatMessage({ id: "TRANSIT_NUMER"}) ? true : false}
                />)}
            </div>
          </div>
          :
          <div className="payment-block payment-relative">
            <div className="brui-mt-15">
              {/* NOTE: add role=list when items is more than one */}
              {PaymentInformationItems().map((item, index) => 
                <SingleRowInformation 
                  className=""
                  label={item.label} 
                  value={
                    item.label === intl.formatMessage({ id: "CARD_NUMBER_LABEL"}) ? `*******${String(item.value).slice(-4)}` 
                      : item.value
                  }
                  data-index={index} 
                  srText={item.label === intl.formatMessage({ id: "CARD_NUMBER_LABEL"}) ? intl.formatMessage({ id: "CREDIT_CARD_SR_TEXT"},{Account: String(item.value).slice(-4)}) : ""}
                  needSRText={item.label === intl.formatMessage({ id: "CARD_NUMBER_LABEL"}) ? true : false}
                />)}
            </div>
          </div>
        }
      </div>
                
    </div>
  );
};


export const PaymentSummary  = injectIntl(PaymentSummaryComponent);



