/** @type {import('tailwindcss').Config} */
module.exports = {
 content: ["./src/utils/**/*.tsx", "./src/views/**/*.tsx", "./src/**/*.tsx", "./src/default-messages.json","./src/components/**/*.tsx",
  "../virgin-preauth-setup/src/**/*.tsx",  "../virgin-preauth-setup/src/views/**/*.tsx",
  "../virgin-preauth-manage/src/**/*.tsx",  "../virgin-preauth-manage/src/views/**/*.tsx"
 ],
 safelist: [
  "vrui-w-1/2",
  "vrui-px-32",
  "sm:vrui-not-sr-only",
  "vrui-max-w-[340px]",
  "sm:vrui-pl-36",
  "vrui-w-[16px]",
  "vrui-animate-spin",
  "vrui-top-14",
  "vrui-right-16",
  "vrui-leading-20",
  "!vrui-leading-18",
  "*:vrui-pl-8",
  "vrui-basis-auto",
  "vrui-grow",
  "sm:vrui-ml-auto",
],
theme: {
  fontFamily: {
    "poppins-Regular": ["Poppins"],
    "poppins-Semibold": ["Poppins-SemiBold"],
    sans: ["Helvetica", "Arial", "sans-serif"],
  },
  container: {
    center: true,
  },
  screens: {
    sm: "768px",
    md: "992px",
    lg: "1240px",
  },
  extend: {
    spacing: {
      0: "0px",
      1: "1px",
      3: "3px",
      4: "4px",
      5: "5px",
      6: "6px",
      7: "7px",
      8: "8px",
      10: "10px",
      11: "11px",
      12: "12px",
      13: "13px",
      14: "14px",
      15: "15px",
      16: "16px",
      20: "20px",
      24: "24px",
      28: "28px",
      30: "30px",
      32: "32px",
      36: "36px",
      40: "40px",
      44: "44px",
      48: "48px",
      50: "50px",
      64: "64px",
      80: "80px",
    },
    fontSize: {
      12: "12px",
      14: "14px",
      15: "15px",
      16: "16px",
      18: "18px",
      20: "20px",
      22: "22px",
      24: "24px",
      26: "26px",
      28: "28px",
      30: "30px",
      32: "32px",
      34: "34px",
      40: "40px",
      64: "64px",
    },
    lineHeight: ({ theme }) => ({
      0: "0px",
      1: "1px",
      14: "14px",
      17: "17px",
      18: "18px",
      19: "19px",
      20: "20px",
      25: "25px",
      28: "28px",
      36: "36px",
      38: "38px",
      46: "46px",
      ...theme("fontSize"),
    }),
    letterSpacing: {
      0.3: "0.3px",
      0.35: "0.35px",
      0.4: "0.4px",
      0.45: "0.45px",
      0.5: "0.5px",
      0.6: "0.6px",
      0.7: "0.7px",
      0.75: "0.75px",
      1: "1px",
      120: "120%",
    },
    colors: {
      white: {
        DEFAULT: "#fff",
      },
      black: {
        DEFAULT: "#111",
        1: "#000",
      },
      gray: {
        DEFAULT: "#555",
        1: "#E5E8F0",
        2: "#686D7C",
        3: "#CDCFD5",
        4: "#444D63",
        5: "#F3F2F0",
        6: "#E6E5E5",
        7: "#858a98",
        8: "#e1e1e1",
        9: "#858A99",
        10: "#BABEC2",
      },
      green: {
        DEFAULT: "#00AA30",
      },
      red: {
        DEFAULT: "#E10A0A",
        1: "#A40000",
        2: "#D71828",
        3: "#bd2025",
      },
      darkblue: "#131c35",
      blue: {
        DEFAULT: "#4E4AE4",
        1: "#2724A3",
        2: "#131c35",
      },
      pink: {
        DEFAULT: "#FFE1E3",
      },
      yellow: {
        DEFAULT: "#FAF5ED",
        1: "#B4781D",
        2: "#C27F1F",
      },
      transparent: {
        1: "rgba(19, 28, 53, 0.08)",
      },
    },
    borderWidth: {
      1: "1px",
    },
    borderRadius: {
      2: "2px",
      4: "4px",
      6: "6px",
      8: "8px",
      12: "12px",
      16: "16px",
      24: "24px",
    },
    outlineOffset: {
      3: "3px",
    },
    keyframes: {
      "icons-flipper": {
        "0%": { transform: "scale(0.8, 0.8)", opacity: 0 },
        "12%": { transform: "scale(1, 1)", opacity: 1 },
        "88%": { transform: "scale(1, 1)", opacity: 1 },
        "100%": { transform: "scale(0.8, 0.8)", opacity: 0 },
      },
    },
    animation: {
      "icons-flipper": "icons-flipper 0.5s ease-in-out infinite",
    },
    boxShadow: {
      "2sm": "0 8px 20px 0 #131C3533;",
    },
    transitionProperty: {
      height: "height",
    },
    textUnderlineOffset: {
      3: "3px",
    },
  },
},
  plugins: [],
  prefix: "payment-",
};
