/* 

Configure VSCode to ignore the unknown at rule @tailwind warning by adding a custom setting in your workspace or user settings. 
You can do this by opening the settings.json file and adding the following line:

{
  "css.lint.unknownAtRules": "ignore"
} 

*/


@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html,
  :host {
    font-family: Helvetica, Arial, sans-serif;
  }
}

@layer components {
  .input_message_container {
    @apply payment-flex payment-flex-col;
  }

  .input_message_container + div {
    @apply payment-flex payment-items-center;
  }

  .input_error_message_container {
    @apply payment-flex payment-items-center;
  }

  .input_error_message_container {
    @apply payment-flex payment-items-center payment-mt-10;
  }

  .payment-chekcbox-disabled input[type=checkbox]:disabled + .vrui-absolute.vrui-top-40.vrui-left-15 .bi_arrow_chekcbox.bi_brui {
    @apply payment-block payment-text-gray-1;
  }

  .scrollbar {
    @apply !payment-overflow-y-auto !payment-overflow-x-auto payment-transition-height payment-duration-500 payment-ease-in-out;
  }

  .payment-scrollbar::-webkit-scrollbar {
    @apply payment-w-[6px] payment-h-[6px];
  }

  .payment-scrollbar::-webkit-scrollbar-thumb {
    @apply payment-h-40 payment-rounded-[3px] payment-bg-blue;
  }

  .payment-scrollbar::-webkit-scrollbar-track {
    @apply payment-rounded-[3px] payment-bg-gray-8;
  }

  .scrollbar-2::-webkit-scrollbar {
    @apply payment-w-3 payment-pr-4;
  }

  .scrollbar-2::-webkit-scrollbar-thumb {
    @apply payment-h-40 payment-rounded-[3px] payment-bg-[#131C35];
  }

  .scrollbar-2::-webkit-scrollbar-track {
    @apply payment-rounded-[3px] payment-bg-[#DDDDDD];
  }

  #termsAndCondDivID h2, #ConfirmationDivID h2{
    @apply payment-outline-none;
  }
}

