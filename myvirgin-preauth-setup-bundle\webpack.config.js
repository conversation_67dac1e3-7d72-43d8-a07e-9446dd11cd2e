const { siteTemplate } = require("webpack-common");
const package = require("./package.json");
const path = require("path");
// const HtmlWebpackPlugin = require('html-webpack-plugin');


module.exports = (env) =>
  siteTemplate(
    package,
    {
      site: path.resolve(__dirname, "src"),
      node_modules: path.resolve(__dirname, "node_modules"),
      dist: path.resolve(__dirname, "dist"),
    }, // PATHS
    {
      output: {
        path: path.resolve(__dirname, "../Bundles/", package.domain, package.name),
      },
      mode: env["-p"] === true ? "production" : "none",
      stats: {
        errorDetails: !env["-p"]
      },
    }, 
    // },// override
    {}, // buildOnlyOverride
    {}, // debugOnlyOverride
    {}, // config
  ); 
    
