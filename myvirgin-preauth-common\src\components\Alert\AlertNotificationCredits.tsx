import * as React from "react";
import {Icon, Card} from "@virgin/virgin-react-ui-library";
import { injectIntl } from "react-intl";

const variants = {
  greatNews: {
    className: "vrui-text-15 vrui-text-blue",
    iconClass: "bi_brui",
    iconName: "bi_tag_note-big",
  },
  notifCardWarning: {
    className: "vrui-text-20 vrui-text-yellow",
    iconClass: "bi_brui",
    iconName: "bi_error_bl_bg_cf",
  }
};

export interface NotificationCardProps {
  intl: any,
  hasNotifCard?: boolean,
  children: React.ReactNode,
  isGreatNewsCard?: boolean,
  isNotifCardWarning?: boolean,
  label: React.ReactNode,
  variant: "greatNews" | "notifCardWarning",
}

const NotificationCardComponent = ({intl, hasNotifCard = false, children, label, variant}:NotificationCardProps) => {
  const GREAT_NEWS_NOTE = intl.formatMessage({id: "ALERT_GREAT_NEWS_NOTE"});
  const GREAT_NEWS_NOTE_DESCRIPTION = intl.formatMessage({id: "ALERT_GREAT_NEWS_NOTE_DESC"});
  return (
    <Card variant="gray" radius={true} className={[
      "vrui-flex vrui-flex-col sm:vrui-flex-row vrui-p-15 vrui-gap-15 vrui-rounded-[16px]",
      hasNotifCard? "" : "vrui-hidden"
    ].join(" ").trim()}>
      <div className="vrui-flex vrui-size-20 vrui-items-start payment-pb-15 payment-pr-15">
        <Icon 
          className={["",variants[variant].className].join(" ").trim()}
          iconClass={["",variants[variant].iconClass].join(" ").trim()}
          iconName={["",variants[variant].iconName].join(" ").trim()}>
        </Icon>
      </div>
      <div className="vrui-flex-grow">
        <p className="vrui-text-14 vrui-leading-18 vrui-text-gray vrui-mb-10">
          {label}
        </p>
        {children}
        <div className="vrui-text-12 vrui-text-gray vrui-leading-14">
          <strong>{GREAT_NEWS_NOTE}</strong>{GREAT_NEWS_NOTE_DESCRIPTION}
        </div>
      </div>
    </Card>
  );
};

export const AlertNotificationCredits = injectIntl(NotificationCardComponent);
