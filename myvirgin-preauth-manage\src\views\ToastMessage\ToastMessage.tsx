import React from "react";
import {Mo<PERSON>, <PERSON>dalContent, ModalBody, Icon, Text} from "@virgin/virgin-react-ui-library";
import {injectIntl } from "react-intl";

interface ToastSuccessModalProps {
  isModalOpen: boolean;
  setIsModalOpen: Function;
  intl: any;
}


const ToastSuccessModalComponent = (props: ToastSuccessModalProps) => {
  const { isModalOpen, setIsModalOpen, intl } = props;

  const onToggleModal = (isOpen: boolean) => {
    setIsModalOpen(isOpen);
  };

  const handleCloseClick = () => {
    setIsModalOpen(false);
    window.location.href = "/";
  };

  return (
    <div>
      {isModalOpen && (
        <Modal
          id="cancel-preauth-success-modal"
          aria-labelledby="cancel-preauth-success-modal-title"      
          onEscapeKeyPressed={() => onToggleModal(false)}
        >
          <ModalContent
            useDefaultRadius={true}
            className="payment-rounded-16 payment-max-w-[304px]"
          >
            <ModalBody
              isDefaultPadding={true}

              className="payment-px-15 sm:payment-px-15 payment-py-15"
            > 
              <div className={["payment-flex payment-gap-15 payment-mt-8"].join(" ").trim()}>
                <Icon className="payment-text-green payment-text-16" iconClass="vi_vrui" iconName="vi_check_c_tk"></Icon>
                <div className="payment-flex payment-flex-col payment-text-12 payment-leading-14" aria-hidden="true">
                  <Text elementType="p" className="payment-text-[#131C35] payment-font-normal"> {intl.formatMessage({id: "SUCCESS_TOAST_MESSAGE"})}
                  </Text>
                </div>
                <button onClick={handleCloseClick} type="button" aria-label="Close dialog box" className="payment-flex payment-rounded-2 focus:payment-outline-blue focus:payment-outline focus:payment-outline-2 focus:payment-outline-offset-3 payment-h-12 payment-ml-auto " id="no-name-on-card-close-button">
                  <span className="vi_close vi_vrui vrui-text-16 payment-text-[#131C35]" role="img" aria-hidden="true" aria-label=" "></span><span className="payment-sr-only">
                    {intl.formatMessage({id: "CTA_CLOSE"})}
                  </span>
                </button>
              </div>
            </ModalBody>
          </ModalContent>
                
        </Modal>
      )}
    </div>
  );
};

export const ToastMessage = injectIntl(ToastSuccessModalComponent);
