// @ts-nocheck
import { Injectable, Store } from "bwtk";
import { Observable, of, catchError, map, mergeMap, ofType } from "rxjs";
import { combineEpics, Epic } from "redux-observable";
import {
  IGetRedirectUrlEpicResponse,
  IGetRedirectUrlEpic,
  IInteracBankInfoEpic,
  IInteracBankInfoEpicResponse,
  ICancelPreauthPaymentsEpic,
  ICancelPreauthPaymentsEpicResponse
} from "../models";
import {
  validateOrderPaymentAction,
  validateOrderPaymentActionCompleted,
  validateOrderPaymentActionFailed,
  createPaymentAction,
  createPaymentCompleted,
  createPaymentFailed,
  submitOrderPaymentAction,
  submitOrderPaymentActionCompleted,
  submitOrderPaymentActionFailed,
  tokenizeAndPropagateFormValues,
  getPassKey,
  setPassKey,
  cardTokenizationSuccess,
  cardTokenizationError,
  getRedirectUrl,
  redirectUrlFailure,
  redirectUrlSuccess,
  getInteracBankInfo,
  interacBankInfoSuccess,
  interacBankInfoFailure,
  setIsLoading,
  createMultiPaymentAction,
  createMultiPaymentCompleted,
  createMultiPaymentFailed,
  validateMultiOrderPaymentAction,
  validateMultiOrderPaymentActionCompleted,
  validateMultiOrderPaymentActionFailed,
  submitMultiOrderPaymentAction,
  submitMultiOrderPaymentActionCompleted,
  submitMultiOrderPaymentActionFailed,
  cancelPreauthAction,
  cancelPreauthSuccessAction,
  cancelPreauthFailureAction
} from "./Actions";

import Tokenize, { IDTSTokenizationPluginResponse } from "../utils/tokenize";
import { Client } from "../Client";
import Config from "../Config";

@Injectable
export class EpicRoot {
  constructor(private client: Client, private config: Config) {}

  combineEpics() {
    return combineEpics(
      this.createPaymentEpic,
      this.validateOrderPaymentEpic,
      this.submitOrderPaymentEpic,
      this.createMultiPaymentEpic,
      this.validateMultiOrderPaymentEpic,
      this.submitMultiOrderPaymentEpic,
      this.tokenizeAndPropagateFormValuesEpic,
      this.fetchPassKeyEpic,
      this.getRedirectUrlEpic,
      this.getInteracBankInfoEpic,
      this.cancelPreauthPaymentsEpic,
    );
  }

  private get tokenizeAndPropagateFormValuesEpic(): Epic {
    return (action$, state$) =>
      action$.pipe(
        ofType(tokenizeAndPropagateFormValues.toString()),
        mergeMap((action: any) =>
          Tokenize("card-number", this.config.DTSTokenization, state$.value.passKey).pipe(
            mergeMap((response: IDTSTokenizationPluginResponse) => of(
              getPassKey({ ban: action.payload.ban, sub: action.payload.sub }),
              cardTokenizationSuccess(response.token)
            )),
            catchError((err) => of(
              cardTokenizationError(typeof err === "string" && err.length > 0 ? err : "TOKENIZATIONERROR")
            ))
          )
        ),
        catchError(() => of(cardTokenizationError("TOKENIZATIONERROR")))
      );
  }

  private get fetchPassKeyEpic(): Epic {
    return (action$, state$) =>
      action$.pipe(
        ofType(getPassKey.toString()),
        mergeMap((action: any) =>
          this.client.getPassKeyRepsonse(action).pipe(
            map((response: any) => setPassKey(response?.data?.PassKey)),
            catchError(() => of(cardTokenizationError("TOKENIZATIONERROR")))
          )
        )
      );
  }

  private get createPaymentEpic(): Epic {
    return (action$, state$) =>
      action$.pipe(
        ofType(createPaymentAction.toString()),
        mergeMap(({ payload }: any) =>
          this.client.createOrderFormData(payload?.ban, payload?.type, payload?.sub).pipe(
            map(({ data }: any) => createPaymentCompleted(data)),
            catchError((error: any) => of({ ...createPaymentFailed(error), error: true }))
          )
        )
      );
  }

  private get validateOrderPaymentEpic(): Epic {
    return (action$, state$) =>
      action$.pipe(
        ofType(validateOrderPaymentAction.toString()),
        mergeMap(({ payload }: any) =>
          this.client.validateOrderForm(
            payload?.ban,
            payload?.type,
            payload?.details,
            payload?.isBankPaymentSelected,
            payload?.sub,
            state$.value.cardTokenizationSuccess
          ).pipe(
            map(({ data }: any) => validateOrderPaymentActionCompleted(data)),
            catchError((error: any) => of({ ...validateOrderPaymentActionFailed(error), error: true }))
          )
        )
      );
  }

  private get submitOrderPaymentEpic(): Epic {
    return (action$, state$) =>
      action$.pipe(
        ofType(submitOrderPaymentAction.toString()),
        mergeMap(({ payload }: any) =>
          this.client.submitOrderForm(payload?.ban, payload?.type, payload?.sub).pipe(
            map(({ data }: any) => submitOrderPaymentActionCompleted(data)),
            catchError((error: any) => of({ ...submitOrderPaymentActionFailed(error), error: true }))
          )
        )
      );
  }

  private get createMultiPaymentEpic(): Epic {
    return (action$, state$) =>
      action$.pipe(
        ofType(createMultiPaymentAction.toString()),
        mergeMap(({ payload }: any) =>
          this.client.createMultiOrderFormData(payload?.ban, payload?.type, payload?.details, payload?.sub).pipe(
            map(({ data }: any) => createMultiPaymentCompleted(data)),
            catchError((error: any) => of({ ...createMultiPaymentFailed(error), error: true }))
          )
        )
      );
  }

  private get validateMultiOrderPaymentEpic(): Epic {
    return (action$, state$) =>
      action$.pipe(
        ofType(validateMultiOrderPaymentAction.toString()),
        mergeMap(({ payload }: any) =>
          this.client.validateMultiOrderForm(
            payload?.ban,
            payload?.type,
            payload?.details,
            payload?.accountInputValue,
            payload?.isBankPaymentSelected,
            payload?.sub,
            state$.value.cardTokenizationSuccess
          ).pipe(
            map(({ data }: any) => validateMultiOrderPaymentActionCompleted(data)),
            catchError((error: any) => of({ ...validateMultiOrderPaymentActionFailed(error), error: true }))
          )
        )
      );
  }

  private get submitMultiOrderPaymentEpic(): Epic {
    return (action$, state$) =>
      action$.pipe(
        ofType(submitMultiOrderPaymentAction.toString()),
        mergeMap(({ payload }: any) =>
          this.client.submitMultiOrderForm(payload?.ban, payload?.type, payload?.details, payload?.sub).pipe(
            map(({ data }: any) => {
              if (data.length > 0) {
                const mutliOrderAllSuccess = data.find((currentElement) => currentElement.OrderFormStatus === 'Confirmation');
                if (mutliOrderAllSuccess) {
                  return submitMultiOrderPaymentActionCompleted(data);
                }
              }
              return submitMultiOrderPaymentActionFailed({ error: true });
            }),
            catchError((error: any) => of({ ...submitMultiOrderPaymentActionFailed(error), error: true }))
          )
        )
      );
  }

  private get getRedirectUrlEpic(): Epic {
    return (action$, state$) =>
      action$.pipe(
        ofType(getRedirectUrl.toString()),
        mergeMap((action: any) =>
          this.client.getRedirectUrl().pipe(
            map(({ data }: IGetRedirectUrlEpicResponse) => redirectUrlSuccess(data)),
            catchError((error: any) => of({ ...redirectUrlFailure(error), error: true }))
          )
        )
      );
  }

  private get getInteracBankInfoEpic(): Epic {
    return (action$, state$) =>
      action$.pipe(
        ofType(getInteracBankInfo.toString()),
        mergeMap(({ payload }: any) => {
          state$.value.dispatch(setIsLoading(true));
          return this.client.getInteracBankInfo(payload?.code).pipe(
            map(({ data }: IInteracBankInfoEpicResponse) => {
              state$.value.dispatch(setIsLoading(false));
              return interacBankInfoSuccess(data);
            }),
            catchError((error: any) => {
              state$.value.dispatch(setIsLoading(false));
              return of({ ...interacBankInfoFailure(error), error: true });
            })
          );
        })
      );
  }

  private get cancelPreauthPaymentsEpic(): Epic {
    return (action$, state$) =>
      action$.pipe(
        ofType(cancelPreauthAction.toString()),
        mergeMap(({ payload }: any) => {
          state$.value.dispatch(setIsLoading(true));
          return this.client.cancelPreauth(payload?.bans).pipe(
            map(({ data }: ICancelPreauthPaymentsEpicResponse) => {
              if (data.length > 0) {
                const successCancelPreauth = data.filter(item => item.success);
                if (successCancelPreauth && successCancelPreauth.length > 0) {
                  state$.value.dispatch(setIsLoading(false));
                  return cancelPreauthSuccessAction(data);
                }
              }
              state$.value.dispatch(setIsLoading(false));
              return cancelPreauthFailureAction({ error: true });
            }),
            catchError((error: any) => {
              state$.value.dispatch(setIsLoading(false));
              return of({ ...cancelPreauthFailureAction(error), error: true });
            })
          );
        })
      );
  }
}
