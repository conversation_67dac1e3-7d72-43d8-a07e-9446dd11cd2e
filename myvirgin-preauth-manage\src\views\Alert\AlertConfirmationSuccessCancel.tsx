import * as React from "react";
import { Alert, Heading, Text } from "@virgin/virgin-react-ui-library";
import { injectIntl } from "react-intl";

interface AlertConfirmationSuccessCancelProps {
  intl: any;
}

const AlertConfirmationSuccessCancelComponent = ({intl}: AlertConfirmationSuccessCancelProps) => {

  const ALERT_CANCEL_PAYMENT_SUCCESS = intl.formatMessage({id: "ALERT_CANCEL_PAYMENT_SUCCESS"});

  return (
    <Alert
      variant="success"
      className="payment-block sm:payment-flex sm:payment-items-center payment-border payment-relative payment-p-16 sm:payment-p-24 payment-rounded-16"
      iconSize="32"
      id="alert-cancel-confirm-success">
      <Text elementType="div" className="payment-pl-0 sm:payment-pl-16 payment-pt-16 sm:payment-pt-0">
        <Heading level="h2" variant="xs" className="payment-font-poppins-Regular payment-leading-19">
          <span className="payment-text-14 payment-leading-19" dangerouslySetInnerHTML={{__html: ALERT_CANCEL_PAYMENT_SUCCESS}} />
        </Heading>
      </Text>
    </Alert>
  );
};

export const AlertConfirmationSuccessCancel = injectIntl(AlertConfirmationSuccessCancelComponent);
