import * as React from "react";
import { Text } from "@virgin/virgin-react-ui-library";

export interface MultiBanInformationProps {
  accountinfo: string,
  className?: string,
  role?: string,
  childrole?: string,
  children: React.ReactNode,
  isLabelOnError?: boolean,
}

export const MultiBanInformation = ({ accountinfo, className, role, childrole, children, isLabelOnError }: MultiBanInformationProps) => (
  <div className={className} role={role}>
    <Text elementType="div"
      className={isLabelOnError ? "vrui-font-bold vrui-text-red vrui-text-14 vrui-leading-19 vrui-mb-5" : "vrui-font-bold payment-text-darkblue vrui-text-14 vrui-leading-19 vrui-mb-5"}
    >
      {accountinfo}
    </Text>
    <div role={childrole}>
      {children}
    </div>
  </div>
);

export default MultiBanInformation;
