import * as React from "react";
import {Al<PERSON>, Heading, Link, Icon, Text} from "@virgin/virgin-react-ui-library";
import { 
  FormattedMessage, 
  injectIntl } from "react-intl";
import { AccountInputValues,PaymentItem, SubscriberOffersWithBan } from "../../models";

interface AlertConfirmationSuccessProps {
  intl: any;
  submitMultiOrderPayment: any;
  accountInputValue: AccountInputValues[];
  isBankPayment?: boolean;
  checkedBillItems: PaymentItem[];
  paymentItem: PaymentItem[];
  creditCardAutopayOffers: SubscriberOffersWithBan[];
  debitCardAutopayOffers: SubscriberOffersWithBan[];
  language: "en" | "fr";
}
const AlertConfirmationSuccessComponent =  ({ 
  intl,
  submitMultiOrderPayment, 
  accountInputValue,
  isBankPayment, 
  checkedBillItems,
  language,
  paymentItem,
  creditCardAutopayOffers,
  debitCardAutopayOffers
}:AlertConfirmationSuccessProps) => {
  const AlertConfirmation = intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_MESSAGE_V3"});
  //    const OTPSuccessMessage = isBankPayment 
  //    ? language === "fr" ? "ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD_FR" : "ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD"
  //    : "ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PACC";
  const OTPSuccessMessage = isBankPayment
    ? (language === "fr" 
      ? "ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD_FR" 
      : "ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD")
    : "ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PACC";

  // autopay changes

  // autopay changes
  const checkeddebitOffers = () => {
    const filteredOffer: any = [];
    debitCardAutopayOffers && debitCardAutopayOffers?.map((item) =>{
      checkedBillItems && checkedBillItems.map((billItem)=>{
        if(item.Ban === billItem.BillName){
          filteredOffer.push(item);
        }
      });
    });
   
    return filteredOffer;   
  };

  const checkedcreditOffers = () => {
    const filteredOffer: any = [];
    creditCardAutopayOffers && creditCardAutopayOffers?.map((item) =>{
      checkedBillItems && checkedBillItems.map((billItem)=>{
        if(item.Ban === billItem.BillName){
          filteredOffer.push(item);
        }
      });
    });
   
    return filteredOffer;   
  };
  const autopayOffers =      
{
  label: intl.formatMessage({id: "PAYMENT_METHOD"}),            
  debits: isBankPayment ? paymentItem && paymentItem.length > 1 ? checkeddebitOffers() : debitCardAutopayOffers : null,
  credits: !isBankPayment ? paymentItem && paymentItem.length > 1 ? checkedcreditOffers() : creditCardAutopayOffers : null,
};
  const offerImpactRemovedCredit = (): boolean => {
    if (autopayOffers.debits){
      // Bank payment
      return autopayOffers?.debits?.some((debit: any) =>
        debit.AutopayEligibleSubscribers?.some((item: any) =>
          item?.autopayOffers?.some((credit: any) => credit.offerImpact === "REMOVE")
        )
      ) || false;
    }else{
      // Creadit Payment
      return autopayOffers?.credits?.some((debit: any) =>
        debit.AutopayEligibleSubscribers?.some((item: any) =>
          item?.autopayOffers?.some((credit: any) => credit.offerImpact === "REMOVE")
        )
      ) || false;
    }
    // Return false if no match found
  };
  const offerImpactIncrease = (): boolean => {
    if (autopayOffers.debits){
      // Bank payment
      return autopayOffers?.debits?.some((debit: any) =>
        debit.AutopayEligibleSubscribers?.some((item: any) =>
          item?.autopayOffers?.some((credit: any) => credit.offerImpact === "INCREASE")
        )
      ) || false;
    }else{
      // Creadit Payment
      return autopayOffers?.credits?.some((debit: any) =>
        debit.AutopayEligibleSubscribers?.some((item: any) =>
          item?.autopayOffers?.some((credit: any) => credit.offerImpact === "INCREASE")
        )
      ) || false;
    }
    // Return false if no match found
  };
  const offerImpactReduce = (): boolean => {
    if (autopayOffers.debits){
      // Bank payment
      return autopayOffers?.debits?.some((debit: any) =>
        debit.AutopayEligibleSubscribers?.some((item: any) =>
          item?.autopayOffers?.some((credit: any) => credit.offerImpact === "REDUCE")
        )
      ) || false;
    }else{
      // Creadit Payment
      return autopayOffers?.credits?.some((debit: any) =>
        debit.AutopayEligibleSubscribers?.some((item: any) =>
          item?.autopayOffers?.some((credit: any) => credit.offerImpact === "REDUCE")
        )
      ) || false;
    }
    // Return false if no match found
  };
  const offerImpactGain = (): boolean => {
    if (autopayOffers.debits){
      // Bank payment
      return autopayOffers?.debits?.some((debit: any) =>
        debit.AutopayEligibleSubscribers?.some((item: any) =>
          item?.autopayOffers?.some((credit: any) => credit.offerImpact === "GAIN")
        )
      ) || false;
    }else{
      // Creadit Payment
      return autopayOffers?.credits?.some((debit: any) =>
        debit.AutopayEligibleSubscribers?.some((item: any) =>
          item?.autopayOffers?.some((credit: any) => credit.offerImpact === "GAIN")
        )
      ) || false;
    }
    // Return false if no match found
  };
  const isshowAutopaysuccess = 
{
  show: autopayOffers && autopayOffers.credits && autopayOffers.credits.length > 0 && autopayOffers.credits[0].AutopayEligibleSubscribers &&  autopayOffers.credits[0].AutopayEligibleSubscribers.length > 0 ||
                              autopayOffers && autopayOffers.debits &&  autopayOffers.debits.length > 0 && autopayOffers.debits[0].AutopayEligibleSubscribers &&  autopayOffers.debits[0].AutopayEligibleSubscribers.length >0
};
  return (
    <Alert
      variant="success" 
      className="payment-alert-success payment-border payment-relative sm:payment-py-30 sm:payment-px-30 payment-rounded-16 payment-p-16 payment-px-15 payment-py-30 sm:payment-flex payment-block" 
      iconSize="36"
      id="alert-3"
      role="">
      <Text id ="Confirmation-message" elementType="div" className="payment-pl-0 sm:payment-pl-16 payment-mt-15 sm:payment-mt-0 sm:payment-pt-0">
        <Heading level="h3" variant="xs" className="vrui-text-18 vrui-leading-20 payment-mb-15 payment-font-poppins-Semibold payment-leading-22 vrui-font-poppins-Semibold">
          {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_HEADING"})}
        </Heading>
        <Text elementType="div" role="list" className="payment-text-14 payment-mb-30 payment-text-darkblue payment-ml-5">
          {
            submitMultiOrderPayment.map((item: any, index: any) => {
              // Define the variable to store the filtered account number
              // const accountNumber = accountInputValue
              //     .filter((x) => x.transactionID === item.OrderFormId)
              //     .map((x) => x.accountNumber)[0];
              const accountNumber = accountInputValue.find((x) => x.transactionID === item.OrderFormId)?.accountNumber;

              // const NickName =  checkedBillItems.filter((x) => x.BanID === accountNumber).map((x) => x.NickName)[0];
              const NickName = checkedBillItems.find((x) => x.BanID === accountNumber)?.NickName;
                        


              const displayTxtAccount = (NickName ? NickName : accountNumber);

              const OTPSuccessMessageAccount  = intl.formatMessage(
                { id: OTPSuccessMessage },
                { account: displayTxtAccount} // Passing dynamic values for account
              );

              //   const OTPSuccessMessageAccountRender = OTPSuccessMessageAccount.replace("{account}", '<strong> ${accountNumber} </strong>');
                    
              return (
                <>
                  {item?.otp != null && item?.otp.isSuccess && (
                    <Text elementType="div" role="listitem" className="payment-flex payment-items-start payment-mb-15">
                      <Icon className="payment-text-12 payment-text-darkblue payment-mt-3" iconClass="vi_vrui" iconName="vi_small_check_v2" />
                      <span className="payment-text-14 payment-leading-19 payment-ml-10" dangerouslySetInnerHTML={{ __html: OTPSuccessMessageAccount }}>
                        {/* <FormattedMessage
                                                id={isBankPayment ? "ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD" : "ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PACC" }
                                                values={{
                                                    account: <strong>{accountNumber}</strong>,
                                                }}
                                            /> */}
                                            
                        {/* The current balance for {accountNumber} has been paid. */}
                      </span>
                    </Text>
                  )}
                                
                </>
              );
            })
          }
          <Text elementType="div" role="listitem" className="payment-flex payment-items-start payment-mb-15"> 
            <Icon className="payment-text-12 payment-text-darkblue payment-mt-3 " iconClass="vi_vrui" iconName="vi_small_check_v2" /> 
            <span className="payment-text-14 payment-leading-19 payment-ml-10"><div dangerouslySetInnerHTML={{ __html: AlertConfirmation }}></div></span>
          </Text>
          { (isshowAutopaysuccess.show && offerImpactRemovedCredit() === false && (offerImpactGain() || offerImpactIncrease() || offerImpactReduce())) ?
            <Text elementType="div" role="listitem" className="brui-flex brui-items-start payment-mb-15">
              <Icon className="payment-text-12 payment-text-darkblue payment-mt-3" iconClass="vi_vrui" iconName="vi_small_check_v2" />
              <span className="brui-text-16 brui-leading-20 payment-ml-10">
                {offerImpactIncrease() ? intl.formatMessage({ id: "AUTOPAY_ALERT_INCREASE" }) : intl.formatMessage({ id: "AUTOPAY_ALERT" })}

              </span>
            </Text> : ""
          }
                
        </Text>
        <p className="payment-text-14 payment-mb-5 payment-text-darkblue payment-leading-18">
          {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_NUMBER"})} {submitMultiOrderPayment[0].PaymentConfirmationNumber}
        </p>
        <p className="payment-text-14 payment-text-darkblue payment-leading-18">
          <FormattedMessage 
            id="ALERT_CONFIRMATION_SUCCESS_DESC" 
            values={{ 
              email: <strong>{submitMultiOrderPayment[0].ConfirmationEmailAddress}</strong>,
            }}
          />&nbsp;<Link variant="textBlue" size="small" href="/MyProfile/Details/EditProfile?editField=EMAIL_ADDRESS" className="">{intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_DESC_LINK"})}</Link>.
        </p>      
      </Text>
    </Alert>
  );
};
export const AlertConfirmationSuccess  = injectIntl(AlertConfirmationSuccessComponent);
