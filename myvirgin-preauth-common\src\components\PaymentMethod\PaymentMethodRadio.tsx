import React, { ComponentPropsWithRef, forwardRef } from "react";
import {RadioButton, Heading, Divider} from "@virgin/virgin-react-ui-library";

export interface PaymentMethodProps extends ComponentPropsWithRef<"input"> {
  id: string;
  name: string;
  defaultChecked?: boolean;
  label: string;
  headingLevel?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6";
  children: React.ReactNode;
  ariaDescribe?: string;
}

export const PaymentRadioCard = forwardRef<HTMLInputElement, PaymentMethodProps>(function PaymentRadioCard(
  {
    id,
    name,
    defaultChecked,
    label,
    headingLevel="h4",
    children,
    ariaDescribe,
    onChange
  },
  ref
){
  return (
    <>
      <div className="payment-rounded-16 payment-border payment-border-gray-8 transition-all payment-mb-15">
        <label className="payment-peer/paymentradio payment-flex payment-items-center payment-pt-32 payment-pl-32 payment-pr-32 payment-pb-24 payment-cursor-pointer">
          <RadioButton
            className="payment-flex payment-items-center payment-absolute payment-size-full payment-opacity-0 enabled:payment-cursor-pointer disabled:payment-cursor-default payment-z-10"
            id={id}
            name={name}
            value={label}
            variant="default"
            defaultChecked={defaultChecked}
            aria-describedby={ariaDescribe || undefined}
            ref={ref}
            onChange={onChange}
          >
            <div className="payment-relative payment-flex payment-justify-between">
              <Heading
                level={headingLevel}
                variant="default"
                id={id + "-label"}
                className="!payment-font-poppins-Regular payment-mb-5 payment-text-18 payment-leading-20 payment-mt-3 sm:payment-mr-64"
              >
                {label}
              </Heading>
            </div>
          </RadioButton>
        </label>
        <div className="payment-z-10 payment-relative payment-hidden peer-has-[input[type=radio]:checked]/paymentradio:payment-block payment-px-15 payment-pb-32 sm:payment-px-32">
          <Divider direction="horizontal" width={1} className="payment-mb-24 payment-bg-gray-1" />
          {children}
        </div>
      </div>
    </>
  );
});

export default PaymentRadioCard;
