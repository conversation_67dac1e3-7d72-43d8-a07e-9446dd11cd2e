import * as React from "react";
import { <PERSON><PERSON>, But<PERSON>, Heading, Text } from "@virgin/virgin-react-ui-library";
import { FormattedMessage, injectIntl } from "react-intl";
import AlertNotificationList from "./AlertNotificationList";
import AlertNotificationListItem from "./AlertNotificationListItem";
import { AccountInputValues, PaymentItem, PaymentItemAccountType } from "../../models";

interface AlertErrorOneTimePaymentProps {
  intl: any;
  checkedCurrentBalanceItems: PaymentItem[];
  submitMultiOrderPayment: any;
  accountInputValue: AccountInputValues[];
  language: "en" | "fr";
  notOptedBalanceItems: PaymentItem[];
  checkedBillItems?: any;
  setOmnitureOnOneTimePaymentFailure: Function;
  isManage?: boolean | undefined | "";
}

const AlertErrorOneTimePaymentComponent = ({intl, checkedCurrentBalanceItems, submitMultiOrderPayment, accountInputValue, language, notOptedBalanceItems, checkedBillItems, isManage, setOmnitureOnOneTimePaymentFailure}:AlertErrorOneTimePaymentProps) => {
  const ALERT_ERROR_OTP_ALL_BALANCE = intl.formatMessage({id: "ALERT_ERROR_OTP_ALL_BALANCE"});
  const ALERT_ERROR_OTP_ALL_BALANCE_SINGULAR = intl.formatMessage({id: "ALERT_ERROR_OTP_ALL_BALANCE_SINGULAR"});
  const ALERT_ERROR_HEADING_SOME_BALANCE = intl.formatMessage({id: "ALERT_ERROR_HEADING_SOME_BALANCE"});
  const ALERT_ERROR_OTP_BALANCE_DESC = intl.formatMessage({id: "ALERT_ERROR_OTP_BALANCE_DESC"});
  const ALERT_ERROR_OTP_BALANCE_DESC_SINGULAR = intl.formatMessage({id: "ALERT_ERROR_OTP_BALANCE_DESC_SINGULAR"});
  const ALERT_ERROR_OPTED_NOT_TO_PAY_SINGULAR = intl.formatMessage({id: "ALERT_ERROR_OPTED_NOT_TO_PAY_SINGULAR"});
  const ALERT_ERROR_OPTED_NOT_TO_PAY_PLURAL = intl.formatMessage({id: "ALERT_ERROR_OPTED_NOT_TO_PAY_PLURAL"});

  const OrderItemsFailed = submitMultiOrderPayment.filter((item:any) => item?.otp && !item?.otp.isSuccess);
  const failedOrderFormIdList = OrderItemsFailed.map((item:any) => item?.OrderFormId);
  const failedOrderItems = accountInputValue
    .filter((x) => failedOrderFormIdList.includes(x.transactionID))
    .map((x) => x.accountNumber);
  const failedOtpItems:any = checkedCurrentBalanceItems?.filter((item) => failedOrderItems.includes(item.Ban));
  const checkedBillItemsHasBalance = checkedBillItems?.length > 0 ? checkedBillItems?.filter((x: PaymentItem) => x.Due > 0 && x.AccountType !== PaymentItemAccountType.OneBill && !x.isLastBillOnPreauth) : 0;
  const SelectedOtpItems:any = checkedCurrentBalanceItems;
  const SomeOtpItemsFailed = (SelectedOtpItems.length > failedOtpItems.length) ? true : false;
  const AllOtpItemsFailed = (SelectedOtpItems.length === failedOtpItems.length && OrderItemsFailed.length > 0 && checkedBillItemsHasBalance.length > 1) ? true : false;

  React.useEffect(() => {
    if (!isManage) {
      setTimeout(() => {
        if (AllOtpItemsFailed || SomeOtpItemsFailed) {
          setOmnitureOnOneTimePaymentFailure();
        }
      }, 1000);
    }
  }, [SomeOtpItemsFailed, AllOtpItemsFailed]);
    
  return (
    <Alert 
      variant="error"
      className="payment-block payment-border payment-rounded-16 sm:payment-flex payment-p-16 sm:payment-p-24 vrui-relative"
      iconSize="32"
      screenReaderText={intl.formatMessage({id: "ALERT_ERROR_ICON"})}
      id="alert-otp-fail"
    >
      <Text elementType="div" className="payment-pl-0 payment-pt-15 sm:payment-pl-15 sm:payment-pt-0">
        <Heading level="h3" variant="xs" className="payment-mb-12 payment-text-darkblue">
          {(SelectedOtpItems.length === 1 && notOptedBalanceItems.length === 0) &&
                        <FormattedMessage 
                          id="ALERT_ERROR_OTP_BALANCE" 
                          values={{ 
                            balance: SelectedOtpItems[0].DueStr,
                          }}
                          children={(errorOTPMessage) => <p dangerouslySetInnerHTML={{ __html: errorOTPMessage }} />}
                        />
          }

          {SomeOtpItemsFailed &&
                        <span dangerouslySetInnerHTML={{ __html: ALERT_ERROR_HEADING_SOME_BALANCE}}></span>
          }

          {AllOtpItemsFailed &&
                        (failedOtpItems.length > 1
                          ? <span dangerouslySetInnerHTML={{ __html: ALERT_ERROR_OTP_ALL_BALANCE}}></span>
                          : <span dangerouslySetInnerHTML={{ __html: ALERT_ERROR_OTP_ALL_BALANCE_SINGULAR}}></span>)
          }
        </Heading>
        <Text elementType="div">
          {failedOtpItems.length > 1
            ? <span className="payment-text-14 payment-leading-19 payment-text-darkblue" dangerouslySetInnerHTML={{ __html: ALERT_ERROR_OTP_BALANCE_DESC}}></span> 
            : <span className="payment-text-14 payment-leading-19 payment-text-darkblue" dangerouslySetInnerHTML={{ __html: ALERT_ERROR_OTP_BALANCE_DESC_SINGULAR}}></span>
          }
        </Text>
        {(SomeOtpItemsFailed || AllOtpItemsFailed) &&
                    <div className="!payment-border-none payment-mt-12">
                      <AlertNotificationList 
                        label="" >
                        {failedOtpItems.map((item:any) => (
                          <AlertNotificationListItem
                            label={item.NickName}
                            labelDescription={item.Due}
                            variant="priceList"
                            priceSettings={{language, showZeroDecimalPart: true}}    
                          />
                        ))}
                      </AlertNotificationList>

                      {/* NOTE: this only shows when SOME OTP FAIL and with item/s that opted out  */}
                      {((SomeOtpItemsFailed && notOptedBalanceItems.length > 0) || (AllOtpItemsFailed && notOptedBalanceItems.length > 0)) && (
                        <div className="payment-border-t-gray-4 payment-mt-12">
                          <AlertNotificationList 
                            label={(notOptedBalanceItems.length === 1) ? ALERT_ERROR_OPTED_NOT_TO_PAY_SINGULAR : ALERT_ERROR_OPTED_NOT_TO_PAY_PLURAL}
                          >
                            {notOptedBalanceItems.map((x) => (
                              <AlertNotificationListItem
                                label={x.NickName}
                                labelDescription={x.Due}
                                variant="priceList"
                                priceSettings={{language, showZeroDecimalPart: true}}    
                              />
                            ))}
                          </AlertNotificationList>
                        </div>
                      )}
                    </div>
        }
        <Button 
          className="payment-mt-24" 
          variant="solidRed" 
          size="regular"
          onClick={() => {
            location.href = `${intl.formatMessage({id: "CTA_MAKE_PAYMENT_LINK"})}`;
          }}
        >
          {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_BUTTON_DESC"})}
        </Button>
      </Text>
    </Alert>
  );
};

export const AlertErrorOneTimePayment = injectIntl(AlertErrorOneTimePaymentComponent);
