import React, {useState, useEffect, useRef} from "react";

import { LocalizationState } from "bwtk";
import { IntlProvider } from "react-intl";
import { IStoreState } from "./store/Store";
import { connect } from "react-redux";
import {Container} from "@virgin/virgin-react-ui-library";
// import { LightBoxFindYourTransaction } from "./views/LightBox/LightBoxFindTransaction";
// import { LightBoxSecurityCode } from "./views/LightBox/LightBoxSecurityCode";
// import {AlertErrorFormList} from "./views/Alert/AlertNotifications"
// import { PaymentAlreadyExist } from "./views/PaymentMethod/PaymentAlreadyExist";
// import { PaymentAlreadyExistRadio } from "./views/PaymentMethod/PaymentAlreadyExistRadio";
// import { PaymentInputFormFieldsPaymentAlreadyExist } from "./views/Form/PaymentInputFormFieldsPaymentAlreadyExist";
// import { Confirmation } from "./views/Confirmation";
import { SubscriberOffersWithBan } from "./models";
import { SelectBills
  // CurrentBalance
} from "./views/CheckboxCard";
import {PaymentMethod} from "./views/PaymentMethod";
import {TermsAndCondition} from "./views/TermsAndCondition";
import { IAppOwnProps,IRequestStatus } from "./models/App";
import Config from "./Config";
import defaultBankInputValue, {
  // AccountInputValues,
  Confirmation,
  CurrentSection,
  getBanSpecificTransactionId,
  PaymentItem,
  PageTitleCurrentSection,
  defaultCreditCardInputValue,
  getInteracBankInfo, 
  getRedirectUrl,
  Loader,
  APIFailure,
  widgetStatusAction
} from "myvirgin-preauth-common";
import { ToastMessage } from "./views/ToastMessage";
import { CancelPaymentFailure } from "./views/CancelPaymentFailure/CancelPaymentFailure";




interface IAppState {
  // config: Config | null; // config can be null initially before it is loaded
  error: string | null;
  paymentItem: PaymentItem[];
  currentSteps: any;
  isLoading: boolean;
  bankitems: any[];
  language: "en" | "fr";
  localization: LocalizationState;
  Config: Config;
  redirectUrlAction: Function;
  getInteracBankInfoAction: Function;
  setWidgetstatus: Function;
  creditCardAutopayOffers: SubscriberOffersWithBan[];
  debitCardAutopayOffers: SubscriberOffersWithBan[];
  removedSubscriberOffers: SubscriberOffersWithBan[];
  cancelPreauthStatus: IRequestStatus;
}


export interface IAppDispatchToProps {

}


export interface IAppMergedProps extends IAppOwnProps, IAppState, IAppDispatchToProps {

}

const AppComponent: React.FC<IAppMergedProps> = (props: IAppMergedProps) => {
  const enableSelectBills = props.Config?.getPaymentItem?.length >1 ? true : false;
  const [paymentHeadingStepState, setPaymentHeadingStepState] = useState(enableSelectBills ? "inactive" : "active");
  const [currentSteps, setCurrentSteps] = useState(":");
  const [currentSection, setCurrentSection] = useState(enableSelectBills ? CurrentSection.SelectBills : CurrentSection.PaymentMethod);
  const [creditCardInputValue, setCreditCardInputValue] = useState(defaultCreditCardInputValue);
  const [BankInputValue, setBankInputValue] = useState(defaultBankInputValue);
  const [isBankSelected, setIsBankSelected] = useState(true);
  const [checkedBillItems, setCheckedBillItems] = useState(enableSelectBills === false ? props.Config?.getPaymentItem : []);
  const [accountInputValues, setAccountInputValues] = useState(enableSelectBills === false
    
    
    ? [{
      accountNumber: props.Config?.getPaymentItem[0]?.BanID,
      subNumber: props.Config?.getPaymentItem[0]?.subscriberId, // This can be null or undefined if not provided
      transactionID: getBanSpecificTransactionId(props.Config?.getPaymentItem[0]?.BanID, props.Config?.transactionIdArray),
      payBalanceAmnt: 0,
    }]
    : [],
  );
  const [apiSatusIsFailed, setApiSatusIsFailed] = useState(false);
  const [cancelPreauthSectionClicked, setCancelPreauthSectionClicked] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  // const [isBanCreditPreauth ] = useState(creditCardInputValue && creditCardInputValue.cardNumber ? true : false);
  const [someCancellationFailed, setSomeCancellationFailed] = useState(false);
  const [bansCancellationFailed, setBansCancellationFailed] = useState<PaymentItem[]>([]);
  const [interacCode, setInteracCode] = useState("");
  const {localization, Config, isLoading} = props;
  
  const onCurrentSteps = (step: any) => {
    if (currentSteps !== step) {
      setCurrentSteps(step);
    }
  };

  const getQueryParameter = (param: string) => new URLSearchParams(document.location.search.substring(1)).get(param);

  const removeSessionStorageCheckedItems = () => {
    const items = sessionStorage.getItem("itemsChecked");
    if (items && items.length > 0) {
      sessionStorage.removeItem("itemsChecked");
    }
  };
 
  useEffect(() => {
    props.setWidgetstatus("LOADED");

    const code = getQueryParameter("code");
    if (code && code != null) {
      // setInteracCode(code);
      const newUrl = Config?.currentUrl?.substring(0, Config?.currentUrl?.lastIndexOf("/")+1)+"PreAuthEdit";
      window.history.pushState({ path: newUrl }, '', newUrl);
      props.getInteracBankInfoAction(code);
      if (Config?.getPaymentItem?.length === 1) {
        removeSessionStorageCheckedItems();
      }
    }
    else {
      removeSessionStorageCheckedItems();
      // props.redirectUrlAction({});
    }

    const container = document.getElementById("container");
    const children = document.getElementsByClassName("myvirgin-preauth-manage");
    
    if (container && children.length > 0) {
      Array.from(children).forEach(child => {
        // Ensure we're adding the class to the <span> itself, not its children
        if (child.classList.contains("myvirgin-preauth-manage")) {
          child.setAttribute("role", "presentation");
        }
      });
    }

  }, []);

  useEffect(() => {
    props.redirectUrlAction({});   // call redirecturl code on every load
  }, []);


  const prevStepsRef = useRef(currentSteps);
  

  useEffect(() => {
    prevStepsRef.current = currentSteps;
  });

  useEffect(() => {
    const pageTitle = Config.pagetitle || "";

    if (currentSection === CurrentSection.Confirmation) {
      const pageConfimationTitle = `${PageTitleCurrentSection.Confirmation} | ${pageTitle}`;
      document.title = parseDOMString(pageConfimationTitle) || "";
    } else {
      document.title = parseDOMString(pageTitle) || "";
    }

  }, [currentSteps, currentSection]);

  const isPreauth: boolean = Config?.getPaymentItem?.some(item => item.IsOnPreauthorizedPayments ?? false);
  const IsAutopayCreditEnabled = Config?.IsAutopayCreditEnabled === "ON" ? true : false;

  
  const parseDOMString = (e: string) =>  // Convert Spacial Characters specially for French word.
    new DOMParser().parseFromString(
      e,
      "text/html"
    ).documentElement.textContent;
  

  return (
    <IntlProvider
      locale={localization.locale}
      messages={localization.messages}
    >
      <>
        {isLoading && <Loader />}
        <div className={isLoading ? "payment-hidden" : ""} role="presentation">
          <Container>
            {(!apiSatusIsFailed && !someCancellationFailed) &&
              <>
                {currentSection !== CurrentSection.Confirmation &&
                  <>
                  
                    <SelectBills
                      paymentItem={Config.getPaymentItem}
                      isShow={enableSelectBills}
                      onCurrentSteps={onCurrentSteps}
                      setCurrentSection={setCurrentSection}
                      currentSection={currentSection}
                      setCheckedBillItems={setCheckedBillItems}
                      paymentItems={Config.getPaymentItem}
                      setAccountValues={setAccountInputValues}
                      accountInputValues={accountInputValues}
                      transactionIds={Config.transactionIdArray}
                      managePreauth={Config.selectedUpdatePaymentMethod}
                      isCheckedBan={Config?.isCheckedBan}
                    />

                    <PaymentMethod paymentItem={Config.getPaymentItem}
                      isHeadingStepActive={currentSection === CurrentSection.PaymentMethod ? "active" : "inactive"}
                      isSingleClickEnableForPACC={false}
                      isSingleClickEnableForPAD={false}
                      onCurrentSteps={onCurrentSteps}
                      setHeadingSteps={setPaymentHeadingStepState}
                      paymentHeadingStepState={paymentHeadingStepState}
                      setInputValue={setCreditCardInputValue}
                      inputValue={creditCardInputValue}
                      setInputBankValue={setBankInputValue}
                      inputBankValue={BankInputValue}
                      setIsBankSelected={setIsBankSelected}
                      setCurrentSection={setCurrentSection}
                      currentSection={currentSection}
                      checkedBillItems={checkedBillItems}
                      bankList={Config.getBankList}
                      accountInputValues={accountInputValues}
                      province={Config.province}
                      language={Config.language as "en" | "fr"}
                      isBankSelected={isBankSelected}
                      creditCardAutopayOffers = {Config.creditCardAutopayOffers}
                      debitCardAutopayOffers={Config.debitCardAutopayOffers}
                      removedSubscriberOffers = {Config.removedSubscriberOffers}
                      managePreauth={Config.selectedUpdatePaymentMethod}
                      setCancelPreauthSectionClicked={setCancelPreauthSectionClicked}
                      cancelPreauthSectionClicked={cancelPreauthSectionClicked}
                      setIsModalOpen={setIsModalOpen}
                      setApiSatusIsFailed={setApiSatusIsFailed}
                      isInteractEnabled = {Config.IsInteracEnabled}
                      IsAutopayCreditEnabled={IsAutopayCreditEnabled}
                      setSomeCancellationFailed={setSomeCancellationFailed}
                      setBansCancellationFailed={setBansCancellationFailed}
                      interacCode={interacCode}
                      setInteracCode = {setInteracCode}
                      isMultiban={enableSelectBills}
                    />

                    {!cancelPreauthSectionClicked && 
                    <TermsAndCondition 
                      isActive={currentSection === CurrentSection.TermsAndCondition}  
                      onCurrentSteps={onCurrentSteps}
                      setCurrentSection={setCurrentSection}
                      currentSection={currentSection}
                      checkedBillItems={checkedBillItems}
                      paymentItem={Config.getPaymentItem}
                      province={Config.province}
                      language={Config.language}
                      userProfileProv={Config.userProfileProvince}
                      accountInputValues={accountInputValues}
                      setApiSatusIsFailed={setApiSatusIsFailed}
                      inputValue={creditCardInputValue}
                      isBankSelected={isBankSelected}
                      interacCode={interacCode}
                      inputBankValue={BankInputValue}
                    />
                    }
                  </>  
                }

                {currentSection === CurrentSection.Confirmation &&
                  <Confirmation
                    paymentItem={Config.getPaymentItem} 
                    checkedBillItems={checkedBillItems} 
                    checkedCurrentBalanceItems={[]}
                    showPaymentSummary={true}
                    isNewbank={false} 
                    isPreauth={isPreauth}
                    inputValue={creditCardInputValue} 
                    isShow={currentSection === CurrentSection.Confirmation}
                    inputBankValue = {BankInputValue}
                    isBankPaymentSelected = {isBankSelected}
                    BankList={Config.getBankList}
                    showCurrentBalance={false}
                    language={Config.language as "en" | "fr"}
                    accountInputValues={accountInputValues}
                    currentSection={currentSection}
                    notOptedBalanceItems={[]}
                    IsAutopayCreditEnabled={IsAutopayCreditEnabled}
                    creditCardAutopayOffers = {Config.creditCardAutopayOffers}
                    debitCardAutopayOffers={Config.debitCardAutopayOffers}  bankitems={[]}   
                    setApiSatusIsFailed={setApiSatusIsFailed}
                    apiSatusIsFailed={apiSatusIsFailed}
                    managePreauth={Config?.selectedUpdatePaymentMethod}
                    interacCode={interacCode}      
                  />
                }
                
              </>
            }
            {apiSatusIsFailed &&
              <APIFailure></APIFailure>
            }
            <ToastMessage isModalOpen={isModalOpen} setIsModalOpen={setIsModalOpen}/>

            {(someCancellationFailed && bansCancellationFailed && bansCancellationFailed.length > 0) &&
              <CancelPaymentFailure cancelledBansFailed={bansCancellationFailed} language={Config.language as "en" | "fr"} />
            }

          </Container>
        </div>
      </>
    </IntlProvider >
  );
};


const mapStateToProps = (state: IStoreState, ownProps: IAppOwnProps) => ({
  localization: state.localization,
  Config: ownProps.Config,
  isLoading: state.isLoading,
  cancelPreauthStatus: state.cancelPreauthStatus,
  cancelPreauthPayments: state.cancelPreauthPayments
});

const mapDispatchToProps = (dispatch: React.Dispatch<any>) => ({
  redirectUrlAction: () => { dispatch(getRedirectUrl({})); },
  getInteracBankInfoAction: (code: string) => { dispatch(getInteracBankInfo({ code })); },
  setWidgetstatus: (type: string) => { dispatch(widgetStatusAction({ type })); }
});

export const App = connect<
  IStoreState,
  IAppOwnProps
>(
  mapStateToProps as any,
  mapDispatchToProps as any
)(AppComponent);
