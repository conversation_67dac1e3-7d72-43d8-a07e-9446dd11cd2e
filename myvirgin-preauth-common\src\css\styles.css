/* 

Configure VSCode to ignore the unknown at rule @tailwind warning by adding a custom setting in your workspace or user settings. 
You can do this by opening the settings.json file and adding the following line:

{
  "css.lint.unknownAtRules": "ignore"
} 

*/


@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html,
  :host {
    font-family: Helvetica, Arial, sans-serif;
  }
}

@layer components {
  .input_message_container {
    @apply payment-flex payment-flex-col;
  }

  .input_message_container + div {
    @apply payment-flex payment-items-center;
  }

  .input_error_message_container {
    @apply payment-flex payment-items-center;
  }

  .input_error_message_container {
    @apply payment-flex payment-items-center payment-mt-10;
  }

  .payment-chekcbox-disabled input[type=checkbox]:disabled + .vrui-absolute.vrui-top-40.vrui-left-15 .bi_arrow_chekcbox.bi_brui {
    @apply payment-block payment-text-gray-1;
  }

  .scrollbar {
    @apply !payment-overflow-y-auto !payment-overflow-x-auto payment-transition-height payment-duration-500 payment-ease-in-out;
  }

  .payment-scrollbar::-webkit-scrollbar {
    @apply payment-w-[6px] payment-h-[6px];
  }

  .payment-scrollbar::-webkit-scrollbar-thumb {
    @apply payment-h-40 payment-rounded-[3px] payment-bg-blue;
  }

  .payment-scrollbar::-webkit-scrollbar-track {
    @apply payment-rounded-[3px] payment-bg-gray-8;
  }

  .scrollbar-2::-webkit-scrollbar {
    @apply payment-w-3 payment-pr-4;
  }

  .scrollbar-2::-webkit-scrollbar-thumb {
    @apply payment-h-40 payment-rounded-[3px] payment-bg-[#131C35];
  }

  .scrollbar-2::-webkit-scrollbar-track {
    @apply payment-rounded-[3px] payment-bg-[#DDDDDD];
  }

  #termsAndCondDivID h2, #ConfirmationDivID h2{
    @apply payment-outline-none;
  }

  .payment-chekcbox-disabled input[type=checkbox]:disabled+.vrui-absolute.vrui-top-16.vrui-left-16 span.vi_checked.vi_vrui {
    color: #858a99;
    display: block;
  }
  .payment-chekcbox-disabled.payment-bg-gray-5 {
    border:0 solid #BABEC2;
    box-shadow: 0 0 0 2px #BABEC2;
  }
  .payment-group\/radiocard .vrui-group\/inputradio #radio-1+div.vrui-absolute, 
  .payment-group\/radiocard .vrui-group\/inputradio #radio-2+div.vrui-absolute{
      top : 32px !important;
  }

  .payment-current-balance > div > input[type="checkbox"] ~ div{
    @apply payment-top-[35px] sm:payment-top-32;
  }

  .payment-alert-success a[href].vrui-underline-offset-4 {
    text-underline-offset:1px;
  }

  .payment-select-box.vrui-border-red {
    border-bottom-width: 2px;
  }

  #cancel-preauth-success-modal .vrui-relative.vrui-z-10.vrui-w-full.vrui-flex {
    width: 304px !important;
  }
    #cancel-preauth-success-modal .vrui-flex.vrui-justify-center.vrui-w-full.vrui-min-h-dvh {
      min-height: 60px !important;
      padding-top: 220px;
    }
}

.virgin-preauth-manage div[aria-labelledby="payment-method"] .payment-w-full div[role="list"] div[role="listitem"].payment-list-none:nth-child(2) div,
.virgin-preauth-manage div[aria-labelledby="payment-method"] .payment-w-full div[role="list"] div[role="listitem"].payment-list-none:nth-child(3) div {
  border-top: 1px solid #CDCFD5;
  padding-top: 15px;
  margin-top: -20px;
}

.payment-select-bills > div > input + .vrui-absolute {
  top: 50% !important;
  transform: translateY(-50%);
}

.payment-preauth-confirmation-heading {
  @apply 
  payment-text-left sm:payment-text-center
  payment-leading-20 sm:payment-leading-[inherit] 
  payment-ml-[90px] sm:payment-ml-0;
}

@media (max-width: 767.9px) {
  .payment-preauth-confirmation-heading {
    @apply payment-text-left payment-leading-20 payment-ml-[90px]
  }

  .payment-preauth-confirmation-heading.isFR > .simple-global-nav-text-18 > span {
    @apply payment-text-12 !payment-leading-16
  }
}

