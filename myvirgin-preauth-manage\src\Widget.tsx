import * as React from "react";
// import * as ReactDOM from "react-dom";
import { Provider } from "react-redux";
import { Widget, ViewWidget, Logger, LoggerConfigKeys } from "bwtk";
import Store  from "./store";
import {App} from "./App";
import Config from "./Config";
import {
  AccountInputValues,
  createMultiPaymentAction,
  getBanSpecificTransactionId,
  PaymentItemAccountType,
  getPassKey
} from "myvirgin-preauth-common";
import { Root } from "react-dom/client";

@Widget({ namespace: "Preauth/Manage" })
export default class extends ViewWidget {
  logger: Logger;
  constructor
  (private store: Store, 
    private config: Config, 
    logger: Logger) {
    super();
    this.logger = logger;
  }

  init() {
    this.config.setConfig(LoggerConfigKeys.SeverityLevel, this.config.logLevel);
    // this.store.dispatch(setEnvironmentVariables({ ...this.config.environmentVariables, language: this.config.language }));
    // this.store.dispatch(widgetStatus(WidgetStates.INITIALIZED));
    // this.store.dispatch(fetchPaymentItems({}));
    // this.store.dispatch(fetchPaymentItems({ ...this.config, language: this.config.language}))
    this.store.dispatch(getPassKey({ban: this.config.getPaymentItem[0].BanID, sub: this.config.getPaymentItem[0].subscriberId}));
    
    

    if (this.config.getPaymentItem != null && this.config.getPaymentItem.length === 1) {
      const paymentItem = this.config.getPaymentItem[0];
      const accountInputValue: AccountInputValues[] = [{
        accountNumber: paymentItem.BanID,
        subNumber: paymentItem.subscriberId, // This can be null or undefined if not provided
        transactionID: getBanSpecificTransactionId(paymentItem.BanID, this.config.transactionIdArray),
        payBalanceAmnt: 0,
      }];
      this.store.dispatch(createMultiPaymentAction({ ban: paymentItem.BanID, type: (paymentItem.AccountType === PaymentItemAccountType.OneBill), details: accountInputValue ,sub: paymentItem.subscriberId }));
    }
  }


  destroy() {
    this.store.destroy();
  }

  render(root: Root) {
    const { store, config } = this;
    root.render(
      <Provider {...{ store }}>
        <App Config={config} />
      </Provider>
    );
  }
}

