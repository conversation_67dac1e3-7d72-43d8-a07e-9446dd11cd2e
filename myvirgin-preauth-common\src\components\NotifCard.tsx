import React from "react";
import { Icon, Card, Text} from "@virgin/virgin-react-ui-library";

const variants = {
  greatNews: {
    className: "vrui-text-24 vrui-text-darkblue",
    iconClass: "vi_vrui",
    iconName: "vi_deals",
    
  },

  notifCardWarning: {
    className: "vrui-text-24 payment-text-yellow-1",
    iconClass: "vi_vrui",
    iconName: "vi_error_c_tk",
  },

  notifCardInfoAlert: {
    className: "vrui-text-20 vrui-text-darkblue",
    iconClass: "vi_vrui",
    iconName: "vi_info_notif_small",
  }
};


export interface PaymentMethodProps {
  id?: string;
  hasNotifCard?: boolean,
  children?: React.ReactNode,
  isGreatNewsCard?: boolean,
  isNotifCardWarning?: boolean,
  // isNoteExisting?: boolean,
  label?: React.ReactNode,
  label1?: React.ReactNode,
  label2?: React.ReactNode
  label3?: React.ReactNode,
  simpleLabel?: React.ReactNode,
  variant: "greatNews" | "notifCardWarning" | "notifCardInfoAlert",
}


export const NotifCard = ({hasNotifCard = false, children, label,label1,label2,label3, variant, id}:PaymentMethodProps) => (
  <Card variant="default" radius={true} className={[
    "payment-flex payment-flex-col sm:payment-flex-row payment-p-16 payment-gap-15 payment-rounded-[16px] payment-mb-24 !payment-bg-gray-5",
    hasNotifCard? "" : "payment-hidden"
  ].join(" ").trim()}>
    <div className="payment-flex payment-size-24 payment-items-center payment-justify-center payment-self-start sm:payment-self-start ">
      <Icon 
        className={["",variants[variant].className].join(" ").trim()}
        iconClass={["",variants[variant].iconClass].join(" ").trim()}
        iconName={["",variants[variant].iconName].join(" ").trim()}>
      </Icon>
    </div>
    <div id ={id ?id:"discount-offer"} className="payment-flex-grow">
      <p className="payment-text-14 payment-leading-19 payment-text-darkblue payment-mb-10">
        <span className="payment-font-bold payment-text-darkblue">
          {label}&nbsp;
        </span>
        <span className="payment-text-darkblue">
          {label1}
        </span>
      </p>
      {children}
      <div className="payment-text-12 payment-text-gray-4 payment-leading-14"><strong>{label2}</strong>{label3}</div>
    </div>
  </Card>
);

export const SimpleNotifCard = ({children, simpleLabel}:PaymentMethodProps) => (
  <Text role="alert" className="payment-bg-gray-5 payment-flex payment-flex-col sm:payment-flex-row payment-p-16 payment-gap-15 payment-rounded-[16px]" elementType="div">
    <div className="payment-flex payment-size-24 payment-items-center payment-justify-center">
      <Icon  className="payment-text-24 payment-text-darkblue" iconClass="vi_vrui" iconName="vi_info_notif_small"></Icon>
    </div>
    <Text className="payment-text-14 payment-leading-19 payment-text-gray-4" elementType="p">{simpleLabel}</Text>
  </Text>
);










