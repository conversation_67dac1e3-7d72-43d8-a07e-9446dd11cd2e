import React, { forwardRef }  from "react";
import {CheckboxCard, Text, Price}from "@virgin//virgin-react-ui-library";
import { PaymentItem } from "../../models";

export interface CurrentBalanceProps extends React.ComponentPropsWithoutRef<"div"> {
  isDisabled?: boolean;
  isChecked?: boolean;
  label?: string;
  id?: string;
  billType?: string;
  billAccountNumber?: string;
  idIndex?: number;
  text?: React.ReactNode;
  priceSettings?: {
    price: number;
    language?: "en" | "fr";
    negativeIndicator?: "CR" | "-" | null;
  };
  currentItem: PaymentItem
  isCheckedBalanceItems: PaymentItem[];
  setIsCheckedBalanceItems: Function;
  priceSettingsSrText?: string;
}
export const CheckboxCardCurrentBalance = forwardRef<HTMLInputElement, CurrentBalanceProps>(({
  className,
  isDisabled,
  isChecked,
  label,
  id,
  billType,
  billAccountNumber,
  idIndex,
  text,
  priceSettings,
  currentItem,
  isCheckedBalanceItems,
  setIsCheckedBalanceItems,
  priceSettingsSrText
}: CurrentBalanceProps, ref) => {

  const handleCheckboxChange = (event: any, item: PaymentItem) => {
    if (event.target.checked)
    {
      setIsCheckedBalanceItems([...isCheckedBalanceItems, item]);
    }
    else
    {
      setIsCheckedBalanceItems((isCheckedItems: any) => isCheckedItems.filter((checkedItem: any) => checkedItem.BillName !== item.BillName));
    }
  };

  return (
    <CheckboxCard
      ref={ref}
      id={id}
      aria-labelledby={label}
      disabled={isDisabled}
      defaultChecked={isChecked}
      className={["group-has-[:disabled]/inputcheckbox:payment-bg-gray-10 payment-chekcbox-disabled payment-group/checkboxcard payment-px-16 sm:!payment-px-24 payment-py-24 sm:!payment-py-32 payment-basis-unset sm:payment-basis-p48 md:payment-basis-p35 vrui-w-full payment-mr-15 vrui-mb-15 payment-current-balance", className].join(" ").trim()}
      defaultPadding={false}
      checkboxPlacement="topLeft"
      data-banDetail={JSON.stringify({id,billAccountNumber, billType, price: priceSettings?.price})}
      onChange={(e) => handleCheckboxChange(e, currentItem)}
    >
      <div className="sm:payment-flex payment-pl-[35px]">
        <div
          className="sm:payment-mt-1 payment-flex-1 sm:payment-text-left"
          id={`checkboxBalance-${idIndex}-label-${idIndex}`}
        >
          <Text
            elementType="span"
            className="sm:payment-pl-5 payment-text-16 sm:payment-text-16 payment-leading-20 sm:payment-leading-22 vrui payment-flex payment-flex-row payment-items-center payment-gap-5 payment-text-darkblue"
          >
            <div aria-hidden="true" className="payment-flex">
              {text}&nbsp;
              {priceSettings && (
                <>
                  <Price
                    language={priceSettings.language ? priceSettings.language : "en"}
                    negativeIndicator={priceSettings.negativeIndicator ? priceSettings.negativeIndicator : "CR"}
                    price={priceSettings.price ? priceSettings.price : 0.0}
                    variant="defaultPrice"
                    className="!payment-text-16 payment-leading-22"
                    disableSrOnlyText
                  />
                </>
              )}
            </div>
            <span className="payment-sr-only">{priceSettingsSrText}</span>
          </Text>
        </div>
        <div
          className="payment-mt-2 sm:payment-mt-1 payment-flex-2 sm:payment-text-right payment-leading-18"
          id={`checkboxBalance-${idIndex}-label-${idIndex}-info`}
        >
          <Text elementType="span" className="payment-text-gray-4 payment-text-14 sm:payment-text-14">
            {" "}
            on
            <Text elementType="span" className="payment-text-gray-4">
              {" "}
              {billType}{" "}
            </Text>
            {billAccountNumber}
          </Text>
        </div>
      </div>
    </CheckboxCard>
  );
}
);

