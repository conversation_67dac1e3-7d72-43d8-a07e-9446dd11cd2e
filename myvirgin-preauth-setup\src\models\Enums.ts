
export enum PreAuthorizedPaymentMethod {
  Creditcard,
  Debit,
  ExistingCreditcard
}

export enum PaymentMethodCreditOption {
  ChangeCreditCardInfo,
  SwitchToBankAccount,
  UnEnroll
}

export enum PaymentMethodDebitOption
{
  ChangeBankAccountInfo,
  SwitchToCreditCard,
  UnEnroll
}

export enum PaymentMethod
{
  Regular, 
  CreditCard, 
  PreAuthBank, 
  PreAuthCreditCard,
  Invoice,
  ECoupon,
  Certificate
}

export enum UnEnrollStatus
{
  Unknown,
  Success,
  Failed,
  NetworkError
}

export enum RegisterPADStatus
{
  Unknown,
  Success,
  Failed,
  NetworkError
}

export enum RegisterPACCStatus
{
  Unknown,
  Success,
  Failed,
  NetworkError
}

export enum MakeOneTimePaymentStatus
{
  Unknown,
  Success,
  Failed,
  NetworkError
}

export enum PaymentItemBillStatus
{
  Unknown,
  Active,
  Suspended,
  Cancelled
}

export enum CreditCardType
{
  DC = 0, // DINERS-CLUB
  VI = 1, // VISA
  MC = 2, // MASTERCARD
  AX = 3 // AMERICAN-EXPRESS
}

export enum CreditCardValidationServiceEnum
{
  OneBill,
  Mobility,
  TV
}

export enum CreditCardValidationStatus
{
  Unknown,
  Success,
  Failed,
  NetworkError
}

export enum FieldType {
  CardNumber = "CREDIT_CARD_NUMBER",
  CardHolderName = "CREDIT_CARD_HOLDER_NAME",
  ExpirationDate = "CREDIT_CARD_EXPIRE_DATE",
  SecurityCode = "CREDIT_CARD_SECURITY_CODE",
  BankName = "BANK_NAME",
  BankAccountHolderName = "BANK_ACCOUNT_HOLDER_NAME",
  BankTransitCode = "BANK_TRANSIT_CODE",
  BankAccountNumber = "BANK_ACCOUNT_NUM",
  ServerValidation = "SERVER_VALIDATION"
}


export enum PageTitleCurrentSection {
  Default,
  SelectBills = "SelectBills",
  PaymentMethod = "PaymentMethod",
  CurrentBalance = "CurrentBalance",
  TermsAndCondition = "TermsAndCondition",
  Confirmation = "Confirmation",    
}
