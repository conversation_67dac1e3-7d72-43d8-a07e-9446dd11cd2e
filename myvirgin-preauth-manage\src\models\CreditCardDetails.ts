import { SelectListItem } from './index';
import { CreditCardType, CreditCardValidationServiceEnum, CreditCardValidationStatus } from './Enums';

export class CreditCardDetails {
  ID: string;
  Token: string;
  CreditCardNumber: string;
  CreditCardNumberMasked: string;
  CreditCardNumberMaskedDisplayView: string;
  CreditCardNumberMaskedDisplayViewB: string;
  CreditCardNumberDTSValidated: boolean;
  CreditCardType: CreditCardType;
  CardholderName: string;
  ExpireYear: string;
  ExpireMonth: string;
  SecurityCodeMasked: string;
  SecurityCode: string;
  ShowAgreement: boolean;
  ShowPreAuthorizedInfo: boolean;
  IsSaveCreditCard: boolean;
  IsPrepaid: boolean;
  Months?: SelectListItem[];
  Years?: SelectListItem[];
  ExpirationDateDisplayViewA: string;
  BackEndModelState: string | null;
  ValidationServiceSource: CreditCardValidationServiceEnum | null;
  ValidationStatus: CreditCardValidationStatus | null;
  IsDTSOutage: boolean;
  IsDTSValidated: boolean;
  IsValid: boolean;
  IsTokenizationValid: boolean;
  HasSpecialCharacters: boolean;
  HasExtraSpaces: boolean;
  ValidatedCVV: string | null;
  ValidatedExpireYear: string | null;
  ValidatedExpireMonth: string | null;
  ValidatedToken: string | null;
  AuthorizationCode: string | null;
  AccountNumber: string | null;
  
}

export const defaultCreditCardDetails: CreditCardDetails = {
  ID: "",
  Token: "",
  CreditCardNumber: "",
  CreditCardNumberMasked: "",
  CreditCardNumberMaskedDisplayView: "",
  CreditCardNumberMaskedDisplayViewB: "",
  CreditCardNumberDTSValidated: false,
  CreditCardType: CreditCardType.VI,
  CardholderName: "",
  ExpireYear: "",
  ExpireMonth: "",
  SecurityCodeMasked: "",
  SecurityCode: "",
  ShowAgreement: false,
  ShowPreAuthorizedInfo: false,
  IsSaveCreditCard: false,
  IsPrepaid: false,
  Months: [],
  Years: [],
  ExpirationDateDisplayViewA: "",
  BackEndModelState: null,
  ValidationServiceSource: null,
  ValidationStatus: null,
  IsDTSOutage: false,
  IsDTSValidated: false,
  IsValid: false,
  IsTokenizationValid: false,
  HasSpecialCharacters: false,
  HasExtraSpaces: false,
  ValidatedCVV: null,
  ValidatedExpireYear: null,
  ValidatedExpireMonth: null,
  ValidatedToken: null,
  AuthorizationCode: null,
  AccountNumber: null,
};

export default CreditCardDetails;

export class CCDetails {
  CreditCardNumber: string;
  CreditCardNumberMasked: string;
  // CreditCardType: CreditCardType;
  CardholderName: string;
  ExpireYear: string;
  ExpireMonth: string;
  SecurityCode: string;
}

export const CCDetailsDefault: CCDetails = {
  CreditCardNumber: '',
  CreditCardNumberMasked: '',
  // CreditCardType: CreditCardType;
  CardholderName: '',
  ExpireYear: '',
  ExpireMonth: '',
  SecurityCode: '',
};

export enum CreditCardDetailsAction {
  ONCHANGE_CREDITCARD_NUMBER ="ONCHANGE_CREDITCARD_NUMBER",
  ONCHANGE_CARDHOLDER_NAME="ONCHANGE_CARDHOLDER_NAME",
  ONCHANGE_EXPIRY_MONTH="ONCHANGE_EXPIRY_MONTH",
  ONCHANGE_EXPIRY_YEAR="ONCHANGE_EXPIRY_YEAR",
  ONCHANGE_EXPIRY_DATE="ONCHANGE_EXPIRY_DATE",
  ONCHANGE_SECURITY_CODE="ONCHANGE_SECURITY_CODE",
  SET_CREDIT_CARD_DEFAULT="SET_CREDIT_CARD_DEFAULT",
  SET_CREDIT_CARD_VALIDATION="SET_CREDIT_CARD_VALIDATION",
  RESET_CREDIT_CARD_VALIDATION="RESET_CREDIT_CARD_VALIDATION",
}

export class CreditCardInputValue {
  cardNumber: string;
  cardType: string;
  cardName: string;
  expiryDate: string;
}
export class InputBankAccountDetail {
  PaymentMethod: string;
  AccountHolder: string;
  BankName: string;
  TransitNumber: string;
  AccountNumber: string;
}

export class BankAccountDetail {
  PaymentMethod: string;
  AccountHolder: string;
  BankName: string;
  TransitNumber: string;
  AccountNumber: string;
}
export const defaultBankInputValue = {
  PaymentMethod: "",
  AccountHolder: "",
  BankName: "",
  TransitNumber: "",
  AccountNumber: "",
};

export const defaultCreditCardInputValue = {
  cardNumber: "",
  cardType: "",
  cardName: "",
  expiryDate: "",
};
