import React, { useState } from "react";
import { <PERSON>ton, <PERSON>dal, ModalBody, ModalContent, ModalHeader} from "@virgin/virgin-react-ui-library";
import { injectIntl } from "react-intl";
import { OmnitureOnBoxNameLightBox } from "../../store/Actions";
import { connect } from "react-redux";

interface LightboxNonameProps {
  intl: any;
  setOmnitureOnBoxNameLightBox: Function;
}


export const LightBoxNonameComponent = ({ intl, setOmnitureOnBoxNameLightBox }: LightboxNonameProps) => {    
  const [isModalOpen, setIsModalOpen] = useState(false);
  const onToggleModal = (isOpen: boolean) => {
    setIsModalOpen(isOpen);
  };

  const handleButtonClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault(); 
    onToggleModal(true);

    setTimeout(() => {
      setOmnitureOnBoxNameLightBox();
    }, 1000);
  };

  return (
    <div>
      <Button 
        variant="textBlue"
        onClick={handleButtonClick}
        size="small"
        className="payment-text-14 payment-leading-18"
      >
        {intl.formatMessage({id: "MODAL_NO_NAME"})}
      </Button>

      {isModalOpen && (
        <Modal
          id="no-name-on-card"
          aria-labelledby="no-name-on-card-title"
          onEscapeKeyPressed={() => onToggleModal(false)}
          onOverlayClick={() => onToggleModal(false)}
        >
          <ModalContent
            useDefaultRadius={true}
            className="payment-rounded-t-24 sm:payment-rounded-b-24 sm:payment-rounded-t-24" 
          >
            <ModalHeader
              variant="grayBar"
              rightButtonIcon="default"
              isDefaultPadding={true}
              className="payment-px-15 sm:payment-px-32 payment-py-24"
              title={intl.formatMessage({id: "MODAL_NO_NAME_TITLE"})}
              onRightButtonClicked={() => onToggleModal(false)}
              rightButtonLabel={intl.formatMessage({id: "CTA_CLOSE"})}
            />
            <ModalBody
              isDefaultPadding={true}
              className="payment-px-15 sm:payment-px-30 payment-py-30">
              <div className="payment-text-gray payment-text-14 payment-leading-18">{intl.formatMessage({id: "MODAL_NO_NAME_DESC"})}</div>
            </ModalBody>
          </ModalContent>
        </Modal>
      )}
    </div>
  );
};

const mapDispatchToProps = (dispatch: React.Dispatch<any>) => ({ 
  setOmnitureOnBoxNameLightBox: (data?:any) => dispatch(OmnitureOnBoxNameLightBox({data}))
});

export const LightBoxNoname = connect(null, mapDispatchToProps)(injectIntl(LightBoxNonameComponent));
