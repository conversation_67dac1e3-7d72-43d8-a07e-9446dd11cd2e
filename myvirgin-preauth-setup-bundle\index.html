<!DOCTYPE html>
<html lang="en">
<head>
    <meta></meta>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>E1.2 - MyBell - Edit Email</title>
    <!-- Bootstrap -->
    <link href="../core/css/bootstrap.min.css" rel="stylesheet">
    <link href="../core/css/allBrowsers_framework.css" rel="stylesheet">
    <link href="../core/css/bell.css" rel="stylesheet">
    <!-- <link href="../core/css/bell-forms.css" rel="stylesheet" />
    <link href="../content/css/ecare-edit-profile.css" rel="stylesheet" /> -->
</head>
<body>
    <div class="clear">
        <!-- Header -->

        <header class="simplified-header spacer55 boxShadow1">
            <div class="container liquid-container">
                <div class="spacer55">
                    <div class="block relative">
                        <div class="absolute txtWhite headerBack">
                            <i class="icon icon-chevron-left arrow txtSize15" aria-hidden="true"></i>
                            <span class="txtSize14 pad-10-left txtLineHeight-16 hidden-xs">Back</span>
                        </div>
                        <label class="bellSlim simplified-header-area-title headerTitle">
                            Edit email
                        </label>
                        <div class="absolute headerLogout hidden-xs hidden-sm">
                            <button class="btn btn-primary-white" type="button">Log out</button>
                        </div>
                    </div>
                </div>
            </div>
        </header>
        <!-- Header -->
        <!--content-->
        <div id="container"></div>
        <!--content-->
        <!--Footer-->
        <div class="spacer40 hidden-xs"></div>
        <div class="spacer10 visible-xs"></div>
        <footer role="contentinfo">
            <div class="top-border-gray-light">
                <div class="spacer30 hidden-xs"></div>
                <div class="spacer25 visible-xs"></div>
                <div class="container liquid-container">
                    <div class="col-xs-12 col-md-9 col-lg-9">
                        <div class="footerLinks txtSize12">
                            <a href="#" class="txtNoUnderline">Privacy</a>
                            <span class="txtLightGray hidden-xs">|</span>
                            <a href="#" class="txtNoUnderline">Security</a>
                            <span class="txtLightGray hidden-xs">|</span>
                            <a href="#" class="txtNoUnderline">Legal &amp; Regulatory</a>
                            <span class="txtLightGray hidden-xs">|</span>
                            <a href="#" class="txtNoUnderline">Your rights as a wireless customer</a>
                        </div>
                        <div class="footerLinks txtSize14 txtCenter-xs">
                            <span>© Bell Canada, 2016. All rights reserved.</span>
                        </div>
                    </div>
                    <div class="col-xs-12 col-sm-12 col-md-3 col-lg-3">
                        <div class="footerBtn margin-20-top-xs margin-15-top-sm">
                            <div class="">
                                <button class="btn btn-primary btn-rate" type="submit">
                                    Rate this page
                                </button>
                                <button class="btn btn-primary btn-logout " type="submit">
                                    Logout
                                </button>
                                <!-- <button class="btn btn-primary btn-french" type="submit">
                                    Français
                                </button>  -->
                                <div class="inlineBlock img">
                                    <!-- <img src="../../content/img/logoFooter.png" class="entrust"> -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="custom-margin"></div>
            </div>
        </footer>


        <!--Footer-->
        <!-- jQuery (necessary for Bootstrap's JavaScript plugins) -->
        <script src="/core/js/jquery-3.5.1.min.js"></script>
        <!-- Include all compiled plugins (below), or include individual files as needed -->
        <!-- <script src="../core/js/bootstrap.min.js"></script> -->
        <!-- <script src="../core/css/bell.css"></script> -->

        <script src="/node_modules/rxjs/dist/bundles/rxjs.umd.min.js"></script>
        <script src="/node_modules/redux/dist/redux.js"></script>
        <script src="/node_modules/redux-observable/dist/redux-observable.js"></script>
        <script src="/node_modules/redux-actions/dist/redux-actions.js"></script>
        <script src="/node_modules/react/umd/react.development.js"></script>
        <script src="/node_modules/react-dom/umd/react-dom.development.js"></script>
        <script src="/node_modules/prop-types/prop-types.js"></script>
        <script src="/node_modules/react-redux/dist/react-redux.js"></script>
        <script src="/node_modules/react-intl/react-intl.iife.js"></script>
        <script src="/node_modules/bwtk-polyfill/dist/polyfill.min.js"></script>
        <script src="/node_modules/bwtk/dist/bwtk.js"></script>
        <script src="/dist/bell-preauth-setup-bundle-bundle.min.js"></script>
        <script>



var config = Object.assign({
			
                "brand": "B",
                "province": "QC",
                "channel": "BELLCAEXT",
                "language": "en-ca",
                "userid": "",
                "useMockData": true
                }
			
		);
		initialize(config, "container");

		let store = bwtk.ServiceLocator.instance.getService(bwtk.CommonServices.Store);
		store.dispatch(bwtk.setLocale("en"));

		// store.createGlobalActionListener(function (action) {
        //     try {
        //         console.log("inside the store action")
        //         //    if (action.type === "CANCEL_EDIT") {
        //         //     var xhr = null;
        //         //     var profileType = "";
        //         //     switch (action.payload.ProfileType) {
        //         //         case 0:
        //         //         profileType = "ProfileInfo";
        //         //             break;
        //         //         default:
        //         //             break;
        //         //     }
        //         // }
    
        //     } catch (err) {
        //         console.error("store.createGlobalActionListener: ", err);
        //     }
        // })
        </script>
    </div>

</body>
</html>
