import React from "react";
import {<PERSON><PERSON>, <PERSON>dalContent, ModalBody} from "@virgin/virgin-react-ui-library";
import {injectIntl } from "react-intl";

interface ToastSuccessModalProps {
    isModalOpen: boolean;
    setIsModalOpen: Function;
    intl: any;
}


const ToastSuccessModalComponent = (props: ToastSuccessModalProps) => {
    const { isModalOpen, setIsModalOpen, intl } = props;

    const onToggleModal = (isOpen: boolean) => {
        setIsModalOpen(isOpen);
    };

    const handleCloseClick = () => {
        setIsModalOpen(false);
        window.location.href = "/";
    }

    return (
        <div>
            {isModalOpen && (
                <Modal
                className="payment-toaster"
                id="cancel-preauth-success-modal"
                aria-labelledby="cancel-preauth-success-modal-title"      
                onEscapeKeyPressed={() => onToggleModal(false)}
              >
                <ModalContent
                  useDefaultRadius={true}
                  className="payment-rounded-t-24 sm:payment-rounded-b-24 sm:payment-rounded-t-24" 
                >
                  <ModalBody
                    isDefaultPadding={true}
                    className="payment-px-15 sm:payment-px-30 payment-py-30"
                  >
                      <div className="payment-text-gray payment-text-14 payment-leading-18">
                          <span className="payment vi_small_checkmark_fill payment-text-green payment-text-24" role="img" aria-hidden="true" aria-label=" "></span>
                          <div id="account-fetched" className="payment-flex payment-flex-col payment-text-14 ">
                            <span className="payment-mt-[3px] payment-text-gray">
                                {intl.formatMessage({id: "SUCCESS_TOAST_MESSAGE"})}
                            </span>
                          </div>
                          <button onClick={handleCloseClick} type="button" aria-label="Close dialog box" className="payment-flex payment-rounded-2 focus:payment-outline-blue focus:payment-outline focus:payment-outline-2 focus:payment-outline-offset-3 payment-h-12 payment-ml-auto" id="no-name-on-card-close-button">
                              <span className="vi_close_bold payment payment-text-12 payment-text-gray" role="img" aria-hidden="true" aria-label=" "></span><span className="payment-sr-only">
                              {intl.formatMessage({id: "CTA_CLOSE"})}
                              </span>
                          </button>
                      </div>
                  </ModalBody>
                </ModalContent>
                
              </Modal>
            )}
        </div>
    );
};

export const ToastMessage = injectIntl(ToastSuccessModalComponent);