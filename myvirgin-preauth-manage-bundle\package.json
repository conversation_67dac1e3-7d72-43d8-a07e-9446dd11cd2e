{"name": "myvirgin-preauth-manage-bundle", "domain": "Payments", "version": "1.0.0", "private": true, "scripts": {"linklocal": "linklocal", "dev": "webpack -w", "build": "webpack", "build:dev": "webpack --env -d", "build:prod": "webpack --env -p", "start": "http-server", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "MIT", "devDependencies": {"bwtk": "git+https://gitlab.int.bell.ca/uxp/bwtk.git#v6.1.0", "myvirgin-preauth-manage": "file:../myvirgin-preauth-manage", "redux-observable": "^0.19.0", "husky": "4.3.8", "webpack-cli": "^5.1.4"}, "husky": {"hooks": {"pre-commit": "node pre-commit.js"}}}