import { Epic } from "redux-observable";
import { AjaxResponse } from "bwtk";
import { IBankInfoReqPayload, IBankInfoRes, IGetRedirectUrl } from "./RedirectUrl";
import { ICancelPreauthReqPayload, ICancelledItem } from "./CancelPreauth";

export type IGetRedirectUrlEpic = Epic<ReduxActions.Action<IGetRedirectUrl>, any>;


export type IInteracBankInfoEpic = Epic<ReduxActions.Action<IBankInfoReqPayload>, any>;

export interface IGetRedirectUrlEpicResponse extends AjaxResponse {
  data: IGetRedirectUrl;
}

export interface IInteracBankInfoEpicResponse extends AjaxResponse {
  data: IBankInfoRes;
}

export type ICancelPreauthPaymentsEpic = Epic<ReduxActions.Action<ICancelPreauthReqPayload>, any>;

export interface ICancelPreauthPaymentsEpicResponse extends AjaxResponse {
  data: ICancelledItem[];
}
