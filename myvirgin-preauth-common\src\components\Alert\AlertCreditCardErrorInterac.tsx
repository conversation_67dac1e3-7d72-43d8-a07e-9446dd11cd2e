import * as React from "react";
import {Alert, Heading, Text} from "@virgin/virgin-react-ui-library";
import { injectIntl } from "react-intl";
import {INTERACTBANKSERVERFAILURE} from "../../utils";


interface ErrorInteracProps {
  intl: any;
  interact: any;
}

const AlertCreditCardErrorInteracComponent = ({intl, interact}: ErrorInteracProps) => (
  <Alert 
    variant="error" 
    className="payment-block payment-p-16 sm:payment-flex vrui-px-0 sm:payment-px-30 sm:payment-py-30 vrui-border-x-0 sm:payment-border-x-1 payment-border-y-1 payment-relative vrui-rounded-none payment-rounded-16" 
    iconSize="36"
    id="alert-3">
    <Text elementType="div" className="payment-pl-0 payment-pt-10 sm:payment-pl-15 sm:payment-pt-0">
      <Heading level="h2" variant="xs" className=" sm:vrui-mt-7 vrui-mb-15 vrui-font-sans vrui-leading-22">
        <span aria-hidden="true">{interact.includes(INTERACTBANKSERVERFAILURE) ?intl.formatMessage({id: "ALERT_ERROR_HEADING_INTERAC"}) : intl.formatMessage({id: "ALERT_ERROR_HEADING_INTERAC"}) }</span>
        <span className="payment-sr-only">{ interact.includes(INTERACTBANKSERVERFAILURE) ? intl.formatMessage({id: "ALERT_ERROR_HEADING_INTERAC_SR"}) : intl.formatMessage({id: "ALERT_ERROR_HEADING_INTERAC_SR"})}</span>
      </Heading>
      <div>
        <p className="vrui-text-14 vrui-my-15 vrui-text-gray">{interact.includes(INTERACTBANKSERVERFAILURE) ? intl.formatMessage({id: "ALERT_ERROR_HEADING_INTERAC_DESC"}) : intl.formatMessage({id: "ALERT_ERROR_HEADING_INTERAC_DESC"})}</p>
      </div>
    </Text>
  </Alert>
);

export const AlertCreditCardErrorInterac = injectIntl(AlertCreditCardErrorInteracComponent);
