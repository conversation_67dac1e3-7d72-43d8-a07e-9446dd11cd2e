import React, { useRef, forwardRef, useState } from "react";
import {CheckboxCard,Text, useHeightResizeObserver } from "@virgin/virgin-react-ui-library";

import { PaymentItem } from "../../models";

export interface BillProps extends React.ComponentPropsWithoutRef<"div">{
  isDisabled?: boolean;
  isChecked?: boolean;
  label?: string;
  id?: string;
  billType?: string;
  billAccountNumber?: string;
  idIndex?: number;
  text?: React.ReactNode;
  isPreAuth?: boolean;
  priceSettings?: {
    price: number;
    language?: "en" | "fr";
    negativeIndicator?: "CR" | "-" | null;
   
  };
  item: PaymentItem;
  isCheckedItems: PaymentItem[];
  setIsCheckedItems: Function;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  isShowLabel?: boolean;
  paymentItems: PaymentItem[];
}


export const CheckboxCardBill = forwardRef<HTMLInputElement, BillProps>(({
  className,
  isDisabled,
  isChecked,
  label,
  id,
  billType,
  billAccountNumber,
  idIndex,
  text,
  priceSettings,
  isPreAuth,
  item,
  isCheckedItems,
  setIsCheckedItems,
  onChange,
  isShowLabel,
  paymentItems
}: BillProps, ref) => {
  const [isCheckBoxChecked, setCheckBoxChecked] =  React.useState<PaymentItem[]>([]);
  const ban = item.BanID;
  const handleCheckboxChange = (event: any, item: PaymentItem) => {
    if (event.target.checked)
    {
      setIsCheckedItems([...isCheckedItems, item]);
    }
    else
    {
      setIsCheckedItems((isCheckedItems: any) => isCheckedItems.filter((checkedItem: any) => checkedItem.BillName !== item.BillName));
    }
  };

  React.useEffect(() => {
    const inputBoxes = document.querySelectorAll("input[type='checkbox']");
    const checkedBoxes: any[] = [];
    inputBoxes.forEach(a => {
      if ((a as HTMLInputElement).checked) {
        const dataAttribute = a.getAttribute("data-bandetail");
        const dataChecked = dataAttribute && JSON.parse(dataAttribute);
        const checkedBan = dataChecked && dataChecked.ban && paymentItems.filter(item => item.BanID === dataChecked.ban);
        if (checkedBan && checkedBan.length > 0)
        {
          checkedBoxes.push(checkedBan[0]);
        }
      }
    });
    if (checkedBoxes && checkedBoxes.length > 0 && checkedBoxes.map(item => item !== null)){
      const checkedCheckBoxes = checkedBoxes.filter(item => item !== null);
      setCheckBoxChecked((isCheckedItems: any) => isCheckedItems.concat([...checkedCheckBoxes]));
    }
  }, []);

  React.useEffect(() => {
    if (isCheckBoxChecked !== null && isCheckBoxChecked.length > 0) {
      const uniqueitems = isCheckBoxChecked.reduce((acc: PaymentItem[], cur) => {
        const existingItem = acc.find((item: PaymentItem) => cur.BanID === item.BanID);
        if (!existingItem) {
          acc.push({ ...cur });
        }
        return acc;
      }, []);
      
      setIsCheckedItems(uniqueitems);
    }

  }, [isCheckBoxChecked]);

  const contentWrapper = useRef<HTMLDivElement | null>(null);
  const [contentHeight, setContentHeight] = useState(contentWrapper.current?.scrollHeight || 0); // Adjust the default value as needed
  useHeightResizeObserver(contentWrapper, contentHeight, setContentHeight);

  return (
    <CheckboxCard
      ref={ref}
      id={id}
      aria-labelledby={label}
      disabled={isDisabled}
      defaultChecked={isChecked}
      className={["group-has-[:disabled]/inputcheckbox:payment-border-gray-10 payment-min-h-[110px] group-has-[:disabled]/inputcheckbox:payment-bg-gray-5 payment-chekcbox-disabled payment-group/checkboxcard payment-pt-24 payment-pl-16 payment-pr-16 payment-pb-24 sm:payment-p30 payment-border-gray-10 payment-border-1 payment-rounded-6 [&>div>div]:payment-rounded-6 [&>div>div:nth-child(3)]:payment-left-16 [&>div>div:nth-child(3)]:payment-top-[36px]", className].join(" ").trim()}
      defaultPadding={false}
      checkboxPlacement="topLeft"
      data-banDetail={JSON.stringify({id,billAccountNumber, ban, billType, price: priceSettings?.price})}
      onChange={(e) => handleCheckboxChange(e, item)}
    >
      <div ref={contentWrapper} className={isShowLabel ? "sm:payment-flex payment-pl-[35px] sm:payment-pl-[36px] payment-flex-col" : "sm:payment-flex payment-pl-[35px] sm:payment-pl-[36px] payment-flex-col payment-mt-[13px]"}>
        <div
          className="payment-flex payment-w-max payment-items-center payment-mb-5"
          id={`checkboxBill${idIndex}-label-${idIndex}`}
        >
          <Text elementType="span" className="payment-font-bold">
            {billType}{" "}
            <Text elementType="span" className="payment-text-14 payment-text-gray !payment-font-normal">
              {billAccountNumber}
            </Text>
          </Text>
        </div>
        {isShowLabel && (
          <div className="payment-flex payment-w-fit payment-items-center payment-mr-[15px]" id={`checkboxBillBalance-${idIndex}-label-${idIndex}`}>
            <Text
              elementType="span"
              className="payment-text-14 payment-text-gray payment-break-words payment-flex payment-items-center payment-leading-18"
            >
              {text} 
            </Text>
          </div>
        )}
      </div>
    </CheckboxCard>
  );
}
);

