import * as React from "react";
import { forwardRef, useState } from "react";


import {
  RadioButton,
  Label
} from "@virgin/virgin-react-ui-library";
import { PaymentItem } from "../../models";

export interface FormFieldsBankPaymentRadioProps {
  intl?: any,
  isErrorCase?: boolean,
  legends?: string,
  isBankPayment?: boolean,
  isCreditCardPayment?: boolean,
  children?: React.ReactNode,
  srOnly: string,
}

export const FormFieldsBankRadioPayment = (
  {
    intl,isErrorCase, children, legends, isBankPayment, isCreditCardPayment,srOnly
  }:FormFieldsBankPaymentRadioProps) => {
  const getInitialState = () => {
    let initialState = false;
    React.Children.forEach(children, (child) => {
      // if (React.isValidElement(child) && child.props.defaultChecked && child.props.showBankFieldsOnChange) {
      //   initialState = true;
      // } OLD CODE

      if (React.isValidElement(child)) {
        // Safely access child.props with the specific type expected
        const { defaultChecked, showBankFieldsOnChange } = child.props as { defaultChecked?: boolean; showBankFieldsOnChange?: boolean };
        if (defaultChecked && showBankFieldsOnChange) {
          initialState = true;
        }
      }
    });
    return initialState;
  };

  const [ bankPaymentShown, setBankPaymentShown ] = useState(getInitialState);
  const handleRadioChange = (isShown: boolean) => {
    setBankPaymentShown(isShown); 
  };
  return( 
    <fieldset>
      <legend className="vrui-sr-only">{srOnly}</legend>
      <div className="vrui-flex vrui-flex-col">
        <div className="vrui-flex vrui-flex-col sm:vrui-flex-row">
          <div className="sm:payment-w-[174px] sm:payment-min-w-[174px] sm:payment-mr-30 sm:payment-text-right payment-pt-13 payment-pb-13 sm:payment-pb-0 payment-mb-10 sm:payment-mb-0">
            <Label className="vrui-block" required>{legends}</Label>
          </div>
          <div className="vrui-flex vrui-flex-col payment-gap-15">
            {React.Children.map(children, (child, index) => React.cloneElement(child as React.ReactElement<any>, {
              showOnChange: handleRadioChange,
              // hasError: isErrorCase,
              childIndex: index
            }))}
          </div>
        </div>
      </div>
      {bankPaymentShown}
    </fieldset>
  );
};

interface BankPaymentRadioButtonProps {
  name?: string;
  value: string;
  hasError?: boolean;
  showBankFieldsOnChange?: boolean;
  showOnChange?: (showBankFieldsOnChange: boolean) => void;
  childIndex?: number;
  label: string;
  idPrefix?: string;
  defaultChecked?: boolean;
  className?: string;
  onChange?: React.ChangeEventHandler<HTMLInputElement> | undefined;
  getExistingBankPaymentDetails?: any;
  paymentDetails?: PaymentItem[];
  srText?: string;
}



export const BankPaymentRadioButton = forwardRef<HTMLInputElement,BankPaymentRadioButtonProps>(function BankPaymentRadioButton(
  {
    name = "bank-card-details-radio",
    value,
    hasError,
    showOnChange,
    showBankFieldsOnChange = false,
    childIndex,
    defaultChecked,
    className,
    label,
    onChange,
    idPrefix = "",
    getExistingBankPaymentDetails,
    paymentDetails,
    srText,
  },forwardedRef) {
  
  const handleClick = (details:any) => {
    if (typeof getExistingBankPaymentDetails === "function") {
      getExistingBankPaymentDetails(details || []);
    }
  };

  React.useEffect(() => {
    if (paymentDetails && defaultChecked) {
      getExistingBankPaymentDetails(paymentDetails || []);
    }
  }, []);

  return (
    <RadioButton
      className={[className,"vrui-flex vrui-items-center vrui-absolute vrui-size-full vrui-opacity-0 enabled:vrui-cursor-pointer disabled:vrui-cursor-default vrui-z-10"].join(" ").trim()}
      id={idPrefix + "bank-payment-radio-id-" + childIndex}
      name={name}
      value={value}
      variant="boxedInMobile"
      hasError={hasError}
      // onChange={() => showOnChange? showOnChange(showBankFieldsOnChange): {onChange}}
      defaultChecked={defaultChecked}
      ref={forwardedRef}
      onChange={onChange}
      onClick={() => handleClick(paymentDetails)}
    >
      {(srText) 
        ?
        <>
          <div className="vrui-text-14 vrui-leading-18 vrui-mt-3" aria-hidden="true" dangerouslySetInnerHTML={{__html: label}}></div>
          <div className="payment-sr-only" dangerouslySetInnerHTML={{__html: srText}}></div>
        </>
        :
        <>
          <div className="vrui-text-14 vrui-leading-18 vrui-mt-3" dangerouslySetInnerHTML={{__html: label}}></div>
        </>
      }
      
    </RadioButton>
  );
});
