import React from "react";
import {HeadingStep, IconLink, Icon } from "@virgin/virgin-react-ui-library";
import { FormattedMessage, injectIntl } from "react-intl";
import {SingleRowInformation} from "../../index";
import { PaymentItemAccountTypeName, PaymentItem } from "../../models";
import { getItemAccountTypeName } from "../../utils";
// import { string } from "prop-types";

interface BillSelectedComponentProps {
  intl: any;
  isActive?: boolean;
  banDetails: string[];
  onIconLinkClick?: (steps:any) => void;
  isCheckedItems: PaymentItem[];
}


const BillSelectedComponent = ({ intl, isActive,onIconLinkClick,banDetails,isCheckedItems}:BillSelectedComponentProps) => {
  // const parseJson = banDetails.map((item) => JSON.parse(item));
  const accountTypename : PaymentItemAccountTypeName = {
    MyBill: intl.formatMessage({id: "ACCOUNT_TYPENAME_MYBILL"}),
    Mobility: intl.formatMessage({id: "ACCOUNT_TYPENAME_MOBILITY"}),
    OneBill: intl.formatMessage({id: "ACCOUNT_TYPENAME_ONEBILL"}),
    TV: intl.formatMessage({id: "ACCOUNT_TYPENAME_TV"}),
    Internet: intl.formatMessage({id: "ACCOUNT_TYPENAME_INTERNET"}),
    HomePhone: intl.formatMessage({id: "ACCOUNT_TYPENAME_HOMEPHONE"}),
    MobilityAndOneBill: intl.formatMessage({id: "ACCOUNT_TYPENAME_MOBILITY_ONEBILL"}),
    SingleBan: intl.formatMessage({id: "ACCOUNT_TYPENAME_SINGLEBAN"})
  };

  const BillInformationItems = isCheckedItems.map((item) => ({
    label: <FormattedMessage 
      id="SELECT_BILLS_ACCOUNT_TITLE"
      values={{
        accounttype: getItemAccountTypeName(item.AccountType,item.IsNM1Account,accountTypename)
      }} />,
    value: item.NickName,
  })
  );

  return (
  
    <div className={isActive ? "payment-mb-40 payment-block" : "sm:payment-mb-15 payment-border-b payment-border-gray-4 payment-hidden"}>
      <div className={isActive ? "payment-flex payment-items-center payment-justify-between payment-mb-15" : "payment-flex payment-items-center payment-justify-between"}>
        <HeadingStep
          autoScrollActiveStep={false}
          disableSrOnlyText={isActive ? true : false}
          status={isActive ? "complete" : "inactive"}
          subtitle=""
          hideSubtitle
          variant="leftAlignNoStep"
          title={BillInformationItems.length > 1 ? 
            intl.formatMessage({id: "SELECT_BILLS_HEADING"}) : 
            intl.formatMessage({id: "SELECT_BILLS_HEADING_SINGULAR"})
          }
          id="pre-auth-select-selected-bill"
        />
        {/* show IconLink component on Review */}
        <div className="payment-pt-44">
          <IconLink
            icon={<Icon iconClass="vi_vrui" iconName="vi_edit" className="vrui-text-16"></Icon>}
            text={intl.formatMessage({id: "CTA_EDIT"})}
            role="button"
            variant="textBlue"
            size="regular"
            href="javascript:void(0);"
            position="right"
            className={["payment-flex payment-items-center !payment-text-14 !payment-leading-18", isActive ? "" : "payment-hidden"].join(" ").trim()}
            aria-describedby="pre-auth-select-selected-bill"
            onClick={() => onIconLinkClick && onIconLinkClick(1)} // Pass the click event
          >
          </IconLink>
        </div>
      </div>

      {/* Review section */}
      <div className={["vrui-pb-40",isActive ? "" : "vrui-hidden"].join(" ").trim()}>
        <div className="vrui-mt-15">
          {BillInformationItems.map((item, index) => (
            <SingleRowInformation className={index > 0 ? "vrui-mt-5" : ""} label={item.label} value={item.value} />
          ))}
        </div>
      </div>
    </div>

  );
};

export const BillSelected = injectIntl(BillSelectedComponent);
