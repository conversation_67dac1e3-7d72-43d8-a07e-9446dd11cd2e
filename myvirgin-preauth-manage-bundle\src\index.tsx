import * as React from "react";
import * as ReactDOM from "react-dom";
import { WidgetLoader, Init } from "bwtk";


export function initialize(config: any, root: string, debug: any) {
  const configFile = { ...config,  "loader.staticWidgetMappings": {
    "myvirgin-preauth-manage": {
      factory: () => require("myvirgin-preauth-manage"),
      namespace: "Preauth/Manage"
    }
  }};
  Init(configFile);

  ReactDOM.render(
    <WidgetLoader widget="myvirgin-preauth-manage" />,
    document.getElementById(root));
}
