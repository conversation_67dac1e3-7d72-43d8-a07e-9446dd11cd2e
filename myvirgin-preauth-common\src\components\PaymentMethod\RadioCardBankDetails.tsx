import React, { ComponentPropsWithRef, forwardRef} from "react";
import {RadioCard, Heading, Text} from "@virgin/virgin-react-ui-library";

// import Interac_box_logo from "../assets/images/interac-logos/Interac_box_logo.svg";


export interface RadioCardBankDetailsProps extends ComponentPropsWithRef<"input"> {
  id: string;
  label: string;
  name: string;
  describe?: string;
  defaultChecked?: boolean;
  isInterac?: boolean;
  headingLevel?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6";
  children: React.ReactNode;
  interactIconPath?: string
  // intl: any;

}

export const RadioCardBankDetails = forwardRef<HTMLInputElement, RadioCardBankDetailsProps>(function RadioCardBankDetails(
  {
    id,
    label,
    name,
    describe,
    defaultChecked = false,
    isInterac,
    headingLevel = "h4",
    children,
    // intl,
    onChange,
    interactIconPath
  },
  ref
){


  return (
    <>
      <div className="payment-mb-16  last:payment-mb-0">
        <RadioCard
          className="payment-border payment-border-gray-3 payment-py-32 payment-group/radiocard"
          id={id}
          name={name}
          defaultChecked={defaultChecked}
          aria-labelledby={"label-" + id}
          aria-describedby={describe ? ('desc-' + id) : ''}
          radioPlacement="topLeft"
          ref={ref}
          onChange={onChange}
        >
          <div className="payment-pl-32 payment-relative payment-flex payment-justify-between">
            <Heading
              level={headingLevel}
              variant="default"
              className={`!payment-font-poppins-Regular group-has-[input[type=radio][name="bank-details-radio"]:checked]/radiocard:payment-font-semibold payment-mb-10 sm:payment-mb-5 payment-text-16 payment-leading-22 payment-mt-3 payment-text-darkblue sm:payment-mr-[85px]`}
              id={"label-" + id}
            >
              {label}
            </Heading>
            {isInterac && (
              <img alt="" className="payment-ml-5 sm:payment-absolute payment-right-0 payment-top-0 sm:payment-w-40 sm:payment-h-40 payment-w-32 payment-h-32" src={interactIconPath} />
            )}
          </div>
          <div className="sm:payment-pl-32 sm:payment-mr-[85px]">
            {describe && (
              <Text
                id={'desc-' + id}
                elementType="p"
                className="payment-text-14 payment-leading-19 payment-text-darkblue"
              >
                {isInterac 
                  ? <span dangerouslySetInnerHTML={{ __html: describe}}></span> 
                  : describe
                }
              </Text>
            )}
            <div className="payment-z-10 payment-relative">
              {/* <div className="payment-hidden group-has-[input[type=radio][name=bank-details-radio]:checked]/radiocard:payment-block"> */}
              <div className={`payment-hidden group-has-[input[type=radio][name="bank-details-radio"]:checked]/radiocard:payment-block`}>
                {children}
              </div>
            </div>
          </div>
        </RadioCard>
      </div>
    </>
  );
});

// export default (injectIntl(RadioCardBankDetails))
export default RadioCardBankDetails;
