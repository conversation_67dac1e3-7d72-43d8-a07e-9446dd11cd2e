import * as React from "react";
import { Alert, Heading, Text } from "@virgin/virgin-react-ui-library";
import { injectIntl } from "react-intl";
import AlertNotificationList from "./AlertNotificationList";
import AlertNotificationListItem from "./AlertNotificationListItem";
import { AccountInputValues, PaymentItem } from "../../models";
import { getPaymentItemAccountType } from "../../utils";

interface AlertErrorOneTimePaymentProps {
  intl: any;
  // checkedCurrentBalanceItems: PaymentItem[];
  submitMultiOrderPayment: any;
  accountInputValue: AccountInputValues[];
  language: "en" | "fr";
  // notOptedBalanceItems: PaymentItem[];
  checkedBillItems: PaymentItem[];
}

const AlertRegFailureComponent = (
  {
    intl, 
    // checkedCurrentBalanceItems, 
    submitMultiOrderPayment, 
    accountInputValue, 
    language, 
    // notOptedBalanceItems, 
    checkedBillItems
  }:AlertErrorOneTimePaymentProps) => {
  const ALERT_REGFAILURE_HEADING = intl.formatMessage({id: "ALERT_REGFAILURE_HEADING"});

  const OrderItemsRegFailed = submitMultiOrderPayment.filter((item:any) => true);
  // const OrderItemsRegFailed = submitMultiOrderPayment.filter((item:any) => item?.RegisterPADStatus && item?.RegisterPADStatus !== 1);
  const regFailedOrderFormIdList = OrderItemsRegFailed.map((item:any) => item?.OrderFormId);
  const regFailedAccountList = accountInputValue
    .filter((x) => regFailedOrderFormIdList.includes(x.transactionID))
    .map((x) => x.accountNumber);
  const regFailedItems = checkedBillItems?.filter((item) => regFailedAccountList.includes(item.Ban));
  // const checkedBillItemsHasBalance = checkedBillItems?.length > 0 ? checkedBillItems?.filter((x: PaymentItem) => x.Due > 0 && x.AccountType != PaymentItemAccountType.OneBill && !x.isLastBillOnPreauth) : 0;
  // const SelectedOtpItems:any = checkedCurrentBalanceItems;
  // const SomeOtpItemsFailed = (SelectedOtpItems.length > failedOtpItems.length) ? true : false;
  // const AllOtpItemsFailed = (SelectedOtpItems.length == failedOtpItems.length && OrderItemsFailed.length > 0 && checkedBillItemsHasBalance.length > 1) ? true : false;
    
  return (
    <Alert 
      variant="error"
      className="payment-block payment-border payment-rounded-16 sm:payment-flex payment-p-16 sm:payment-p-24 vrui-relative"
      iconSize="32"
      screenReaderText={intl.formatMessage({id: "ALERT_ERROR_ICON"})}
      id="alert-otp-fail"
    >
      <Text elementType="div" className="payment-pl-0 payment-pt-15 sm:payment-pl-15 sm:payment-pt-0">
        <Heading level="h3" variant="xs" className="payment-mb-12 payment-text-darkblue">
          {ALERT_REGFAILURE_HEADING}
          {/* {(SelectedOtpItems.length == 1 && notOptedBalanceItems.length == 0) &&
                        <FormattedMessage 
                            id="ALERT_ERROR_OTP_BALANCE" 
                            values={{ 
                                balance: SelectedOtpItems[0].Due,
                            }}
                            children={(errorOTPMessage) => <p dangerouslySetInnerHTML={{ __html: errorOTPMessage }} />}
                        />
                    }

                    {SomeOtpItemsFailed &&
                        <span dangerouslySetInnerHTML={{ __html: ALERT_ERROR_HEADING_SOME_BALANCE}}></span>
                    }

                    {AllOtpItemsFailed &&
                        (failedOtpItems.length > 1
                        ? <span dangerouslySetInnerHTML={{ __html: ALERT_ERROR_OTP_ALL_BALANCE}}></span>
                        : <span dangerouslySetInnerHTML={{ __html: ALERT_ERROR_OTP_ALL_BALANCE_SINGULAR}}></span>)
                    } */}
        </Heading>
        <div className="!payment-border-none payment-mt-12">
          <AlertNotificationList 
            label="" >
            {regFailedItems.map((item:any) => (
              <AlertNotificationListItem
                label={getPaymentItemAccountType(item.AccountType,false)}
                labelDescription={item.Ban}
                variant="regErrorBanList"
              />
            ))}
          </AlertNotificationList>
        </div>
      </Text>
    </Alert>
  );
};

export const AlertRegFailure = injectIntl(AlertRegFailureComponent);
