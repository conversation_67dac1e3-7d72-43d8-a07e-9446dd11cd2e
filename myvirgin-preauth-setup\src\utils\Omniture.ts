// s_oPYM:bank, Credit
// s_oCCDT:credit card type
import { PaymentItem } from "myvirgin-preauth-common"; // Ensure this path is correct or update it to the correct path
import { IBankInfoRes, InputBankAccountDetail } from "../models";

export const cardPatterns = {
  VISA: /^4/,
  MASTERCARD: /^(5[1-5]|2[2-7])/,
  AMEX: /^3[47]/,
};

export const getCardType = (number: string) => {
  if (cardPatterns.VISA.test(number)) return "VISA";
  if (cardPatterns.MASTERCARD.test(number)) return "MASTERCARD";
  if (cardPatterns.AMEX.test(number)) return "AMEX";
  return " "; // Return default for unknown patterns
};

export const getConfirmationOmniture = (
  isBankpaymentSelscted: boolean,
  showCurrentBalance: boolean,
  checkedCurrentBalanceItems: PaymentItem[],
  paymentItem: PaymentItem[],
  language:string,
  submitMultiOrderPayment: any,
  CreditCardType?: string,
) => {
  
  if (isBankpaymentSelscted) {
    if (showCurrentBalance) {
      if (checkedCurrentBalanceItems.length > 0) {
        return {
          s_oPYM: "Bank payment:optin",
          s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
        };
      } else if (checkedCurrentBalanceItems.length === 0) {
        return {
          s_oPYM: "Bank payment:optout",
          s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
        };
      }
    } else {
      return {
        s_oPYM: "Bank payment",
        s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
      };
    }
  } else if (!isBankpaymentSelscted) {
    if (showCurrentBalance) {
      if (checkedCurrentBalanceItems.length > 0) {
        return {
          s_oPYM: "Creditcard:optin",
          s_oCCDT: { CreditCardType },
          s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber,
        };
      } else if (checkedCurrentBalanceItems.length === 0) {
        return {
          s_oPYM: "Creditcard:optout",
          s_oCCDT: { CreditCardType },
          s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber
        };
      }
    } else {
      return {
        s_oPYM: "Creditcard",
        s_oCCDT: { CreditCardType },
        s_OPID: submitMultiOrderPayment[0].PaymentConfirmationNumber
      };
    }
  }

  return {
    s_oPYM: "",
    s_oCCDT: "",
  };
};

export const getOmnitureOnConfirmationFailure = (
  isBankpaymentSelscted: boolean,
  checkedBillItems: PaymentItem[],
  CreditCardType?: any
) => {

  if (isBankpaymentSelscted) {
    if (checkedBillItems.length >0 && checkedBillItems[0].BankAccountDetails) {
      return {
        s_oPYM: "Bank payment",
        s_oAPT: "327-2-2",
      };
    } else if (checkedBillItems.length >0 && checkedBillItems[0].CreditCardDetails) {
      return {
        s_oPYM: "Bank payment",
        s_oAPT: "331-2-2",
      };
    }
  } else if (!isBankpaymentSelscted) {
    if (checkedBillItems.length >0 && checkedBillItems[0].BankAccountDetails) {
      return {
        s_oPYM: "Credit card",
        s_oAPT: "328-2-2",
        s_oCCDT: { CreditCardType },
      };
    } else if (checkedBillItems.length >0 && checkedBillItems[0].CreditCardDetails) {
      return {
        s_oPYM: "Credit card",
        s_oAPT: "330-2-2",
        s_oCCDT: { CreditCardType },
      };
    }
  }

  return {
    s_oPYM: "",
    s_oAPT: "",
    s_oCCDT: "",
  };

};

export const hasInteracValueChanged = (bankInfo: IBankInfoRes, bankAccountDetail?: InputBankAccountDetail) => {
  let result = false;
  if(bankInfo.bankAccountNumber !== "" && bankAccountDetail && bankAccountDetail.AccountNumber !== "")
  {
    if(bankAccountDetail.AccountHolder !== bankInfo.accountHolderName || bankAccountDetail.BankName !== bankInfo.bankCode || bankAccountDetail.TransitNumber !== bankInfo.transitNumber || bankAccountDetail.AccountNumber !== bankInfo.bankAccountNumber)
    {
      result = true;
    }
  }
  return result;
};

export const IsDetailsValid = (validateMultiOrderPayment: any) => {
  let result = false ;
  if (Object.values(validateMultiOrderPayment).length > 0) {
    for (const item of Object.values(validateMultiOrderPayment) as any) {
      if (item?.errorCodeID && item?.errorCodeID !== "") {
        result = true;
        break;
      }
    }
  }
  return result;
};
