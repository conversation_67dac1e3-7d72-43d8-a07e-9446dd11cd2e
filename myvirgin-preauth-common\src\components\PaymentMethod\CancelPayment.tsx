import * as React from "react";
import { injectIntl } from "react-intl";
import { PaymentRadioCard } from "./PaymentMethodRadio";
import {NotifCard} from "../NotifCard";
import { Heading, ListItem, Price, Text , Alert } from "@virgin/virgin-react-ui-library";
import {  AlertNotificationListItem } from "../Alert/AlertNotificationListItem";
import { AlertNotificationList } from "../Alert/AlertNotificationList";


interface CancelPaymentProps {
  intl: any;
  children: React.ReactNode;
  Checked: boolean;
}

const CancelPaymentComponent = ({ intl, children, Checked }:CancelPaymentProps) => {

  const CANCEL_PAYMENT_LABEL = intl.formatMessage({id: "CANCEL_PAYMENT_LABEL"});

  return (
    <div className="vrui-mb-15">
      <PaymentRadioCard 
        id="payment-radio-cancel" 
        name="payment-radio" 
        label={CANCEL_PAYMENT_LABEL}
        headingLevel="h3"
        defaultChecked={Checked ? true : undefined}
      >
        <div>
          <Heading
            variant="default"
            level="h4"
            className="vrui-font-sans vrui-text-black vrui-text-18 vrui-text-left vrui-leading-22 vrui-font-bold"
          >
            Are you sure you want to cancel?
          </Heading>
          <span className="vrui-text-gray vrui-text-14 vrui-leading-18 vrui-mt-5">
            Pre-authorized payments are the best way to ensure your Bell bill is always paid on time, and avoids late fees.
          </span>
        </div>

        {/* Warning message - Final payment */}
        <div className="vrui-pt-30">
          <Alert
            variant="warning"
            iconSize="36"
            className="sm:vrui-flex sm:vrui-border sm:vrui-border-gray-4 sm:payment-rounded-[16px] sm:vrui-p-30"
          >
            <div className="vrui-w-full vrui-mt-15 sm:vrui-mt-0 sm:vrui-ml-15">
              <div>
                <Heading
                  variant="default"
                  level="h4"
                  className="vrui-font-sans vrui-text-black vrui-text-18 vrui-text-left vrui-leading-22 vrui-font-bold"
                >
                  Final payment
                </Heading>
                <Text elementType="span" className="vrui-text-14 vrui-text-gray vrui-leading-18 vrui-mt-5">
                  One final pre-authorized payment will be withdrawn for the following:
                </Text>
              </div>

              <AlertNotificationList className="vrui-mt-15">
                <AlertNotificationListItem
                  variant="accountList"
                  label="MyBill"
                  labelDescription="Home"
                  cardDetails="American Express ending in 1234, valid until 00/00"
                />
              </AlertNotificationList>
            </div>
          </Alert>
        </div>

        {/* Autopay credits removed message */}
        <div className="vrui-pt-15 sm:vrui-pt-30">
          <NotifCard
            hasNotifCard
            variant="notifCardWarning"
            label={<span className='vrui-font-bold vrui-text-black'>Your autopay credit(s) will be removed</span>}
            label1
            label2
            label3
          >
            <ul className="vrui-list-disc vrui-list-inside vrui-mb-15">
              <ListItem className="vrui-text-14 vrui-text-gray vrui-leading-18">
                (555) 555-5555 -&nbsp;<Price className="vrui-inline vrui-lowercase !vrui-text-14 vrui-font-normal vrui-text-gray" price={3.00} variant="defaultPrice" suffixText="perMonth" language="en"></Price>
              </ListItem>
              <ListItem className="vrui-text-14 vrui-text-gray vrui-leading-18">
                (555) 555-5555 -&nbsp;<Price className="vrui-inline vrui-lowercase !vrui-text-14 vrui-font-normal vrui-text-gray" price={3.00} variant="defaultPrice" suffixText="perMonth" language="en"></Price>
              </ListItem>
            </ul>
          </NotifCard>
        </div>
      </PaymentRadioCard>
    </div>
  );
};

export const CancelPayment = injectIntl(CancelPaymentComponent);
