import * as React from "react";
import { <PERSON><PERSON>, <PERSON>ing, <PERSON>, <PERSON><PERSON>, Text, Loader } from "@virgin/virgin-react-ui-library";
import { FormattedMessage, injectIntl } from "react-intl";
import { SingleRowInformation } from "../SingleRowInformation/SingleRowInformation";
import { MultiBanInformation, SummaryInformationHeading } from "../SummaryInformationHeading";
import { AlertConfirmationSuccess, AlertErrorOneTimePayment, AlertRegFailure } from "../Alert";
import {AlertWarningWithSomeBalancesPaidMulti} from "../Alert/AlertWarningWithSomeBalance";
import { PaymentItem, InputBankAccountDetail, SelectListItem, PaymentItemAccountTypeName, PaymentItemAccountType, AccountInputValues, CurrentSection, SubscriberOffersWithBan} from "../../models";
import { PaymentSummary } from "../PaymentSummary/PaymentSummary";
import { State } from "../../store";
import { connect } from "react-redux";
import { getItemAccountTypeName } from "../../utils";
import { AlertConfirmationInfo } from "../Alert/AlertConfirmationInfo";
import { IRequestStatus } from "../../models/App";
import { OmnitureOnConfirmation, apiConfirmationStatus, OmnitureOnConfirmationFailure, OmnitureOnOneTimePaymentFailure } from "../../store/Actions";
import { getConfirmationSetUpOmniture, getConfirmationManageOmniture, getOmnitureOnManageConfirmationFailure } from "../../utils/Omniture";
import { formatNumber } from "../../utils/PaymentItemUtils";

interface ConfirmationComponentProps {
  intl: any;
  paymentItem: PaymentItem[];
  checkedBillItems: PaymentItem[];
  checkedCurrentBalanceItems: PaymentItem[];
  showPaymentSummary: boolean;
  isPreauth: boolean;
  isNewbank: boolean;
  inputValue: any;
  inputBankValue?: InputBankAccountDetail;
  isShow: boolean; // to be cofirm with dave
  isBankPaymentSelected: boolean;
  submitMultiOrderPayment: any;
  submitMultiOrderFormStatus: IRequestStatus;
  BankList: SelectListItem[];
  showCurrentBalance: boolean;
  accountInputValues: AccountInputValues[];
  currentSection: CurrentSection;
  language: "en" | "fr";
  notOptedBalanceItems: PaymentItem[];
  setApiSatusIsFailed: (isFailed: boolean) => void;
  creditCardAutopayOffers: SubscriberOffersWithBan[];
  debitCardAutopayOffers: SubscriberOffersWithBan[];
  bankitems: any[];
  setOmnitureOnConfirmation: Function,
  setApiConfirmationStatus: Function;
  apiSatusIsFailed: boolean;
  managePreauth: string;
  setOmnitureOnConfirmationFailure: Function;
  setOmnitureOnOneTimePaymentFailure: Function;
  interacCode: string;
}

const ConfirmationComponent = ({ intl,
  paymentItem,
  checkedBillItems,
  checkedCurrentBalanceItems,
  showPaymentSummary,
  isPreauth,
  isNewbank,inputValue,
  inputBankValue, 
  isShow, 
  isBankPaymentSelected, 
  submitMultiOrderPayment,
  submitMultiOrderFormStatus,
  accountInputValues,
  BankList, 
  showCurrentBalance,
  currentSection, 
  language,
  notOptedBalanceItems,
  setApiSatusIsFailed,
  creditCardAutopayOffers,
  debitCardAutopayOffers,
  bankitems,
  setOmnitureOnConfirmation,
  setApiConfirmationStatus,
  apiSatusIsFailed,
  managePreauth,
  setOmnitureOnConfirmationFailure,
  setOmnitureOnOneTimePaymentFailure,
  interacCode
}: ConfirmationComponentProps) => {
  const accountTypename : PaymentItemAccountTypeName = {
    MyBill: intl.formatMessage({id: "ACCOUNT_TYPENAME_MYBILL"}),
    Mobility: intl.formatMessage({id: "ACCOUNT_TYPENAME_MOBILITY"}),
    OneBill: intl.formatMessage({id: "ACCOUNT_TYPENAME_ONEBILL"}),
    TV: intl.formatMessage({id: "ACCOUNT_TYPENAME_TV"}),
    Internet: intl.formatMessage({id: "ACCOUNT_TYPENAME_INTERNET"}),
    HomePhone: intl.formatMessage({id: "ACCOUNT_TYPENAME_HOMEPHONE"}),
    MobilityAndOneBill: intl.formatMessage({id: "ACCOUNT_TYPENAME_MOBILITY_ONEBILL"}),
    SingleBan: intl.formatMessage({id: "ACCOUNT_TYPENAME_SINGLEBAN"})
  };
  const checkeddebitOffers = () => {
    const filteredOffer: any = [];
    debitCardAutopayOffers && debitCardAutopayOffers?.map((item) => {
      checkedBillItems && checkedBillItems.map((billItem) => {
        if (item.Ban === billItem.BillName) {
          filteredOffer.push(item);
        }
      });
    });

    return filteredOffer;
  };

  const checkedcreditOffers = () => {
    const filteredOffer: any = [];
    creditCardAutopayOffers && creditCardAutopayOffers?.map((item) => {
      checkedBillItems && checkedBillItems.map((billItem) => {
        if (item.Ban === billItem.BillName) {
          filteredOffer.push(item);
        }
      });
    });

    return filteredOffer;
  };
  const autopayOffers =
    {
      label: intl.formatMessage({ id: "PAYMENT_METHOD" }),
      debits: isBankPaymentSelected ? paymentItem && paymentItem.length > 1 ? checkeddebitOffers() : debitCardAutopayOffers : null,
      credits: !isBankPaymentSelected ? paymentItem && paymentItem.length > 1 ? checkedcreditOffers() : creditCardAutopayOffers : null,
    };

  const isshowAutopaysuccess =
    {
      show: autopayOffers && autopayOffers.credits && autopayOffers.credits.length > 0 && autopayOffers.credits[0].AutopayEligibleSubscribers && autopayOffers.credits[0].AutopayEligibleSubscribers.length > 0 ||
              autopayOffers && autopayOffers.debits && autopayOffers.debits.length > 0 && autopayOffers.debits[0].AutopayEligibleSubscribers && autopayOffers.debits[0].AutopayEligibleSubscribers.length > 0
    };
  const checkedBanOffers = () => {
    const filteredOffer: any = [];
    !isBankPaymentSelected ?
      creditCardAutopayOffers && creditCardAutopayOffers?.map((item) => {
        checkedBillItems && checkedBillItems?.map((billItem) => {
          if (item.Ban === billItem.BillName) {
            filteredOffer.push(item);
          }
        });
      }):
      debitCardAutopayOffers && debitCardAutopayOffers?.map((item) =>{
        checkedBillItems && checkedBillItems.map((billItem)=>{
          if(item.Ban === billItem.BillName){
            filteredOffer.push(item);
          }
        });
      });
    return filteredOffer;
  };
  const getTotalOffers = () => paymentItem && paymentItem.length > 1 ? checkedBanOffers().reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {
         
    const offersCount = ban.AutopayEligibleSubscribers.reduce(
      (count, subscriber) => count + subscriber.autopayOffers.length,
      0
    );
    return total + offersCount;
  }, 0): !isBankPaymentSelected ? creditCardAutopayOffers && creditCardAutopayOffers.reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {
         
    const offersCount = ban.AutopayEligibleSubscribers.reduce(
      (count, subscriber) => count + subscriber.autopayOffers.length,
      0
    );
    return total + offersCount;
  }, 0): debitCardAutopayOffers && debitCardAutopayOffers.reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {
         
    const offersCount = ban.AutopayEligibleSubscribers.reduce(
      (count, subscriber) => count + subscriber.autopayOffers.length,
      0
    );
    return total + offersCount;
  }, 0);
  const debitOffers =      
{
  label: intl.formatMessage({id: "PAYMENT_METHOD"}),            
  credits: paymentItem && paymentItem.length > 1 ? checkedBanOffers() : debitCardAutopayOffers
};
  const CreditOffers =
{
  label: intl.formatMessage({ id: "PAYMENT_METHOD" }),
  credits: paymentItem && paymentItem.length > 1 ? checkedBanOffers() : creditCardAutopayOffers
};
  const offerImpactGain = (): boolean => 
    debitOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => (credit.offerImpact === "GAIN" ||credit.offerImpact === "" || credit.offerImpact === null)&&(credit.offerImpact !== "RETAIN")) 
      )
    ) || false // Return false if no match found
      ;
  const offerImpactGainCredit = (): boolean => 
    CreditOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => (credit.offerImpact === "GAIN" ||credit.offerImpact === "" || credit.offerImpact === null)&&(credit.offerImpact !== "RETAIN")) 
      )
    ) || false // Return false if no match found
      ;
     
  const offerImpactRemoved = (): boolean => 
    debitOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => credit.offerImpact === "REMOVE")
      )
    ) || false // Return false if no match found
      ;
  const offerImpactRemovedCredit = (): boolean => 
    CreditOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => credit.offerImpact === "REMOVE")
      )
    ) || false // Return false if no match found
      ;
  const offerImpactReduce = (): boolean => 
    debitOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => credit.offerImpact === "REDUCE")
      )
    ) || false // Return false if no match found
      ;
  const offerImpactIncrease = (): boolean => 
    CreditOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => credit.offerImpact === "INCREASE")
      )
    ) || false // Return false if no match found
      ;
  const offerImpactIncreaseDebit = (): boolean => 
    debitOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => credit.offerImpact === "INCREASE")
      )
    ) || false // Return false if no match found
      ;
  const offerImpactReduceCredit = (): boolean => 
    CreditOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => credit.offerImpact === "REDUCE")
      )
    ) || false // Return false if no match found
      ;
      
      
  const reduce =      
    {
      
      label: getTotalOffers() >1 ?intl.formatMessage({id: "LABEL_LOADED_OFFERS_REDUCED_DEBIT_TITLE"}): intl.formatMessage({id: "LABEL_LOADED_OFFER_REDUCED_DEBIT_TITLE"})          
      
    };
    // const gain =      
    // {
      
  //   label:  getTotalOffers() >1 ?intl.formatMessage({id:"LABEL_LOADED_OFFERS_DEBIT_TITLE_CONFIRMATION"}): intl.formatMessage({id:"LABEL_LOADED_OFFER_DEBIT_TITLE_CONFIRMATION"})          
      
  // };
  const remove =      
    {
      
      label: getTotalOffers() >1 ?intl.formatMessage({id: "LABEL_LOADED_OFFERS_REMOVED_DEBIT_TITLE_CONFIRMATION"}): intl.formatMessage({id: "LABEL_LOADED_OFFER_REMOVED_DEBIT_TITLE_CONFIRMATION"})          
      
    };
  const increase =      
    {
      
      label: getTotalOffers() >1 ?intl.formatMessage({id: "LABEL_LOADED_OFFERS_INCREASED_CREDIT_TITLE_CONFIRMATION"}): intl.formatMessage({id: "LABEL_LOADED_OFFER_INCREASED_CREDIT_TITLE_CONFIRMATION"})          
      
    };
  const PaymentInformationItems = checkedBillItems.map((item) => ({ 
    label: <FormattedMessage 
      id="SELECT_BILLS_ACCOUNT_TITLE"
      values={{
        accounttype: getItemAccountTypeName(item.AccountType,item.IsNM1Account,accountTypename)
      }} />,
    value: item.NickName,
    key: getItemAccountTypeName(item.AccountType,item.IsNM1Account,accountTypename),
  })
  );
  const groupedDebitOffers = (offerType) => debitOffers?.credits?.reduce((acc: any, debit: any) => {
    debit.AutopayEligibleSubscribers.forEach((item: any) => {
      if (!acc[debit.banInfo.nickName]) {
        acc[debit.banInfo.nickName] = [];
      }
      item.autopayOffers.forEach((credit: any) => {
        if (credit.offerImpact !== "RETAIN" && credit.offerImpact === offerType) {
          acc[debit.banInfo.nickName].push({
            phoneNumber: item.subscriberTelephoneNumber,
            discountAmount: credit.discountAmount,
            currentDiscountAmount: credit.currentdiscountAmount,
          });
        }
      });
    });
    return acc;
  }, {});
  const groupedCreditOffers = (offerType) => CreditOffers?.credits?.reduce((acc: any, debit: any) => {
    debit.AutopayEligibleSubscribers.forEach((item: any) => {
      if (!acc[debit.banInfo.nickName]) {
        acc[debit.banInfo.nickName] = [];
      }
      item.autopayOffers.forEach((credit: any) => {
        if (credit.offerImpact !== "RETAIN" && credit.offerImpact === offerType) {
          acc[debit.banInfo.nickName].push({
            phoneNumber: item.subscriberTelephoneNumber,
            discountAmount: credit.discountAmount,
            currentDiscountAmount: credit.currentdiscountAmount,
          });
        }
      });
    });
    return acc;
  }, {});
  const notPaidCurrentBalance: PaymentItem[] = checkedBillItems.filter((item) => !checkedCurrentBalanceItems.includes(item) && item.Due > 0);
   


  const containerRef = React.useRef<HTMLDivElement | null>(null);


  const handleBackClick = () => {
    window.location.href = '/';
  };

  const PAY_CURRENT_BALANCE_OPTED_IN = (isBankPaymentSelected ? intl.formatMessage(
    { id: "PAY_CURRENT_BALANCE_OPTED_IN" },
    { balance: new Intl.NumberFormat(language, { style: "currency", currency: "USD", currencyDisplay: "narrowSymbol", }).format(0.00) }) 
    :
    (currentSection === CurrentSection.Confirmation ? "" :intl.formatMessage(
      { id: "PAY_CURRENT_BALANCE_OPTED_IN_PACC" })
    )
  );
  const PAY_CURRENT_BALANCE_OPTED_OUT_SINGULAR = intl.formatMessage({ id: "PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_OUT_SINGULAR" });
  const PAY_CURRENT_BALANCE_OPTED_OUT_PLURAL = intl.formatMessage({ id: "PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_OUT_PLURAL" });
    
  const currentBalancesisOpted  = () => {
    if (checkedCurrentBalanceItems.length === 0) {
      if (checkedBillItems.length - checkedCurrentBalanceItems.length > 1){ // none opted
        return PAY_CURRENT_BALANCE_OPTED_OUT_PLURAL;
      } else {
        return PAY_CURRENT_BALANCE_OPTED_OUT_SINGULAR;
      }
    }
    else if(checkedCurrentBalanceItems.every(item => checkedBillItems.includes(item))){ // all opted in
      return PAY_CURRENT_BALANCE_OPTED_IN;
    } else if (checkedCurrentBalanceItems.some(item => checkedBillItems.includes(item))) { // some opted in
      return PAY_CURRENT_BALANCE_OPTED_IN; // update erd for some opted in
    }
  };
  const [showAlertErrorOneTimePayment, setShowAlertErrorOneTimePayment] = React.useState(false);

  const isManage = managePreauth && managePreauth !== null;

  const getOmnitureOnConfirmFailure = () => {
    if(currentSection === CurrentSection.Confirmation)
      if(!isManage)
      {
        setOmnitureOnConfirmationFailure();
      } 
      else {
        if(isBankPaymentSelected)
        {
          setOmnitureOnConfirmationFailure(getOmnitureOnManageConfirmationFailure(true , checkedBillItems, paymentItem));
        }
        else{
          setOmnitureOnConfirmationFailure(getOmnitureOnManageConfirmationFailure(false , checkedBillItems , paymentItem, inputValue.cardType));
        }
      }
  };

  React.useEffect(() => {
    if (submitMultiOrderFormStatus === IRequestStatus.FAILED){
      setApiSatusIsFailed(true);
      setApiConfirmationStatus("FAILED");
      getOmnitureOnConfirmFailure();
    } else if (paymentItem.length === 1){
      if(submitMultiOrderFormStatus === IRequestStatus.COMPLETED && Object.values(submitMultiOrderPayment).length > 0){
        for (const item of Object.values(submitMultiOrderPayment) as any) {
          if (item.ErrorCodeID.trim() !== "") {
            setApiSatusIsFailed(true);
            setApiConfirmationStatus("FAILED");
            getOmnitureOnConfirmFailure();
            break; 
          } else {  // added for action call to get the icon on confirmation page
            setApiConfirmationStatus("COMPLETED");
            break;
          }
        }
      }
    }

    if (submitMultiOrderFormStatus === IRequestStatus.COMPLETED && Object.values(submitMultiOrderPayment).length > 0 && paymentItem.length > 1) {
      if (!apiSatusIsFailed) {
        setApiConfirmationStatus("COMPLETED");
      }
    }

    if (submitMultiOrderFormStatus === IRequestStatus.COMPLETED && Object.values(submitMultiOrderPayment).length > 0) {
      if (isManage) {
        if(!apiSatusIsFailed){
          if(isBankPaymentSelected)
          {
            setOmnitureOnConfirmation(getConfirmationManageOmniture(isBankPaymentSelected, checkedBillItems, Object.values(submitMultiOrderPayment), paymentItem, "", interacCode));
          }
          else
          {
            setOmnitureOnConfirmation(getConfirmationManageOmniture(isBankPaymentSelected, checkedBillItems, Object.values(submitMultiOrderPayment), paymentItem, inputValue.cardType));
          }
        }
      } else {
        if (isShow && !apiSatusIsFailed) {
          if (isBankPaymentSelected) 
          {
            setOmnitureOnConfirmation(getConfirmationSetUpOmniture(isBankPaymentSelected, showCurrentBalance, checkedCurrentBalanceItems, Object.values(submitMultiOrderPayment), interacCode));
          }
          else 
          {
            setOmnitureOnConfirmation(getConfirmationSetUpOmniture(isBankPaymentSelected, showCurrentBalance, checkedCurrentBalanceItems, Object.values(submitMultiOrderPayment), "", inputValue.cardType));
          }
        }
      }
    }
        
    if (submitMultiOrderFormStatus === IRequestStatus.COMPLETED && containerRef.current){
      // For reading heading and title in confirmation in pageload only accessibility
      // const firstFocusableElement = containerRef.current.querySelector('.payment-focus-sr');
      // if (firstFocusableElement) {
      // firstFocusableElement.scrollIntoView({ behavior: 'smooth' });
      // (firstFocusableElement as HTMLElement).focus();
      // }

      // setTimeout(()=>{
      //     const headingFocusableElement = containerRef?.current?.querySelector('.payment-focus-heading');
      //     if (headingFocusableElement) {
      //         headingFocusableElement.scrollIntoView({ behavior: 'smooth' });
      //         (headingFocusableElement as HTMLElement).focus();
      //     }           
      // },100)

      // setTimeout(()=>{
      //     const firstFocusableElement = containerRef?.current?.querySelector('.payment-focus-sr') as HTMLElement;
      //     if (firstFocusableElement) {
      //         firstFocusableElement.style.display = 'none';
      //     }
      // },1000)

      setShowAlertErrorOneTimePayment(true);
    }
  }, [submitMultiOrderFormStatus]); 

  React.useEffect(()=> {
    
  },[submitMultiOrderFormStatus]);

  React.useEffect(()=> {
    
  },[submitMultiOrderPayment]);
    
  const confirmationHeadingTitle = () => {
    if (Object.values(submitMultiOrderPayment).some((item:any) => item?.otp && !item?.otp.isSuccess)){
      return   intl.formatMessage({ id: "TRANSACTION_SUBMITTED_HEADING" });
    }else{
      return   intl.formatMessage({ id: "CONFIRMATION_HEADING" });
    }   
  };
  const confirmationPayAmountLabel = () => {
    if (Object.values(submitMultiOrderPayment).some((item:any) => item?.otp && !item?.otp.isSuccess)){
      return   intl.formatMessage({ id: "UNPAID_BALANCE" });
    }else{
      return   intl.formatMessage({ id: "PAYMENT_AMOUNT" });
    }   
  };
    
  const [hasSomeOtpErrors, setSomeOtpErrors] = React.useState(false);
  const [failedOtpItems, setFailedOtpItems] = React.useState([]);
  React.useEffect(() => {
    if (Object.values(failedOtpItems).length === 0) {
      const OrderItemsFailed = Object.values(submitMultiOrderPayment).filter((item:any) => item?.otp && !item?.otp.isSuccess);
      const failedOrderFormIdList = OrderItemsFailed.map((item:any) => item?.OrderFormId);
      const failedOrderItems = accountInputValues
        .filter((x) => failedOrderFormIdList.includes(x.transactionID))
        .map((x) => x.accountNumber);
      const failedOtpItems:any = checkedCurrentBalanceItems.filter((item) => failedOrderItems.includes(item.Ban));

      if (checkedCurrentBalanceItems.length > failedOtpItems.length && checkedCurrentBalanceItems.length !== 1) {
        setSomeOtpErrors(true);
      } else {
        setSomeOtpErrors(false);
      }

      setFailedOtpItems(failedOtpItems);
    }
  },[failedOtpItems]);

  const LOADER_SUBMIT = intl.formatMessage({ id: "LOADER_SUBMIT" });
  const LOADER_SUBMIT_DESC = intl.formatMessage({ id: "LOADER_SUBMIT_DESC" });

  return (
    <>
      {submitMultiOrderFormStatus === IRequestStatus.PENDING &&
                <Loader text={"<div className='vrui-font-bold'>"+LOADER_SUBMIT+"</div><div>"+LOADER_SUBMIT_DESC+"</div>"} />
      }

      {(Object.values(submitMultiOrderPayment).length > 0 && submitMultiOrderFormStatus === IRequestStatus.COMPLETED) &&
        
            <div className="payment-border-gray-4" ref={containerRef}>
              <div className="payment-mb-15 payment-flex payment-items-center payment-justify-between payment-mt-44" id="ConfirmationDivID">
                {/* Additional tag for SR only reading*/}
                <span className="payment-focus-sr payment-sr-only" id="pageConfirmationId"  tabIndex={-1}>{intl.formatMessage({id: "PAGE_TITLE_CONFIRMATON"})}</span>
                <Heading className="payment-focus-heading" level="h2" variant="lg" tabIndex={-1}>
                  {confirmationHeadingTitle()}
                </Heading>
              </div>

              {(!isBankPaymentSelected && Object.values(submitMultiOrderPayment).length > 0 && Object.values(submitMultiOrderPayment).some((item:any) => item?.otp && !item?.otp.isSuccess) && showAlertErrorOneTimePayment) && (
                <div id= "OtpFailure" className="payment-pt-15 payment-pb-0">
                  <AlertErrorOneTimePayment 
                    checkedCurrentBalanceItems={checkedCurrentBalanceItems}
                    submitMultiOrderPayment={Object.values(submitMultiOrderPayment)} 
                    accountInputValue={accountInputValues} 
                    language={language}
                    notOptedBalanceItems={notOptedBalanceItems} 
                    checkedBillItems={checkedBillItems}
                    setOmnitureOnOneTimePaymentFailure={setOmnitureOnOneTimePaymentFailure}
                    isManage={isManage}
                  />
                </div>
              )}

              {(isBankPaymentSelected && Object.values(submitMultiOrderPayment).length > 0 && Object.values(submitMultiOrderPayment).some((item:any) => item?.RegisterPADStatus !== undefined && item?.RegisterPADStatus !== 1)) &&
                    <div id= "RegFailure" className="payment-pt-15 payment-pb-0">
                      <AlertRegFailure 
                        submitMultiOrderPayment={Object.values(submitMultiOrderPayment).filter((item:any) => item?.RegisterPADStatus !== 1)}  
                        accountInputValue={accountInputValues} 
                        language={language}
                        checkedBillItems={checkedBillItems}
                      />
                    </div>
              }

                
              {(!isBankPaymentSelected && Object.values(submitMultiOrderPayment).length > 0 && Object.values(submitMultiOrderPayment).some((item:any) => item?.RegisterPADStatus !== undefined && item?.RegisterPACCStatus !== 1)) &&
                    <div id= "RegFailure" className="payment-pt-15 payment-pb-0">
                      <AlertRegFailure 
                        submitMultiOrderPayment={Object.values(submitMultiOrderPayment).filter((item:any) => item?.RegisterPACCStatus !== 1)}  
                        accountInputValue={accountInputValues} 
                        language={language}
                        checkedBillItems={checkedBillItems}
                      />
                    </div>
              }

              <div id="ConfirmationSuccess" className="payment-pt-15 payment-mb-16 sm:payment-mb-15">
                <AlertConfirmationSuccess 
                  submitMultiOrderPayment={Object.values(submitMultiOrderPayment)} 
                  accountInputValue={accountInputValues} 
                  isBankPayment={isBankPaymentSelected}
                  checkedBillItems={checkedBillItems}
                  language={language}
                  paymentItem = {paymentItem}
                  creditCardAutopayOffers = {creditCardAutopayOffers}
                  debitCardAutopayOffers={debitCardAutopayOffers}/>
              </div>
                
                
            
              {(paymentItem.find(item => item.AccountType !== PaymentItemAccountType.OneBill) && notPaidCurrentBalance.length > 0 && Object.values(submitMultiOrderPayment).some((item:any) => item?.otp && !item?.otp.isSuccess) === false && !isManage)  && (
                <div id = "AvoidLatePayment" className="payment-mb-15">
                  <AlertWarningWithSomeBalancesPaidMulti
                    multiban={notOptedBalanceItems.length > 1 ? true : false}
                    submitMultiOrderPayment={Object.values(submitMultiOrderPayment)}
                    accountInputValue={accountInputValues} 
                    paymentItem={paymentItem}
                    language={language}
                  />
                </div>
              )}

                 
              {paymentItem.find(item => item.AccountType === PaymentItemAccountType.OneBill) && (
                <div  id="AvoidLatePayment" className="payment-mb-15" >
                  <AlertConfirmationInfo
                  />
                </div>
                
              )}
              <div className="payment-block payment-border payment-rounded-16 payment-mt-16 payment-relative payment-px-16 sm:payment-px-32 payment-py-32">
                <div>
                  <Heading level="h3" variant="md">
                    {intl.formatMessage({ id: "PAYMENT_SUMMARY_TITLE" })}
                  </Heading>
                </div>
                <div className="payment-mt-32 sm:payment-mt-40">
                  <SummaryInformationHeading
                    title={intl.formatMessage({ id: "BILL_INFORMATION_TITLE" })}
                  >
                    {PaymentInformationItems.map((item, index) => (
                      <SingleRowInformation
                        className={index > 0 ? "payment-mt-8" : ""}
                        label={item.label}
                        value={item.value}
                      />
                    ))}
                  </SummaryInformationHeading>
                </div>
                <div className="payment-mt-44">
                  <SummaryInformationHeading title={intl.formatMessage({ id: "PAYMENT_INFORMATION_TITLE" })}>
                    <PaymentSummary paymentItem={paymentItem} className={showPaymentSummary ? "" : "payment-hidden" } inputValue={inputValue} isNewbank={isNewbank} isPreauth={isPreauth} inputBankValue={inputBankValue} showHeading={false} isBankPaymentSelected={isBankPaymentSelected} bankList={BankList} debitCardAutopayOffers={debitCardAutopayOffers} creditCardAutopayOffers={creditCardAutopayOffers}
                      // creditCardAutopayOffers={creditCardAutopayOffers}
                      // debitCardAutopayOffers={debitCardAutopayOffers}
                      checkedBillItems={checkedBillItems}bankitems={bankitems}isConfirmation={true}></PaymentSummary>
                    {/* {PaymentInformationItems.map((item, index) => (
                                <SingleRowInformation
                                    className={index > 0 ? "payment-mt-5" : ""}
                                    label={item.label}
                                    value={item.value}
                                    role="listitem"
                                />
                            ))} */}
                  </SummaryInformationHeading>
                </div>
                {showCurrentBalance &&
                        <div className="payment-mt-44">
                          <SummaryInformationHeading title={intl.formatMessage({ id: "CURRENT_BALANCE_TITLE" })}>
                                
                            {(!isBankPaymentSelected && Object.values(submitMultiOrderPayment).length > 0 && Object.values(submitMultiOrderPayment).some((item:any) =>  item?.otp && !item?.otp.isSuccess) && !hasSomeOtpErrors)  && (
                              <div className="payment-flex payment-items-start payment-mb-15">
                                <Icon className="payment-text-16 payment-text-red payment-mr-8 payment-leading-19" iconClass="vi_vrui" iconName="vi_warning_c"></Icon>
                                <p className="payment-text-darkblue payment-text-14 payment-leading-19">{intl.formatMessage({ id: "SUMMARY_CURRENT_BAL_OTP_FAILURE" })}</p>
                              </div>
                            )}
                            <div className="payment-text-gray-4 payment-text-14 payment-mt-5">
                              <p className="payment-leading-19" dangerouslySetInnerHTML={{ __html: currentBalancesisOpted() }}/>
                            </div>
                            <div>
                              {paymentItem.length > 1 ?
                                <>
                                  {checkedCurrentBalanceItems.map((item) => (
                                    <MultiBanInformation accountinfo={item.NickName}  childrole="listitem" 
                                      className="first:payment-mt-15 payment-mb-15 last:payment-mb-0"
                                      isLabelOnError={(hasSomeOtpErrors && failedOtpItems.filter((x:any) => x.Ban === item.Ban).length > 0) ? true : false}
                                    >
                                      <>
                                        {(hasSomeOtpErrors && failedOtpItems.filter((x:any) => x.Ban === item.Ban).length > 0) && (
                                          <div className="payment-flex payment-items-start payment-mb-5">
                                            <Icon className="payment-text-15 payment-text-red payment-mr-10 payment-mt-2" iconClass="vi_vrui" iconName="vi_small_warning"></Icon>
                                            <p className="payment-text-red payment-text-14">{intl.formatMessage({ id: "SUMMARY_CURRENT_BAL_OTP_FAILURE" })}</p>
                                          </div>
                                        )}
                                        <SingleRowInformation
                                          label={failedOtpItems.filter((x:any) => x.Ban === item.Ban).length > 0
                                            ? intl.formatMessage({ id: "UNPAID_BALANCE" })
                                            : intl.formatMessage({ id: "PAYMENT_AMOUNT" })
                                          }
                                          value={
                                            <Price
                                              language={language}
                                              price={item.Due}
                                              variant="ordinaryPrice"
                                              className="!payment-text-14 payment-leading-19 payment-text-gray-4"/>
                                          }
                                          needSRText
                                          srText={`${item.Due} dollars`}
                                          className="payment-text-darkblue" 
                                          isMultiBan={true}/>
                                      </>
                                    </MultiBanInformation>
                                  ))} 
                                </> :
                                <>
                                  {checkedCurrentBalanceItems.map((item) => (
                                    <SingleRowInformation
                                      label={confirmationPayAmountLabel()}
                                      value={
                                        <Price
                                          language={language}
                                          price={item.Due}
                                          variant="ordinaryPrice"
                                          className="!payment-text-14 payment-leading-19 payment-text-gray-4"/>
                                      }
                                      needSRText
                                      srText={`${item.Due} dollars`}
                                      className="payment-text-darkblue first:payment-mt-15 payment-mb-15 last:payment-mb-0"
                                    />
                                  ))} 
                                </>
                              }
                                    
                              {(checkedCurrentBalanceItems.length > 0 && notOptedBalanceItems.length === 1) &&
                                        <div className="payment-mt-16">{PAY_CURRENT_BALANCE_OPTED_OUT_SINGULAR}</div>
                              }

                              {(checkedCurrentBalanceItems.length > 0 && notOptedBalanceItems.length > 1) &&
                                        <div className="payment-mt-16">{PAY_CURRENT_BALANCE_OPTED_OUT_PLURAL}</div>
                              }
                            </div>
                          </SummaryInformationHeading>
                        </div>
                }
                {isshowAutopaysuccess.show ?
                  isBankPaymentSelected ?
                    offerImpactGain() || offerImpactRemoved() || offerImpactIncreaseDebit() || offerImpactRemoved() ?
                      <div className="payment-mt-44">
                        <SummaryInformationHeading title={intl.formatMessage({ id: "AUTOPAY_CREDITS_TITLE" })}>
                          {offerImpactReduce()? <>
                            <Text elementType="div" className="payment-flex payment-items-center payment-mt-5 payment-pb-15">
                              <Icon className="payment-text-yellow-1 payment-text-16 payment-mr-5" iconClass="vi_vrui" iconName="vi_small_warning"></Icon>
                              <p className="payment-text-gray payment-text-14">{reduce.label} </p>
                            </Text>
                          </>
                            :null}
                          {/* {offerImpactGain()?<>
                               <Text elementType="div" className="payment-flex payment-items-center payment-mt-5 payment-pb-15">
                                    <Icon className="payment-text-15 payment-text-blue payment-mr-5" iconClass="vi_vrui" iconName="vi_deals"></Icon>
                                    <p className="payment-text-gray payment-text-14">{gain.label} </p>
                                </Text>
                                </>
                                :null} */}
                          {offerImpactRemoved()?
                            <>
                              <Text elementType="div" className="payment-flex payment-items-center payment-mt-5 payment-pb-15">
                                <Icon className="payment-text-yellow-1 payment-text-16 payment-mr-5" iconClass="vi_vrui" iconName="vi_small_warning"></Icon>
                                <p className="payment-text-gray payment-text-14">{remove.label} </p>
                              </Text>
                            </>
                            :null}
                          {offerImpactIncreaseDebit()? <>
                            <Text elementType="div" className="payment-flex payment-items-center payment-mt-5 payment-pb-15">
                              <Icon className="payment-text-15 payment-text-blue payment-mr-5" iconClass="vi_vrui" iconName="vi_deals"></Icon>
                              <p className="payment-text-gray payment-text-14">{increase.label} </p>
                            </Text>
                          </>
                            :null}
                          {offerImpactGain()?<>
                            {Object.entries(groupedDebitOffers("GAIN")).map(([ban, offers]: [string, any], banIndex) => (
                              <div key={ban}>
                                {paymentItem && paymentItem.length > 1 ? <p id={`list-title-${banIndex}`} className="payment-text-14 payment-font-bold">{ban}</p> : "" }
                                <ul className="payment-list-disc payment-list-inside payment-mb-10">
                                  {offers.map((offer: any, index: number) => (
                                    <SingleRowInformation 
                                      label={offer.phoneNumber} 
                                      value={ language === "en"? "$"+ offer.discountAmount.toFixed(2) + "/mo.": offer.discountAmount.toFixed(2) + " $/mois"} 
                                      needSRText 
                                      srText={language === "en" ? formatNumber(offer.discountAmount) + " dollars per month" : formatNumber(offer.discountAmount) + " dollars par mois"} 
                                      role="listitem" 
                                      isMultiBan={paymentItem.length > 1 ? true : false }
                                      className={paymentItem.length > 1 ? "payment-text-gray" : "payment-text-black"}
                                    />
                                                        
                                  ))}
                                </ul>
                              </div>
                            ))}  
                          </> : null}
                          {offerImpactIncreaseDebit() ?<>    
                            {Object.entries(groupedDebitOffers("INCREASE")).map(([ban, offers]: [string, any], banIndex) => (
                              <div key={ban}>
                                {paymentItem && paymentItem.length > 1 ? <p id={`list-title-${banIndex}`} className="payment-text-14 payment-font-bold">{ban}</p> : "" }
                                <ul className="payment-list-disc payment-list-inside payment-mb-10">
                                  {offers.map((offer: any, index: number) => (                                                          
                                    <SingleRowInformation
                                      label={<strong>{offer.phoneNumber}</strong>}
                                      value={<><div className="payment-inline-block">from <Price className="payment-text-gray payment-inline payment-lowercase !payment-text-14 payment-font-normal"
                                        price={offer.currentdiscountAmount} variant="defaultPrice" suffixText="perMonth" language={language}></Price> to &nbsp;<Price className="payment-inline payment-lowercase !payment-text-14 payment-font-normal"
                                        price={offer.discountAmount} variant="defaultPrice" suffixText="perMonth" language={language}></Price></div></>}
                                      role="listitem"
                                      isMultiBan={paymentItem.length > 1 ? true : false }
                                      className="payment-text-black"
                                    />
                                                        
                                  ))}
                                </ul>
                              </div>
                            ))}
                          </>:null}
                          {offerImpactReduce() ?<>
                            {Object.entries(groupedDebitOffers("REDUCE")).map(([ban, offers]: [string, any], banIndex) => (
                              <div key={ban}>
                                {paymentItem && paymentItem.length > 1 ? <p id={`list-title-${banIndex}`} className="payment-text-14 payment-font-bold">{ban}</p> : "" }
                                <ul className="payment-list-disc payment-list-inside payment-mb-10">
                                  {offers.map((offer: any, index: number) => (
                                    <SingleRowInformation
                                      label={<strong>{offer.phoneNumber}</strong>}
                                      value={<><div className="payment-inline-block">from <Price className="payment-text-gray payment-inline payment-lowercase !payment-text-14 payment-font-normal"
                                        price={offer.currentdiscountAmount} variant="defaultPrice" suffixText="perMonth" language={language}></Price> to &nbsp;<Price className="payment-inline payment-lowercase !payment-text-14 payment-font-normal"
                                        price={offer.discountAmount} variant="defaultPrice" suffixText="perMonth" language={language}></Price></div></>}
                                      role="listitem"
                                      isMultiBan={paymentItem.length > 1 ? true : false }
                                      className="payment-text-black"
                                    />                                
                                  ))}
                                </ul>
                              </div>
                            ))}                                          
                          </>:null}    
                          {offerImpactRemoved()?<>
                            {Object.entries(groupedDebitOffers("REMOVE")).map(([ban, offers]: [string, any], banIndex) => (
                              <div key={ban}>
                                {paymentItem && paymentItem.length > 1 ? <p id={`list-title-${banIndex}`} className="payment-text-14 payment-font-bold">{ban}</p> : "" }
                                <ul className="payment-list-disc payment-list-inside payment-mb-10">
                                  {offers.map((offer: any, index: number) => (
                                                 
                                    <SingleRowInformation 
                                      label={offer.phoneNumber} 
                                      value={ language === "en"? "$"+ offer.discountAmount.toFixed(2) + "/mo.": offer.discountAmount.toFixed(2) + " $/mois"} 
                                      needSRText 
                                      srText={language === "en" ? formatNumber(offer.discountAmount) + " dollars per month" : formatNumber(offer.discountAmount) + " dollars par mois"} 
                                      role="listitem" 
                                      isMultiBan={paymentItem.length > 1 ? true : false }
                                      className={paymentItem.length > 1 ? "payment-text-gray" : "payment-text-black"}
                                    />
                               
                                  ))}
                                </ul>
                              </div>
                            ))}
                                  
                          </> : null}           

                        </SummaryInformationHeading>                                
                        <div className="payment-mt-15">
                          <p
                            className="payment-text-gray-4 payment-text-12 payment-leading-14"
                          ><strong>{intl.formatMessage({ id: "MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE_HEADING" })} </strong>{intl.formatMessage({ id: "MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE" })}</p>

                        </div>
                      </div> : ""
                    : offerImpactIncrease() || offerImpactReduceCredit() || offerImpactGainCredit() || offerImpactRemovedCredit() ?
                      <div className="payment-mt-44">                              
                        <SummaryInformationHeading title={intl.formatMessage({ id: "AUTOPAY_CREDITS_TITLE" })}>
                          {offerImpactIncrease()? <>
                            <Text elementType="div" className="payment-flex payment-items-center payment-mt-5 payment-pb-15">
                              <Icon className="payment-text-yellow-1 payment-text-16 payment-mr-5" iconClass="vi_vrui" iconName="vi_small_warning"></Icon>
                              <p className="payment-text-gray payment-text-14">{increase.label} </p>
                            </Text>
                          </>
                            :null}
                          {offerImpactReduceCredit()?
                            <>
                              <Text elementType="div" className="payment-flex payment-items-center payment-mt-5 payment-pb-15">
                                <Icon className="payment-text-yellow-1 payment-text-16 payment-mr-5" iconClass="vi_vrui" iconName="vi_small_warning"></Icon>
                                <p className="payment-text-gray payment-text-14">{reduce.label} </p>
                              </Text>
                            </>
                            :null}
                          {/* {offerImpactGainCredit()?<>
                                    <Text elementType="div" className="payment-flex payment-items-center payment-mt-5 payment-pb-15">
                                        <Icon className="payment-text-15 payment-text-blue payment-mr-5" iconClass="vi_vrui" iconName="vi_deals"></Icon>
                                        <p className="payment-text-gray payment-text-14">{gain.label} </p>
                                    </Text>
                                </>
                                :null} */}
                          {offerImpactRemovedCredit()?
                            <>
                              <Text elementType="div" className="payment-flex payment-items-center payment-mt-5 payment-pb-15">
                                <Icon className="payment-text-yellow-1 payment-text-16 payment-mr-5" iconClass="vi_vrui" iconName="vi_small_warning"></Icon>
                                <p className="payment-text-gray payment-text-14">{remove.label} </p>
                              </Text>
                            </>
                            :null}
                          {offerImpactGainCredit()?<>
                            {Object.entries(groupedCreditOffers("GAIN")).map(([ban, offers]: [string, any], banIndex) => (
                              <div key={ban}>
                                {paymentItem && paymentItem.length > 1 ? <p id={`list-title-${banIndex}`} className="payment-text-14 payment-font-bold">{ban}</p> : "" }
                                <ul className="payment-list-disc payment-list-inside payment-mb-10">
                                  {offers.map((offer: any, index: number) => (
                                    <SingleRowInformation 
                                      label={offer.phoneNumber} 
                                      value={ language === "en"? "$"+ offer.discountAmount.toFixed(2) + "/mo.": offer.discountAmount.toFixed(2) + " $/mois"} 
                                      needSRText 
                                      srText={language === "en" ? formatNumber(offer.discountAmount) + " dollars per month" : formatNumber(offer.discountAmount) + " dollars par mois"} 
                                      role="listitem" 
                                      isMultiBan={paymentItem.length > 1 ? true : false }
                                      className={paymentItem.length > 1 ? "payment-text-gray" : "payment-text-black"}
                                    />
                                  ))}
                                </ul>
                              </div>
                            ))}                                   
                          </> : null}
                          {offerImpactIncrease() ?<>    
                            {Object.entries(groupedCreditOffers("INCREASE")).map(([ban, offers]: [string, any], banIndex) => (
                              <div key={ban}>
                                {paymentItem && paymentItem.length > 1 ? <p id={`list-title-${banIndex}`} className="payment-text-14 payment-font-bold">{ban}</p> : "" }
                                <ul className="payment-list-disc payment-list-inside payment-mb-10">
                                  {offers.map((offer: any, index: number) => (                                                   
                                    <SingleRowInformation
                                      label={<strong>{offer.phoneNumber}</strong>}
                                      value={<><div className="payment-inline-block">from <Price className="payment-text-gray payment-inline payment-lowercase !payment-text-14 payment-font-normal"
                                        price={offer.currentdiscountAmount} variant="defaultPrice" suffixText="perMonth" language={language}></Price> to &nbsp;<Price className="payment-inline payment-lowercase !payment-text-14 payment-font-normal"
                                        price={offer.discountAmount} variant="defaultPrice" suffixText="perMonth" language={language}></Price></div></>}
                                      role="listitem"
                                      isMultiBan={paymentItem.length > 1 ? true : false }
                                      className="payment-text-black"
                                    />
                                  ))}
                                </ul>
                              </div>
                            ))}                                         
                          </>:null}
                          {offerImpactReduceCredit() ?<>
                            {Object.entries(groupedCreditOffers("REDUCE")).map(([ban, offers]: [string, any], banIndex) => (
                              <div key={ban}>
                                {paymentItem && paymentItem.length > 1 ? <p id={`list-title-${banIndex}`} className="payment-text-14 payment-font-bold">{ban}</p> : "" }
                                <ul className="payment-list-disc payment-list-inside payment-mb-10">
                                  {offers.map((offer: any, index: number) => (    
                                    <SingleRowInformation
                                      label={<strong>{offer.phoneNumber}</strong>}
                                      value={<><div className="payment-inline-block">from <Price className="payment-text-gray payment-inline payment-lowercase !payment-text-14 payment-font-normal"
                                        price={offer.currentdiscountAmount} variant="defaultPrice" suffixText="perMonth" language={language}></Price> to &nbsp;<Price className="payment-inline payment-lowercase !payment-text-14 payment-font-normal"
                                        price={offer.discountAmount} variant="defaultPrice" suffixText="perMonth" language={language}></Price></div></>}
                                      role="listitem"
                                      isMultiBan={paymentItem.length > 1 ? true : false }
                                      className="payment-text-black"
                                    />                                                           
                                  ))}
                                </ul>
                              </div>
                            ))}                                     
                          </>:null}    
                          {offerImpactRemovedCredit()?<>
                            {Object.entries(groupedCreditOffers("REMOVE")).map(([ban, offers]: [string, any], banIndex) => (
                              <div key={ban}>
                                {paymentItem && paymentItem.length > 1 ? <p id={`list-title-${banIndex}`} className="payment-text-14 payment-font-bold">{ban}</p> : "" }
                                <ul className="payment-list-disc payment-list-inside payment-mb-10">
                                  {offers.map((offer: any, index: number) => (    
                                    <SingleRowInformation 
                                      label={offer.phoneNumber} 
                                      value={ language === "en"? "$"+ offer.discountAmount.toFixed(2) + "/mo.": offer.discountAmount.toFixed(2) + " $/mois"} 
                                      needSRText 
                                      srText={language === "en" ? formatNumber(offer.discountAmount) + " dollars per month" : formatNumber(offer.discountAmount) + " dollars par mois"} 
                                      role="listitem" 
                                      isMultiBan={paymentItem.length > 1 ? true : false }
                                      className={paymentItem.length > 1 ? "payment-text-gray" : "payment-text-black"}
                                    />
                                  ))}
                                </ul>
                              </div>
                            ))}
                          </> : null}          

                                           


                        </SummaryInformationHeading>
                        <div className="payment-mt-15">
                          <p
                            className="payment-text-gray-4 payment-text-12 payment-leading-14"
                          ><strong>{intl.formatMessage({ id: "MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE_HEADING" })} </strong>{intl.formatMessage({ id: "MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE" })}</p>

                        </div>
                      </div> : "" 
                  : ""}
              </div>
              <div className="payment-mt-32 payment-mb-44 sm:payment-mb-64">
                <Button size="regular" variant="outlinedBlack" onClick={handleBackClick} >
                  {intl.formatMessage({ id: "BACK_TO_MY_ACCOUNT" })}
                </Button>
              </div>
            </div>
      }
    </>
  );
};

const mapStateToProps = (state: State) => ({
  submitMultiOrderPayment: state.submitMultiOrderPayment,
  submitMultiOrderFormStatus: state.submitMultiOrderFormStatus,
});
const mapDispatchToProps = (dispatch: React.Dispatch<any>) => ({ 
  setOmnitureOnConfirmation: (data?: any) => dispatch(OmnitureOnConfirmation({data})),
  setApiConfirmationStatus: (type: any) => dispatch(apiConfirmationStatus({ type })),
  setOmnitureOnConfirmationFailure: (data? : any) => dispatch(OmnitureOnConfirmationFailure({data})),
  setOmnitureOnOneTimePaymentFailure: (data? : any) => dispatch(OmnitureOnOneTimePaymentFailure({data})),
});
 

export const Confirmation = connect(mapStateToProps, mapDispatchToProps)(injectIntl(ConfirmationComponent));
