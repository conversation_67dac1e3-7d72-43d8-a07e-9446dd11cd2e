import { ComponentPropsWithRef } from 'react';
import { PaymentItem } from "myvirgin-preauth-common"
import SubscriberOffersWithBan from './PreauthorizePayment';

export interface CreditCardPaymentProps extends ComponentPropsWithRef<"input"> {
  intl: any;
  Checked?: boolean;
  isPreauth?: boolean;
  errorCardNumber?: boolean;
  hasCreditCardDetails?: PaymentItem;
  errorCardName?: boolean;
  errorExpiryDate?: boolean;
  errorSecurityCode?: boolean;
  cardNumber?: string;
  cardType?: string;
  cardIcons: { [key: string]: string };
  bankitems?: any;
  radioRef?: React.RefObject<HTMLInputElement | null>;
  inputRefs: {
      inputCreditCardNumber: React.RefObject<HTMLInputElement | null>;
      inputCreditCardHolderName: React.RefObject<HTMLInputElement | null>;
      inputCreditCardExpiryMonth: React.RefObject<HTMLSelectElement | null>;
      inputCreditCardExpiryYear: React.RefObject<HTMLSelectElement | null>;
      inputCreditCardSecurityCode: React.RefObject<HTMLInputElement | null>;
  };
  handleBankRadioChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleCreditCardChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  isBankChecked?: boolean;
  handleMaskCVV?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  CVV?: string;
  creditCardAutopayOffers:SubscriberOffersWithBan[];
  debitCardAutopayOffers:SubscriberOffersWithBan[];
  checkedBillItems:PaymentItem[];
  language: "en" | "fr";
  managePreauth?: string;
}

