/*! myvirgin-preauth-manage-bundle (bundle) 1.0.0 | bwtk 6.1.0 | 2025-06-27T19:03:14.922Z */
var factory;factory=function(e,t,r){return function(){"use strict";function n(e){var t,r=a[e];return void 0!==r?r.exports:(t=a[e]={exports:{}},i[e](t,t.exports,n),t.exports)}var o,i=[,function(t){t.exports=e},function(e){e.exports=t},function(e){e.exports=r}],a={};return n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o={},function(){function e(e,t,o){var i=r(r({},e),{"loader.staticWidgetMappings":{"myvirgin-preauth-manage":{factory:function(){return n(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-manage'");throw e.code="MODULE_NOT_FOUND",e}()))},namespace:"Preauth/Manage"}}});(0,u.Init)(i),c.render(a.createElement(u.WidgetLoader,{widget:"myvirgin-preauth-manage"}),document.getElementById(t))}var t,r,i,a,c,u;n.r(o),n.d(o,{initialize:function(){return e}}),t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},t(e,r)},r=function(){return r=Object.assign||function(e){var t,r,n,o;for(r=1,n=arguments.length;r<n;r++)for(o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},r.apply(this,arguments)},Object.create,Object.create,i=function(e){return i=Object.getOwnPropertyNames||function(e){var t,r=[];for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&(r[r.length]=t);return r},i(e)},"function"==typeof SuppressedError&&SuppressedError,a=n(1),c=n(2),u=n(3)}(),o}()},"object"==typeof exports&&"object"==typeof module?module.exports=factory(require("react"),require("react-dom"),require("bwtk")):"function"==typeof define&&define.amd?define("Bundle",["react","react-dom","bwtk"],factory):"object"==typeof exports?exports.Bundle=factory(require("react"),require("react-dom"),require("bwtk")):this.Bundle=factory(this.React,this.ReactDOM,this.bwtk);
//# sourceMappingURL=myvirgin-preauth-manage-bundle-bundle.min.js.map