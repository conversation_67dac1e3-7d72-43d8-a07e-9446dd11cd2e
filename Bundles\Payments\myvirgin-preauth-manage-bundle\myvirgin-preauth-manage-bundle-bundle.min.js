/*! myvirgin-preauth-manage-bundle (bundle) 1.0.0 | bwtk 6.1.0 | 2025-06-27T22:35:50.889Z */
var factory;factory=function(e,t,n,r,i,o,a,u,l){return function(){function c(e){var t,n=m[e];return void 0!==n?n.exports:(t=m[e]={exports:{}},d[e](t,t.exports,c),t.exports)}var s,d=[,function(t){"use strict";t.exports=e},function(e){"use strict";e.exports=t},function(e){"use strict";e.exports=n},function(e,t,n){var r,i,o,a,u,l,c,s,d;self,e.exports=(r=n(1),i=n(5),o=n(3),a=n(6),u=n(7),l=n(8),c=n(9),s=n(10),d=n(2),function(){function e(t){var r,i=m[t];return void 0!==i?i.exports:(r=m[t]={exports:{}},n[t].call(r.exports,r,r.exports,e),r.exports)}var t,n=[,function(e){"use strict";e.exports=r},function(e){"use strict";e.exports=i},function(e){"use strict";e.exports=o},function(e){"use strict";e.exports=a},function(e){"use strict";e.exports=u},function(e){"use strict";e.exports=JSON.parse('{"en":{"SELECT_BANK_PLACEHOLDER":"Select an option","ALERT_ERROR_VALID_CC_NUMBER":"Please enter a valid credit card number and try again","ALERT_CONFIRMATION_INFO_HEADING":"Avoid late fees by paying your current balance.","ALERT_CONFIRMATION_INFO_DESC":"Pre-authorized payments will only begin on your next billing period. Any existing balance on your account must be paid separately with a final one-time payment, or risk late fees.","ALERT_CONFIRMATION_INFO_BUTTON_DESC":"Make a payment","ALERT_CONFIRMATION_INFO_DESC_1":"Pre-authorized payments will only begin on your next billing period. You must pay your ","ALERT_CONFIRMATION_INFO_DESC_2":"balance in a separate and final one-time payment, or risk late fees. ","EXISTING_CC_TITLE":"Select a credit Card","DEBIT_PAYMENT_OFFER_AMOUNT":"${amount}/mo.","EXISTING_BANK_TITLE":"Select a bank account","EXISTING_BANK_TITLE_SR":"Requried, Select a bank account","EXISTING_BANK_ACCOUNT_MANUAL_DETAILS_LABEL":"Choose a bank account already on file or add a new one manually","SELECT_BILLS_HEADING":"Select bills","SELECT_BILLS_HEADING_SINGULAR":"Select bill","SELECT_BILLS_ACCOUNT_TITLE":"{accounttype} account number","SELECT_BILLS_HEADING_DESC":"Set up pre-authorized payments for the following bill(s):","SELECT_BANK_TITLE":"Select a bank account","ACCOUNT_TYPE_ONE_BILL":"One Bill","ACCOUNT_TYPE_MYBILL":"One Bill","ACCOUNT_TYPENAME_ONEBILL":"One Bill","ACCOUNT_TYPENAME_MYBILL":"My bill","ACCOUNT_TYPENAME_MOBILITY":"Mobility","ACCOUNT_TYPENAME_TV":"TV","ACCOUNT_TYPENAME_INTERNET":"Internet","ACCOUNT_TYPENAME_HOMEPHONE":"Home Phone","ACCOUNT_TYPENAME_MOBILITY_ONEBILL":"Mobility and One Bill","ACCOUNT_TYPENAME_SINGLEBAN":"Single Ban","ACCOUNT_BALANCE":"Your current balance is","CHECKBOX_BALANCE":"{balance}","CHECKBOX_BALANCE_SR":"Your current balance is {balance} dollars","BACK_TO_MY_ACCOUNT":"Back to My Account","CTA_NEXT":"Next","CTA_EDIT":"Edit","CTA_CLOSE":"Close dialog box","CTA_EXPAND_TERMS":"Expand terms of service","CTA_CONFIRM":"Confirm and submit","CTA_CONFIRM_CANCEL":"Confirm","CTA_CANCEL":"Cancel","CTA_COLLAPSE_TERMS":"Collapse terms of service","CTA_INTERAC":"Sign in with Interac® verification service","CTA_INTERAC_SR":"Sign in with Interac registered trademark verification service","CREDIT_CARD_LABEL":"Credit card","CARD_NUMBER_LABEL":"Credit card","CARD_NAME_LABEL":"Cardholder","CREDIT_CARD_TYPE_LABEL":"Card type","CREDIT_CARD_NAME_LABEL":"Cardholder name","CREDIT_CARD_NAME_DESC_INPUT_LABEL":"As it appears on the card","CREDIT_CARD_NUMBER_LABEL":"Credit card number","CREDIT_CARD_NUMBER_DESC_INPUT_LABEL":"15 or 16 digits","CREDIT_CARD_EXPIRY_LABEL":"Expiration date","CREDIT_CARD_EXPIRY_SR_MONTH_LABEL":"Required, Expiration date month","CREDIT_CARD_EXPIRY_SR_YEAR_LABEL":"Required, Expiration date year","CREDIT_CARD_SECURITY_CODE_INPUT_LABEL":"Card security code","CREDIT_CARD_NAME_SR_LABEL":"Required, Cardholder name","CREDIT_CARD_NUMBER_SR_LABEL":"Required, Credit card number","CREDIT_CARD_SECURITY_CODE_INPUT_SR_LABEL":"Required, Card security code","ERROR_CREDIT_CARD_NAME_INPUT_LABEL":"Enter a valid cardholder name","ERROR_CREDIT_CARD_NUMBER_INPUT_LABEL":"Enter a valid credit card number","ERROR_CREDIT_CARD_EXPIRY_INPUT_LABEL":"Enter a valid expiration date","ERROR_CREDIT_CARD_SECURITY_CODE_INPUT_LABEL":"Enter a valid card security code","CREDIT_CARD_VALID":"Valid Until","CREDIT_CARD_MONTH_PLACEHOLDER":"MM","CREDIT_CARD_YEAR_PLACEHOLDER":"YY","REQUIRED_LABEL":"*Required information","MODAL_NO_NAME":"No name on card?","MODAL_SECURITY_CODE":"What is a card security code?","MODAL_NO_NAME_TITLE":"No name on the credit card?","MODAL_NO_NAME_DESC":"If you’re using a prepaid credit card that doesn’t have a cardholder name, enter the name of the Virginplus account holder instead.","MODAL_SECURITY_CODE_TITLE":"What is a card security code?","MODAL_SECURITY_CODE_DESC":"The card security code (CSC) is a fraud prevention feature used to verify that the credit card is in your possession. It’s sometimes called a card verification code (CVC) or card verification value (CVV). See where to find your security code below:","CARD_TYPE_VISA_MASTERCARD":"Visa and Mastercard","CARD_TYPE_VISA_MASTERCARD_DESC":"A 3-digit number on the back of your credit card.","CARD_TYPE_AMERICAN_EXP":"American Express","CARD_TYPE_AMERICAN_EXP_DESC":"A 4-digit number on the front of your credit card.","MODAL_TRANSIT_ACCOUNT_NUMBERS_TITLE":"Find your transit and account numbers","MODAL_BANK_STATEMENT_TITLE":"Look at a bank statement or cheque","MODAL_BANK_STATEMENT_DESC":"You can find your bank account details on a bank statement or cheque. You can also find your account information in your banking app or online account.","MODAL_TRANSIT_NUMBER_DESC":"The <strong>transit number</strong> is 5 digits.","MODAL_ACCOUNT_NUMBER_DESC":"Your <strong>account number</strong> is 7 to 12 digits.","ALERT_ERROR_HEADING":"An error occurred while processing your request.","ALERT_ERROR_HEADING_SR":"Warning, An error occured while processing your request.","ALERT_ERROR_INFO_REQUIRED":"- This information is required.","ALERT_ERROR_SELECT_BILL_INFO_REQUIRED":"Select a bill – This information is required.","ALERT_ERROR_SELECT_BILL_INFO":"Select a bill","ALERT_ERROR_SELECT_BILL_DESC":"This information is required.","ALERT_ERROR_ONE_SELECT_BILL":"Select at least one bill","ALERT_ERROR_HEADING_SOME_BALANCE":"<strong>Some of your current balances</strong> were not paid due to an error processing your request.","ALERT_ERROR_OTP_ALL_BALANCE":"<strong>Your current balance(s) were not paid</strong> due to an error processing your request.","ALERT_ERROR_OTP_BALANCE":"<strong>Your balance of {balance} was not paid.</strong>","ALERT_ERROR_OTP_BALANCE_SR":"Your balance of {balance} dollars was not paid","ALERT_ERROR_OTP_BALANCE_DESC":"There was an error processing your one-time payment. Please pay your current account balance to avoid late payment charges.","ALERT_ERROR_OPTED_NOT_TO_PAY_SINGULAR":"Though you opted not to pay the following balance, they will also require a separate payment:","ALERT_ERROR_OPTED_NOT_TO_PAY_PLURAL":"Though you opted not to pay the following balance(s), they will also require a separate payment:","SELECT_PAYMENT_METHOD_HEADING":"Choose a payment method","UPDATE_PAYMENT_METHOD_HEADING":"Change payment information","PAY_CURRENT_BALANCE_HEADING":"Pay your current balance","PAY_CURRENT_BALANCE_DESC":"Pre-authorized payments will  <strong class=\'payment-text-darkblue\'>begin on your next bill.</strong> You can pay your current account balance separately with a one-time payment to avoid late payment charges.","PAY_CURRENT_BALANCE_DESC_CC":"Credit card payments will <strong class=\'payment-text-darkblue\'>begin on your next bill.</strong> You can pay your current account balance separately with a one-time payment.","PAY_CURRENT_BALANCE_OPTED_IN":"Your one-time payment will be processed in <strong>3 to 5 business days</strong>. After that, you’ll have a balance of {balance} on your account.","PAY_CURRENT_BALANCE_OPTED_IN_PACC":"Your one-time payment will be processed <strong>as soon as you submit this transaction</strong>.","PAY_CURRENT_BALANCE_OPTED_OUT_SINGULAR":"<p>There’s an outstanding balance on your account.</p><p class=\'vrui-mt-10\'>Pre-authorized payments will begin on your next bill. You can pay your current account balance separately with a one-time payment to avoid late payment charges.</p>","PAY_CURRENT_BALANCE_OPTED_OUT_PLURAL":"<p>You chose not to pay your current account balance.</p>","PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_IN":"Your one-time payment will be processed in <strong>3 to 5 business days</strong>. After that, you’ll have a balance of {balance} on your account.","PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_OUT_SINGULAR":"There’s an outstanding balance on your other account.","PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_OUT_PLURAL":"There are outstanding balances on your other accounts.","PAY_CURRENT_BALANCE_SOME_BANS_OPTED_OUT_SINGULAR":"There’s an outstanding balance on your other account.","PAY_CURRENT_BALANCE_SOME_BANS_OPTED_OUT_PLURAL":"There are outstanding balances on your other accounts.","PAYMENT_AMOUNT":"Payment amount","UNPAID_BALANCE":"Unpaid balance","PAY_MY_BALANCE":"Pay my balance of","PAY_MY_BALANCE_SR":"Pay my balance of {balance} dollars","PAY_CURRENT_BALANCE_NOTE_1":"We’ll use the bank account information provided above to make the payment.","PAY_CURRENT_BALANCE_NOTE_1_CC":"We’ll use the credit card information provided above to make the payment.","PAY_CURRENT_BALANCE_NOTE_2":"Note: If you made a payment in the last 3 to 5 business days, your balance owing may not be updated here yet.","TERMS_AND_CONDITION_HEADING":"Pre-authorized Payment Authorization","TERMS_AND_CONDITION_DISCLAIMER":"By clicking <strong>confirm and submit</strong>, I am confirming that I have read and agree to the Virginplus Terms of Service and the pricing details of my selected service(s).","TRANSACTION_SUBMITTED_HEADING":"Transaction submitted","CONFIRMATION_HEADING":"Confirmation","ALERT_CONFIRMATION_SUCCESS_HEADING":"Here’s what’s happening:","ALERT_CONFIRMATION_SUCCESS_MESSAGE":"Pre-authorized payments have been set up and will begin on your <strong>next bill.</strong>","ALERT_CONFIRMATION_SUCCESS_MESSAGE_V2":"Pre-authorized payments have been set up and will begin on your <strong>next billing cycle.</strong>","ALERT_CONFIRMATION_SUCCESS_MESSAGE_V3":"Pre-authorized payments have been updated and will <strong>begin on your next bill.</strong>","ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PACC":"Your one-time payment for <strong> {account} </strong> has been processed.","ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD":"Your one-time payment for <strong> {account} </strong> will be processed in <strong>3 to 5 business days.</strong>","ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS":"Your Autopay credit will start within 1 to 2 billing periods.","ALERT_CONFIRMATION_SUCCESS_NUMBER":"Confirmation number:","ALERT_CONFIRMATION_SUCCESS_DESC":"We’ve sent a confirmation to {email}. If this isn’t your correct email address, please","ALERT_CONFIRMATION_SUCCESS_DESC_LINK":"update your profile.","ALERT_CONFIRMATION_SUCCESS_CONFIRM_NO":"Confirmation Number:","ALERT_CONFIRMATION_CURRENT_BALANCE":"The current balance for {account} has been paid.","PAYMENT_SUMMARY_TITLE":"Payment summary","PAYMENT_INFORMATION_TITLE":"Payment information","BILL_INFORMATION_TITLE":"Bill information","BILL_INFORMATION_ACCOUNT_NUMBER":"Account","PAYMENT_METHOD_TITLE":"Payment method","CURRENT_BALANCE_TITLE":"Current balance","SUMMARY_CURRENT_BAL_OTP_FAILURE":"Payment for your current balance was not processed. Please make a separate one-time payment.","TERMS_AND_CON_TITLE":"Pre-authorized Payment Authorization","TERMS_AND_CON_DESC_1":"You authorize us, Virgin Plus, to set up pre-authorized payment using the payment information you provided, as follows","TERMS_AND_CON_DESC_LIST_1":"For <strong>monthly payments:</strong>","TERMS_AND_CON_DESC_LIST_1_ITEM1":"We will debit your bank account or charge your credit card the total amount due, on the same date or close to that date each month (for example, the date may be different if it falls on a Sunday or a holiday);","TERMS_AND_CON_DESC_LIST_1_ITEM2":"You will be notified at least 10 days in advance on your bill of the amount due, which may vary due to charges you incurred;","TERMS_AND_CON_DESC_LIST_1_ITEM3":"<strong>For services for which a bill is not provided, you waive the requirement to be notified of the amount before the payment when the amount remains the same or is as previously agreed.</strong>","TERMS_AND_CON_DESC_LIST_2":"To <strong>add funds</strong> to your account for prepaid services:","TERMS_AND_CON_DESC_LIST_2_ITEM1":"We will debit your bank account or charge your credit card the amount set according to the criteria you selected;","TERMS_AND_CON_DESC_LIST_2_ITEM2":"<strong>You waive the requirement to be notified of the amount before the payment when the amount is the same as you selected or lower.</strong>","TERMS_AND_CON_DESC_2":"For <strong>other types of payments</strong>, we will obtain your authorization before the payment. In some circumstances, we may also use your pre-authorized payment method to make refunds.","TERMS_AND_CON_DESC_3":"If any payment is not compliant, you may have certain rights such as requesting its refund. For more information or to cancel this authorization, call us or go to MyAccount (<a class=\\"focus-visible:payment-outline-blue focus-visible:payment-outline focus-visible:payment-outline-2 focus-visible:payment-outline-offset-3 focus-visible:payment-rounded-6 payment-underline-offset-2 payment-text-14 payment-text-blue payment-underline payment-leading-18 hover:payment-text-blue-1 hover:payment-no-underline\\" href=\\"virginplus.ca/myaccount\\">virginplus.ca/myaccount</a>). When you cancel your authorization, you must notify us at least 30 days before the next pre-authorized payment date. For pre-authorized debits made with a bank account, to obtain a sample cancellation form, or for more information on your right to cancel this authorization, contact your financial institution or <a class=\\"focus-visible:payment-outline-blue focus-visible:payment-outline focus-visible:payment-outline-2 focus-visible:payment-outline-offset-3 focus-visible:payment-rounded-6 payment-underline-offset-2 payment-text-14 payment-text-blue payment-underline payment-leading-18 hover:payment-text-blue-1 hover:payment-no-underline\\" href=\\"#\\">visit payments.ca.</a>","TERMS_AND_CON_DESC_4":"Virginplus Canada","TERMS_AND_CON_ZIP_CODE":"P.O Box 9000","TERMS_AND_CON_REGION":"North York, Ontario M3C 2X7","TERMS_AND_CON_TEL":"**************","TERMS_AND_CONDITION_HEADING_QC":"Autorisation pour prélèvement automatique","TERMS_AND_CON_TITLE_QC":"Autorisation pour prélèvement automatique","TERMS_AND_CON_DESC_1_QC":"Vous nous autorisez, Virginplus, à mettre en place le paiement par prélèvement automatique en utilisant les renseignements de paiement que vous avez fournis, comme suit.","TERMS_AND_CON_DESC_LIST_1_QC":"Pour les paiements mensuels:","TERMS_AND_CON_DESC_LIST_1_ITEM1_QC":"Nous débiterons votre compte bancaire ou porterons à votre carte de crédit le montant total dû, à la même date ou à une date près de cette date chaque mois (par exemple, la date peut être différente si elle tombe un dimanche ou un jour férié);","TERMS_AND_CON_DESC_LIST_1_ITEM2_QC":"Vous serez avisé au moins 10 jours à l’avance sur votre facture du montant dû, qui peut varier en fonction des frais que vous avez engagés;","TERMS_AND_CON_DESC_LIST_1_ITEM3_QC":"Pour les services pour lesquels aucune facture n’est fournie, vous renoncez à l’exigence d’être avisé du montant avant le prélèvement lorsque le montant demeure le même ou correspond à celui convenu précédemment.","TERMS_AND_CON_DESC_LIST_2_QC":"Pour <strong>ajouter des fonds</strong> à votre compte pour les services prépayés :","TERMS_AND_CON_DESC_LIST_2_ITEM1_QC":"Nous débiterons votre compte bancaire ou porterons à votre carte de crédit, le montant établi selon les critères que vous avez sélectionnés;","TERMS_AND_CON_DESC_LIST_2_ITEM2_QC":"<strong>Vous renoncez à l’exigence d’être avisé du montant avant le prélèvement lorsque le montant est le même que celui que vous avez sélectionné ou inférieur.</strong>","TERMS_AND_CON_DESC_2_QC":"Pour les <strong>autres types de paiements</strong>,  nous obtiendrons votre autorisation avant le prélèvement. Dans certaines circonstances, nous pouvons également utiliser votre mode de paiement par prélèvement automatique pour effectuer des remboursements.","TERMS_AND_CON_DESC_3_QC":"Si un prélèvement n’est pas conforme à l’autorisation, vous pouvez avoir certains droits, comme demander son remboursement. Pour en savoir plus ou pour annuler cette autorisation, appelez-nous ou rendez-vous sur MonVirginplus (monvirginplus.ca). Lorsque vous annulez votre autorisation, vous devez nous en aviser au moins 30 jours avant la date du prochain prélèvement. Pour les prélèvements automatiques effectués avec un compte bancaire, pour obtenir un exemple de formulaire d’annulation ou pour en savoir plus sur votre droit d’annuler cette autorisation, communiquez avec votre institution financière ou visitez paiements.ca.","TERMS_AND_CON_DESC_4_QC":"Bell Canada ","TERMS_AND_CON_ZIP_CODE_QC":"C.P. 8712, succ. Centre-Ville","TERMS_AND_CON_REGION_QC":"Montréal (Québec) H3C 3P6","TERMS_AND_CON_TEL_QC":"Tél.: **************","BANK_ACCOUNT_LABEL":"Bank account","UPDATE_BANK_ACCOUNT_LABEL":"Update bank account","SWITCH_TO_DEBIT_PAYMENT":"Switch to debit payments","UPDATE_CREDITCARD_PAYMENT":"Update credit card information","SWITCH_TO_CREDIT_CARD":"Switch to credit card payments","CANCEL_PAYMENT_LABEL":"Cancel pre-authorized payments","TRANSIT_ACC_NO_PNG":"/Styles/BRF3/content/img/transit-account-number.png","INTERACT_VERIFICATION_V2_PNG":"/Styles/BRF3/content/img/interac-logos/interac-verification-v2-en.png","MASTER_CC_PNG":"/Styles/BRF3/content/img/creditCardIcons/mastercard.png","VISA_CC_PNG":"/Styles/BRF3/content/img/creditCardIcons/visa.png","AMEX_CC_PNG":"/Styles/BRF3/content/img/creditCardIcons/amex.png","FRONT_CC_PNG":"/Styles/BRF3/content/img/Front.png","BACK_CC_PNG":"/Styles/BRF3/content/img/Back.png","SELECT_BILLS_CC_DESC":"{CreditCardType} ending in {CCFourDigits}, expiring {ExpiryDate}","NEW_CREDIT_ACCOUNT_LABEL":"New credit account","SELECT_BILLS_BANK_DESC":"{BankName} ({Code}) account {BankMaskedDigits}","ALERT_GREAT_NEWS":"Great news","ALERT_GREAT_NEWS_DESC":" - by setting up pre-authorized debit payments, the following services will be eligible for an Autopay credit:","ALERT_GREAT_NEWS_DESC_1":" - you’ll receive an Autopay credit.","ALERT_GREAT_NEWS_NOTE":"Note: ","ALERT_GREAT_NEWS_NOTE_DESC":"If you have any upcoming changes to your account, they may impact the Autopay credit above. Any changes to the credit will take effect within 1 to 2 billing periods.","BANK_ACCOUNT_AUTOMATIC_LABEL":"Fill in your bank account details automatically","BANK_ACCOUNT_AUTOMATIC_DESCRIPTION":"Simply use the Interac® verification service to securely sign in to your bank’s website. Available for the following banks:","BANK_ACCOUNT_MANUAL_DETAILS_LABEL":"Enter your bank account details manually","BANK_ACCOUNT_MANUAL_DETAILS_DESCRIPTION":"Virgin Plus accepts payments from most banks and credit unions in Canada.","BANK_NAME_LABEL":"Bank","BANK_NAME_LABEL_SR_TEXT":"Required, Bank","BANK_HOLDER_NAME_LABEL":"Account holder name","BANK_TRANSIT_NUMBER_LABEL":"Transit number","BANK_TRANSIT_NUMBER_DESCRIPTION":"5 digits","BANK_ACCOUNT_NUMBER_LABEL":"Account number","BANK_ACCOUNT_NUMBER_DESCRIPTION":"7 to 12 digits","BANK_ACCOUNT_FETCHED_LABEL":"Account number filled automatically Please confirm this is the correct account.","BANK_NEED_HELP":"Need help?","BANK_NEW_BANK_ACCOUNT_LABEL":"New bank account","SELECT_REQUIRED_LEGEND":"Required","CC_IMAGE_SR_LABEL":"Accepted credit cards: MasterCard Visa AMEX","LOAD_MORE":"Load More","PAYMENT_METHOD":"Payment method","ACCOUNT_HOLDER":"Account holder name ","BANK_NAME":"Bank ","TRANSIT_NUMER":"Transit number ","ACCOUNT_NUMBER":"Account number ","BANK_HOLDER_NAME_ERROR_LABEL":"Enter a valid name","BANK_NAME_ERROR_LABEL":"Select a bank","BANK_TRANSIT_ERROR_LABEL":"Enter a valid, 5-digit transit number","BANK_ACCOUNT_HOLDER_NUMBER_ERROR_LABEL":"Enter a valid bank account number of 7 to 12 digits","ALERT_ERROR_GENERAL_DESC":"This information is required.","PAGE_TITLE_CONFIRMATON":"Confirmation: Set up pre-authorized payments document","INTERAC_BOX_LOGO":"/Styles/BRF3/content/img/interac-logos/Interac_box_logo.png","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_SINGLE":"Avoid late payment charges by paying your current account balance.","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_SINGLE_1":"Pre-authorized payments will begin on your next bill. You can pay your current account balance of ","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_SINGLE_2":" separately with a one-time payment to avoid late payment charges.","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_MULTI":"Avoid late payment charges by paying your current account balance.","ALERT_CONFIRMATION_OPTED_SOME_BALANCES_MULTI":"Avoid late payment charges by paying your current account balances.","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_MULTI":"Pre-authorized payments will begin on your next bill. You can pay your current account balance separately with a one-time payment to avoid late payment charges.","ALERT_CONFIRMATION_OPTED_SOME_BALANCES_DESC_MULTI":"Pre-authorized payments will begin on your next bill. You can pay your current account balances separately with a one-time payment to avoid late payment charges.","CTA_MAKE_PAYMENT_LINK":"/Payment/MakePayment","LOADER":"Loading data. Please wait…","INTERAC_FETCHED_LABEL":"Account number filled automatically","INTERAC_FETCHED_SUBTITLE":"Please confirm this is the correct account.","ALERT_ERROR_HEADING_INTERAC":"We experienced a technical issue","ALERT_ERROR_HEADING_INTERAC_SR":"Error: We experienced a technical issue","ALERT_ERROR_HEADING_INTERAC_DESC":"Sorry, we were unable to process your request due to a technical issue. You can enter your bank account details manually instead.","FAILURE_API_BAN_HEADING":"Something went wrong","FAILURE_API_BAN_HEADING_SR":"Error, Something went wrong","FAILURE_API_BAN_MAIN_DESC":"Sorry, we were unable to process your request.","FAILURE_API_BAN_MAIN_DESC_2":"Please try again.","FAILURE_API_BAN_SUB_DESC":"If the issue persists:","FAILURE_API_BAN_SUB_DESC_LISTITEM_1":"Use a different credit card or set up pre-authorized debit payments instead.","FAILURE_API_BAN_SUB_DESC_LISTITEM_2":"Wait a little while and try again later.","FAILURE_API_BAN_BUTTON":"Try again","LOADER_SUBMIT":"Submitting payment information","LOADER_SUBMIT_DESC":"Please wait","LOADER_PAYMENT":"Validating payment information","LOADER_PAYMENT_DESC":"Please wait","LABEL_LOADED_OFFERS_DEBIT_TITLE":"-by setting up pre-authorized debit payments, the following services will be eligible for an Autopay credit:","LABEL_LOADED_OFFER_DEBIT_TITLE":"-by setting up pre-authorized debit payments, the following service will be eligible for an Autopay credit:","GREAT_NEWS":"Great news","MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE_HEADING":"Note: ","MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE":"If you have any upcoming changes to your account, they may impact the Autopay credits above. Any changes to the credits will take effect within 1 to 2 billing periods.","MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE":"If you have any upcoming changes to your account, they may impact the Autopay credit above. Any changes to the credit will take effect within 1 to 2 billing periods.","SORRY_MESSAGE":"Sorry, the Autopay credit isn’t available for this payment method. To receive the credit, set up pre-authorized debit payments instead.","LABEL_LOADED_OFFERS_CREDIT_TITLE":"– by setting up pre-authorized credit card payments, the following services will be eligible for an Autopay credit:","LABEL_LOADED_OFFER_CREDIT_TITLE":"– by setting up pre-authorized credit card payments, the following service will be eligible for an Autopay credit:","LABEL_LOADED_OFFERS_TRUE_AUTOPAY_TITLE":"– by setting up pre-authorized payments, the following services will be eligible for an Autopay credit:","LABEL_LOADED_OFFER_TRUE_AUTOPAY_TITLE":"– by setting up pre-authorized payments, the following service will be eligible for an Autopay credit:","REVIEW_PAGE_AUTOPAY_CREDIT":"Great news – you’ll receive an Autopay credit.","AUTOPAY_ALERT":"Your Autopay credit will start within 1 to 2 billing periods.","AUTOPAY_CREDITS_TITLE":"Autopay credits","BANK_ACCOUNT_SR_TEXT":"Account number ending in {Account}","BANK_ACCOUNT_EXISTING_SR_TEXT":"ending in","CREDIT_CARD_SR_TEXT":"Card number ending in {Account}","PAYMENT_METHOD_DEBIT":"Debit","FINAL_PAYMENT":"Final payment","FINAL_PAYMENT_HEADING":"One final pre-authorized payment will be withdrawn.","CANCEL_PREAUTH_SUBHEADING_INFO":"Pre-authorized payments ensure your Virginplus bill is always paid on time and help you avoid late payment charges.","CANCEL_PREAUTH_SUBHEADING":"Are you sure you want to cancel pre-authorized payments?","SUCCESS_TOAST_MESSAGE":"Your pre-authorized payments have been cancelled.","SELECT_BILLS_HEADING_DESC_MANAGE":"For which bills do you want to update the payment information?","NOT_PREAUTHORIZED_NOTIFICATION":"Not on pre-authorized payments","LABEL_LOADED_OFFER_REMOVED_DEBIT_TITLE":"The following Autopay credit will be removed:","LABEL_LOADED_OFFERS_REMOVED_DEBIT_TITLE":"The following Autopay credits will be removed:","ALERT_REGFAILURE_HEADING":"The following was not registered for pre-authorized payments due to an error processing your request. Please try again.","ALERT_CANCEL_PAYMENT_SUCCESS":"Pre-authorized payments have been cancelled for the remaining account(s) requested, and will take effect on your <strong>next billing cycle.</strong>","ALERT_CANCEL_PAYMENT_FAILURE":"An error occurred – We were unable to cancel pre-authorized payment for the following bill. Please try again.","ALERT_CANCEL_PAYMENT_FAILURES":"An error occurred – We were unable to cancel pre-authorized payments for the following bills. Please try again.","ALERT_CANCEL_TRY_AGAIN":"Try again","CANCEL_PAYMENT_BACK_TO_MY_ACCOUNT":"Back to My Account","NOT_PREAUTH_NOTE":"<strong>Note: </strong> You also selected {account} which is not currently on pre-authorized payments. No change will be made."},"fr":{"ALERT_ERROR_VALID_CC_NUMBER":"Veuillez entrer un numéro de carte de crédit valide et réessayer.","SELECT_BANK_PLACEHOLDER":"Sélectionner votre institution financière","ALERT_CONFIRMATION_INFO_HEADING":"Évitez des frais de retard en payant le solde actuel de votre compte.","ALERT_CONFIRMATION_INFO_DESC":"Les paiements par prélèvement automatique débuteront à partir de votre prochaine facture. Vous pouvez payer séparément votre solde actuel au moyen d’un paiement unique, afin d’éviter des frais de retard.","ALERT_CONFIRMATION_INFO_BUTTON_DESC":"Effectuer un paiement","ALERT_CONFIRMATION_INFO_DESC_1":"Pre-authorized payments will only begin on your next billing period. You must pay your ","ALERT_CONFIRMATION_INFO_DESC_2":"balance in a separate and final one-time payment, or risk late fees. ","EXISTING_CC_TITLE":"Sélectionnez une carte de crédit","EXISTING_BANK_TITLE":"Sélectionner un compte bancaire","EXISTING_BANK_TITLE_SR":"Requis, Sélectionner un compte bancaire","EXISTING_BANK_ACCOUNT_MANUAL_DETAILS_LABEL":"Choisir un compte bancaire déjà associé à votre compte Virginplus, ou en ajouter un autre manuellement","SELECT_BILLS_HEADING":"Sélectionner les factures","SELECT_BILLS_HEADING_SINGULAR":"Sélectionner les factures","SELECT_BILLS_ACCOUNT_TITLE":"Numéro de compte {accounttype}","SELECT_BILLS_HEADING_DESC":"Configurez les paiements par prélèvement automatique pour la ou les factures suivantes :","SELECT_BANK_TITLE":"Requis, Sélectionner un compte bancaire","ACCOUNT_TYPE_ONE_BILL":"Facture unique","ACCOUNT_TYPE_MYBILL":"One Bill","ACCOUNT_TYPENAME_ONEBILL":"Facture unique","ACCOUNT_TYPENAME_MYBILL":"Ma facture","ACCOUNT_TYPENAME_MOBILITY":"Mobilité","ACCOUNT_TYPENAME_TV":"TV","ACCOUNT_TYPENAME_INTERNET":"Internet","ACCOUNT_TYPENAME_HOMEPHONE":"Home Phone","ACCOUNT_TYPENAME_MOBILITY_ONEBILL":"Mobility and One Bill","ACCOUNT_TYPENAME_SINGLEBAN":"Single Ban","ACCOUNT_BALANCE":"Votre solde actuel est de","CHECKBOX_BALANCE":"{balance}","CHECKBOX_BALANCE_SR":"Votre solde actuel est de {balance} dollars","BACK_TO_MY_ACCOUNT":"Retour à MonVirginplus","CTA_NEXT":"Suivant","CTA_EDIT":"Modifier","CTA_CLOSE":"Fermer la boîte de dialogue","CTA_EXPAND_TERMS":"Afficher les modalités de service","CTA_COLLAPSE_TERMS":"Masquer les modalités de service","CTA_CONFIRM":"Confirmer et envoyer","CTA_CONFIRM_CANCEL":"Confirmer","CTA_CANCEL":"Annuler","CTA_INTERAC":"Se connecter avec Interac<sup>MD</sup>","CTA_INTERAC_SR":"Se connecter avec Interac marque déposée","CREDIT_CARD_LABEL":"Carte de crédit","CREDIT_CARD_TYPE_LABEL":"Type de carte","CREDIT_CARD_NAME_LABEL":"Nom du titulaire de la carte","CREDIT_CARD_NAME_DESC_INPUT_LABEL":"Tel qu’il apparaît sur la carte","CREDIT_CARD_NUMBER_LABEL":"Numéro de carte de crédit","CREDIT_CARD_NUMBER_DESC_INPUT_LABEL":"15 ou 16 chiffres","CREDIT_CARD_EXPIRY_LABEL":"Date d’expiration","CREDIT_CARD_EXPIRY_SR_MONTH_LABEL":"Requis, Date d’expiration mois","CREDIT_CARD_EXPIRY_SR_YEAR_LABEL":"Requis, Date d’expiration année","CREDIT_CARD_SECURITY_CODE_INPUT_LABEL":"Code de sécurité","CREDIT_CARD_NAME_SR_LABEL":"Requis, Nom du titulaire de la carte ","CREDIT_CARD_NUMBER_SR_LABEL":"Requis, Numéro de carte de crédit","CREDIT_CARD_SECURITY_CODE_INPUT_SR_LABEL":"Requis, Code de sécurité","ERROR_CREDIT_CARD_NAME_INPUT_LABEL":"Entrer un numéro de carte de crédit valide ","ERROR_CREDIT_CARD_NUMBER_INPUT_LABEL":"Entrer un nom de titulaire de carte valide ","ERROR_CREDIT_CARD_EXPIRY_INPUT_LABEL":"Entrer une date d’expiration valide","ERROR_CREDIT_CARD_SECURITY_CODE_INPUT_LABEL":"Entrer un code de sécurité valide ","CREDIT_CARD_VALID":"Valable jusqu\'au","CREDIT_CARD_MONTH_PLACEHOLDER":"MM","CREDIT_CARD_YEAR_PLACEHOLDER":"AA","REQUIRED_LABEL":"*Renseignement requis","MODAL_NO_NAME":"Il n’y a pas de nom sur la carte?","MODAL_NO_NAME_TITLE":"Pas de nom sur la carte de crédit?","MODAL_NO_NAME_DESC":"Si vous utilisez une carte de crédit prépayée sur laquelle ne figure aucun nom, entrez le nom du titulaire de compte Virginplus. ","MODAL_SECURITY_CODE":"Qu’est-ce que le code de sécurité de la carte?","MODAL_SECURITY_CODE_TITLE":"Qu’est-ce que le code de sécurité?","MODAL_SECURITY_CODE_DESC":"Le code de sécurité de la carte de crédit (CSC) est une mesure de prévention contre la fraude. Il permet de vérifier que la carte de crédit est en votre possession. Ce code est parfois appelé code de vérification de carte (CVC) ou valeur de vérification de carte (CVV). Voici où trouver votre code de sécurité :","CARD_TYPE_VISA_MASTERCARD":"Visa et Mastercard","CARD_TYPE_VISA_MASTERCARD_DESC":"Un numéro de 3 chiffres au verso de la carte de crédit.","CARD_TYPE_AMERICAN_EXP":"American Express","CARD_TYPE_AMERICAN_EXP_DESC":"Un numéro de 4 chiffres au recto de la carte de crédit.","MODAL_TRANSIT_ACCOUNT_NUMBERS_TITLE":"Trouver votre numéro de succursale et votre numéro de compte","MODAL_BANK_STATEMENT_TITLE":"Sur un relevé bancaire ou un chèque","MODAL_BANK_STATEMENT_DESC":"Vous trouverez vos renseignements bancaires sur un relevé bancaire ou un chèque. Vous pouvez aussi trouver ces renseignements dans votre application bancaire ou en accédant à votre compte bancaire en ligne.","MODAL_TRANSIT_NUMBER_DESC":"Le numéro de succursale comporte 5 chiffres.","MODAL_ACCOUNT_NUMBER_DESC":"Votre numéro de compte comporte de 7 à 12 chiffres.","ALERT_ERROR_HEADING":"Une erreur est survenue pendant le traitement de votre demande.","ALERT_ERROR_HEADING_SR":"Une erreur s’est produite lors du traitement de votre demande.","ALERT_ERROR_INFO_REQUIRED":"– Ce renseignement est requis.","ALERT_ERROR_SELECT_BILL_INFO":"Sélectionner une facture","ALERT_ERROR_SELECT_BILL_DESC":"Ce renseignement est requis.","ALERT_ERROR_SELECT_BILL_INFO_REQUIRED":"Select a bill – This information is required.","ALERT_ERROR_ONE_SELECT_BILL":"Sélectionnez au moins une facture","ALERT_ERROR_HEADING_SOME_BALANCE":"<strong>Certains de vos soldes actuels</strong> n’ont pas été payés en raison d’une erreur lors du traitement de votre demande.","ALERT_ERROR_OTP_ALL_BALANCE":"<strong>Votre ou vos soldes actuels</strong> n’ont pas été payés en raison d’une erreur lors du traitement de votre demande.","ALERT_ERROR_OTP_BALANCE":"<strong>Votre solde de {balance} n\'a pas été payé.</strong>","ALERT_ERROR_OTP_BALANCE_SR":"Votre solde de {balance} dollars n\'a pas été payé.","ALERT_ERROR_OTP_BALANCE_DESC":"Une erreur s’est produite lors du traitement de votre paiement unique. Veuillez payer le solde actuel de votre compte afin d’éviter des frais de retard.","ALERT_ERROR_OPTED_NOT_TO_PAY_SINGULAR":"Même si vous avez choisi de ne pas payer le solde suivant, un paiement séparé sera également requis:","ALERT_ERROR_OPTED_NOT_TO_PAY_PLURAL":"Même si vous avez choisi de ne pas payer le(s) solde(s) suivant(s), ils nécessiteront également un paiement séparé:","SELECT_PAYMENT_METHOD_HEADING":"Choisir le mode de paiement","PAY_CURRENT_BALANCE_HEADING":"Payer votre solde actuel","PAY_CURRENT_BALANCE_DESC":"Les paiements par prélèvement automatique débuteront <strong class=\'vrui-text-darkblue\'>à partir de votre prochaine facture.</strong> Vous pouvez payer séparément votre solde actuel au moyen d’un paiement unique, afin d’éviter des frais de retard.","PAY_CURRENT_BALANCE_DESC_CC":"Les paiements par carte de crédit commenceront <strong class=\'vrui-text-darkblue\'>à compter de votre prochaine facture.</strong> Vous pouvez payer séparément le solde actuel de votre compte au moyen d’un paiement unique.","PAY_CURRENT_BALANCE_OPTED_IN":"Votre paiement unique sera traité dans un délai de <strong>trois à cinq jours ouvrables</strong>. Par la suite, il restera un solde de {balance} dans votre compte.","PAY_CURRENT_BALANCE_OPTED_IN_PACC":"Votre paiement unique sera traité <strong>dès que vous enverrez cette transaction</strong>.","PAY_CURRENT_BALANCE_OPTED_OUT_SINGULAR":"<p>Votre compte comporte un solde à payer.</p><p class=\'vrui-mt-10\'>Les paiements par prélèvement automatique débuteront à partir de votre prochaine facture. Vous pouvez payer séparément votre solde actuel au moyen d’un paiement unique, afin d’éviter des frais de retard.</p>","PAY_CURRENT_BALANCE_OPTED_OUT_PLURAL":"<p>Vous avez choisi de ne pas payer le solde actuel de votre compte.</p>","PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_IN":"Votre paiement unique sera traité dans un délai de <strong>trois à cinq jours ouvrables</strong>. Par la suite, il restera un solde de {balance} dans votre compte.","PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_OUT_SINGULAR":"Votre autre compte comporte un solde à payer.","PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_OUT_PLURAL":"Vos autres comptes comportent des soldes à payer.","PAY_CURRENT_BALANCE_SOME_BANS_OPTED_OUT_SINGULAR":"Votre autre compte comporte un solde à payer.","PAY_CURRENT_BALANCE_SOME_BANS_OPTED_OUT_PLURAL":"Vos autres comptes comportent des soldes à payer.","PAYMENT_AMOUNT":"Montant du paiement","UNPAID_BALANCE":"Solde impayé","PAY_MY_BALANCE":"Payer mon solde de","PAY_MY_BALANCE_SR":"Payer mon solde de {balance} dollars","PAY_CURRENT_BALANCE_NOTE_1":"Nous utiliserons les renseignements relatifs au compte bancaire ci-dessus pour le paiement.","PAY_CURRENT_BALANCE_NOTE_1_CC":"Nous utiliserons les renseignements relatifs à la carte de crédit ci-dessus pour le paiement.","PAY_CURRENT_BALANCE_NOTE_2":"Remarque : Si vous avez effectué un paiement au cours des trois à cinq derniers jours ouvrables, il se peut que votre solde ne soit pas encore mis à jour.","TERMS_AND_CONDITION_HEADING":"Autorisation pour prélèvement automatique","TERMS_AND_CONDITION_DISCLAIMER":"En cliquant sur <strong>Confirmer et envoyer</strong>, je confirme avoir lu et accepté les modalités de service Virginplus et les tarifs du ou des services sélectionnés","TRANSACTION_SUBMITTED_HEADING":"Transaction envoyée","CONFIRMATION_HEADING":"Confirmation","ALERT_CONFIRMATION_SUCCESS_HEADING":"Voici ce qui se passe :","ALERT_CONFIRMATION_SUCCESS_MESSAGE":"Des paiements par prélèvement automatique ont été configurés et commenceront sur votre <strong>prochaine facture.</strong>","ALERT_CONFIRMATION_SUCCESS_MESSAGE_V2":"Pre-authorized payments have been set up and will begin on your <strong>next billing cycle.</strong>","ALERT_CONFIRMATION_SUCCESS_MESSAGE_V3":"Les paiements par prélèvement automatique ont été mis à jour <strong>et commenceront sur votre prochaine facture.</strong>","ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PACC":"Votre paiement unique pour le compte <strong> {account} </strong> a bien été traité.","ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD":"Votre paiement unique porté au compte <strong> {account} </strong> sera traité dans un délai de <strong>trois à cinq jours ouvrables.</strong>","ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD_FR":"Votre paiement unique porté au compte no <strong> {account} </strong> sera traité dans un délai de <strong>trois à cinq jours ouvrables.</strong>","ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS":"Votre crédit pour autopaiement débutera d’ici une à deux périodes de facturation.","ALERT_CONFIRMATION_SUCCESS_NUMBER":"Numéro de confirmation :","ALERT_CONFIRMATION_SUCCESS_DESC":"Nous avons envoyé un courriel de confirmation à l’adresse {email}. S’il ne s’agit pas de la bonne adresse de courriel, veuillez","ALERT_CONFIRMATION_SUCCESS_DESC_LINK":"mettre votre profil à jour.","ALERT_CONFIRMATION_SUCCESS_CONFIRM_NO":"Confirmation Number:","ALERT_CONFIRMATION_CURRENT_BALANCE":"The current balance for {account} has been paid.","PAYMENT_SUMMARY_TITLE":"Sommaire des prélèvements","PAYMENT_INFORMATION_TITLE":"Renseignements de paiement","BILL_INFORMATION_TITLE":"Renseignements sur la facture","BILL_INFORMATION_ACCOUNT_NUMBER":"Compte","PAYMENT_METHOD_TITLE":"Mode de paiement","CURRENT_BALANCE_TITLE":"Solde actuel","SUMMARY_CURRENT_BAL_OTP_FAILURE":"Le paiement de votre solde actuel n’a pas été traité. Veuillez effectuer un paiement unique distinct.","TERMS_AND_CON_TITLE":"Autorisation pour prélèvement automatique","TERMS_AND_CON_DESC_1":"Vous nous autorisez, Virginplus, à mettre en place le paiement par prélèvement automatique en utilisant les renseignements de paiement que vous avez fournis, comme suit.","TERMS_AND_CON_DESC_LIST_1":"Pour les paiements mensuels:","TERMS_AND_CON_DESC_LIST_1_ITEM1":"Nous débiterons votre compte bancaire ou porterons à votre carte de crédit le montant total dû, à la même date ou à une date près de cette date chaque mois (par exemple, la date peut être différente si elle tombe un dimanche ou un jour férié);","TERMS_AND_CON_DESC_LIST_1_ITEM2":"Vous serez avisé au moins 10 jours à l’avance sur votre facture du montant dû, qui peut varier en fonction des frais que vous avez engagés;","TERMS_AND_CON_DESC_LIST_1_ITEM3":"Pour les services pour lesquels aucune facture n’est fournie, vous renoncez à l’exigence d’être avisé du montant avant le prélèvement lorsque le montant demeure le même ou correspond à celui convenu précédemment.","TERMS_AND_CON_DESC_LIST_2":"Pour <strong>ajouter des fonds</strong> à votre compte pour les services prépayés :","TERMS_AND_CON_DESC_LIST_2_ITEM1":"Nous débiterons votre compte bancaire ou porterons à votre carte de crédit, le montant établi selon les critères que vous avez sélectionnés;","TERMS_AND_CON_DESC_LIST_2_ITEM2":"<strong>Vous renoncez à l’exigence d’être avisé du montant avant le prélèvement lorsque le montant est le même que celui que vous avez sélectionné ou inférieur.</strong>","TERMS_AND_CON_DESC_2":"Pour les <strong>autres types de paiements</strong>,  nous obtiendrons votre autorisation avant le prélèvement. Dans certaines circonstances, nous pouvons également utiliser votre mode de paiement par prélèvement automatique pour effectuer des remboursements.","TERMS_AND_CON_DESC_3":"Si un prélèvement n’est pas conforme à l’autorisation, vous pouvez avoir certains droits, comme demander son remboursement. Pour en savoir plus ou pour annuler cette autorisation, appelez-nous ou rendez-vous sur MonVirginplus (monvirginplus.ca). Lorsque vous annulez votre autorisation, vous devez nous en aviser au moins 30 jours avant la date du prochain prélèvement. Pour les prélèvements automatiques effectués avec un compte bancaire, pour obtenir un exemple de formulaire d’annulation ou pour en savoir plus sur votre droit d’annuler cette autorisation, communiquez avec votre institution financière ou visitez paiements.ca.","TERMS_AND_CON_DESC_4":"Bell Canada ","TERMS_AND_CON_ZIP_CODE":"C.P. 8712, succ. Centre-Ville","TERMS_AND_CON_REGION":"Montréal (Québec) H3C 3P6","TERMS_AND_CON_TEL":"Tél.: **************","TERMS_AND_CONDITION_HEADING_QC":"Modalités et conditions","TERMS_AND_CON_TITLE_QC":"Autorisation pour prélèvement automatique","TERMS_AND_CON_DESC_1_QC":"Vous nous autorisez, Virginplus, à mettre en place le paiement par prélèvement automatique en utilisant les renseignements de paiement que vous avez fournis, comme suit.","TERMS_AND_CON_DESC_LIST_1_QC":"Pour les paiements mensuels:","TERMS_AND_CON_DESC_LIST_1_ITEM1_QC":"Nous débiterons votre compte bancaire ou porterons à votre carte de crédit le montant total dû, à la même date ou à une date près de cette date chaque mois (par exemple, la date peut être différente si elle tombe un dimanche ou un jour férié);","TERMS_AND_CON_DESC_LIST_1_ITEM2_QC":"Vous serez avisé au moins 10 jours à l’avance sur votre facture du montant dû, qui peut varier en fonction des frais que vous avez engagés;","TERMS_AND_CON_DESC_LIST_1_ITEM3_QC":"Pour les services pour lesquels aucune facture n’est fournie, vous renoncez à l’exigence d’être avisé du montant avant le prélèvement lorsque le montant demeure le même ou correspond à celui convenu précédemment.","TERMS_AND_CON_DESC_LIST_2_QC":"Pour <strong>ajouter des fonds</strong> à votre compte pour les services prépayés :","TERMS_AND_CON_DESC_LIST_2_ITEM1_QC":"Nous débiterons votre compte bancaire ou porterons à votre carte de crédit, le montant établi selon les critères que vous avez sélectionnés;","TERMS_AND_CON_DESC_LIST_2_ITEM2_QC":"<strong>Vous renoncez à l’exigence d’être avisé du montant avant le prélèvement lorsque le montant est le même que celui que vous avez sélectionné ou inférieur.</strong>","TERMS_AND_CON_DESC_2_QC":"Pour les <strong>autres types de paiements</strong>,  nous obtiendrons votre autorisation avant le prélèvement. Dans certaines circonstances, nous pouvons également utiliser votre mode de paiement par prélèvement automatique pour effectuer des remboursements.","TERMS_AND_CON_DESC_3_QC":"Si un prélèvement n’est pas conforme à l’autorisation, vous pouvez avoir certains droits, comme demander son remboursement. Pour en savoir plus ou pour annuler cette autorisation, appelez-nous ou rendez-vous sur MonVirginplus (monvirginplus.ca). Lorsque vous annulez votre autorisation, vous devez nous en aviser au moins 30 jours avant la date du prochain prélèvement. Pour les prélèvements automatiques effectués avec un compte bancaire, pour obtenir un exemple de formulaire d’annulation ou pour en savoir plus sur votre droit d’annuler cette autorisation, communiquez avec votre institution financière ou visitez paiements.ca.","TERMS_AND_CON_DESC_4_QC":"Virginplus Canada ","TERMS_AND_CON_ZIP_CODE_QC":"C.P. 8712, succ. Centre-Ville","TERMS_AND_CON_REGION_QC":"Montréal (Québec) H3C 3P6","TERMS_AND_CON_TEL_QC":"Tél.: **************","BANK_ACCOUNT_LABEL":"Du compte bancaire","TRANSIT_ACC_NO_PNG":"/Styles/BRF3/content/img/transit-account-number-fr.png","INTERACT_VERIFICATION_V2_PNG":"/Styles/BRF3/content/img/interac-logos/interac-verification-v2-en.png","MASTER_CC_PNG":"/Styles/BRF3/content/img/creditCardIcons/mastercard.png","VISA_CC_PNG":"/Styles/BRF3/content/img/creditCardIcons/visa.png","AMEX_CC_PNG":"/Styles/BRF3/content/img/creditCardIcons/amex.png","FRONT_CC_PNG":"/Styles/BRF3/content/img/Front.png","BACK_CC_PNG":"/Styles/BRF3/content/img/Back.png","INTERAC_BOX_LOGO":"/Styles/BRF3/content/img/interac-logos/Interac_box_logo.png","SELECT_BILLS_CC_DESC":"{CreditCardType} se terminant par {CCFourDigits}, expirant le {ExpiryDate}","NEW_CREDIT_ACCOUNT_LABEL":"Nouvelle carte de crédit","SELECT_BILLS_BANK_DESC":"{BankName} ({Code}) account {BankMaskedDigits}","ALERT_GREAT_NEWS":"Bonne nouvelle","ALERT_GREAT_NEWS_DESC":" : avec des prélèvements automatiques par débit, les services suivants donneront droit à un crédit pour autopaiement :","ALERT_GREAT_NEWS_DESC_1":" : vous aurez droit à un crédit pour autopaiement.","ALERT_GREAT_NEWS_NOTE":"Remarque : ","ALERT_GREAT_NEWS_NOTE_DESC":"S’il y a des changements à venir à votre compte, ils pourraient avoir une incidence sur le crédit pour autopaiement ci-dessus. Tout changement apporté au crédit entrera en vigueur d’ici une à deux périodes de facturation.","BANK_ACCOUNT_AUTOMATIC_LABEL":"Entrer automatiquement les renseignements relatifs à votre compte","BANK_ACCOUNT_AUTOMATIC_DESCRIPTION":"Utilisez le service de vérification Interac<sup>MD</sup> pour vous connecter en toute sécurité au site Web de votre institution financière. Offert pour les institutions suivantes :","BANK_ACCOUNT_MANUAL_DETAILS_LABEL":"Entrer manuellement les renseignements relatifs à votre compte","BANK_ACCOUNT_MANUAL_DETAILS_DESCRIPTION":"Virgin Plus accepte les paiements de la plupart des banques et coopératives de crédit au Canada.","BANK_NAME_LABEL":"Institution financière","BANK_NAME_LABEL_SR_TEXT":"Requis, Institution financière","BANK_HOLDER_NAME_LABEL":"Nom du titulaire du compte","BANK_TRANSIT_NUMBER_LABEL":"Code de transit","BANK_TRANSIT_NUMBER_DESCRIPTION":"5 chiffres","BANK_ACCOUNT_NUMBER_LABEL":"Numéro de compte","BANK_ACCOUNT_NUMBER_DESCRIPTION":"7 à 12 chiffres","BANK_ACCOUNT_FETCHED_LABEL":"Numéro de compte entré automatiquement Veuillez confirmer qu’il s’agit du bon compte.","BANK_NEED_HELP":"Besoin d’aide?","BANK_NEW_BANK_ACCOUNT_LABEL":"Nouvelle carte bancaire","SELECT_REQUIRED_LEGEND":"Requis","CC_IMAGE_SR_LABEL":"Cartes de crédit acceptées: Visa MasterCard American Express","LOAD_MORE":"Charger Plus","PAYMENT_METHOD":"Mode de paiement","ACCOUNT_HOLDER":"Nom du titulaire du compte ","BANK_NAME":"Institution financière  ","TRANSIT_NUMER":"Numéro de succursale ","ACCOUNT_NUMBER":"Numéro de compte ","BANK_HOLDER_NAME_ERROR_LABEL":"Entrer un nom valide","BANK_NAME_ERROR_LABEL":"Sélectionner votre institution financière","BANK_TRANSIT_ERROR_LABEL":"Entrer un numéro de succursale de 5 chiffres valide","BANK_ACCOUNT_HOLDER_NUMBER_ERROR_LABEL":"Entrer un numéro de compte bancaire de 7 à 12 chiffres valide","ALERT_ERROR_GENERAL_DESC":"Ce renseignement est requis.","PAGE_TITLE_CONFIRMATON":"Confirmation: Établir un prélèvement automatique document","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_SINGLE":"Évitez des frais de retard en payant le solde actuel de votre compte.","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_SINGLE_1":"Les paiements par prélèvement automatique débuteront à partir de votre prochaine facture. Vous pouvez payer séparément votre solde actuel de ","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_SINGLE_2":" $ au moyen d’un paiement unique, afin d’éviter des frais de retard.","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_MULTI":"Évitez des frais de retard en payant les solde actuel de vos comptes.","ALERT_CONFIRMATION_OPTED_SOME_BALANCES_MULTI":"Évitez des frais de retard en payant les solde actuel de vos comptes.","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_MULTI":"Les paiements par prélèvement automatique débuteront à partir de votre prochaine facture. Vous pouvez payer séparément vos soldes actuels au moyen d’un paiement unique, afin d’éviter des frais de retard.","ALERT_CONFIRMATION_OPTED_SOME_BALANCES_DESC_MULTI":"Les paiements par prélèvement automatique débuteront à partir de votre prochaine facture. Vous pouvez payer séparément vos soldes actuels au moyen d’un paiement unique, afin d’éviter des frais de retard.","CTA_MAKE_PAYMENT_LINK":"/Payment/MakePayment","LOADER":"Chargement des données. Veuillez patienter…","INTERAC_FETCHED_LABEL":"Numéro de compte entré automatiquement","INTERAC_FETCHED_SUBTITLE":"Veuillez confirmer qu’il s’agit du bon compte.","ALERT_ERROR_HEADING_INTERAC":"Un problème technique est survenu","ALERT_ERROR_HEADING_INTERAC_SR":"Erreur: Un problème technique est survenu","ALERT_ERROR_HEADING_INTERAC_DESC":"Il nous est malheureusement impossible de traiter votre demande en raison d’un problème technique. Vous pouvez entrer vos renseignements bancaires manuellement.","FAILURE_API_BAN_HEADING":"Un problème est survenu","FAILURE_API_BAN_HEADING_SR":"Erreur, Un problème est survenu","FAILURE_API_BAN_MAIN_DESC":"Il nous est malheureusement impossible de traiter votre demande.","FAILURE_API_BAN_MAIN_DESC_2":"Veuillez réessayer.","FAILURE_API_BAN_SUB_DESC":"Si le problème persiste :","FAILURE_API_BAN_SUB_DESC_LISTITEM_1":"Essayez d’utiliser une autre carte de crédit ou encore de configurer des prélèvements automatiques par débit.","FAILURE_API_BAN_SUB_DESC_LISTITEM_2":"Attendez un moment, puis réessayez.","FAILURE_API_BAN_BUTTON":"Réessayer","LOADER_SUBMIT":"Transmission des renseignements de paiement en cours","LOADER_SUBMIT_DESC":"Veuillez patienter","LOADER_PAYMENT":"Validation renseignements de paiement en cours","LOADER_PAYMENT_DESC":"Veuillez patienter","GREAT_NEWS":"Bonne nouvelle","MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE_HEADING":"Remarque :","MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE":" S’il y a des changements à venir à votre compte, ils pourraient avoir une incidence sur les crédits pour autopaiement ci-dessus. Tout changement apporté aux crédits entrera en vigueur d’ici une à deux périodes de facturation.","MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE":" S’il y a des changements à venir à votre compte, ils pourraient avoir une incidence sur le crédit pour autopaiement ci-dessus. Tout changement apporté au crédit entrera en vigueur d’ici une à deux périodes de facturation.","SORRY_MESSAGE":"Désolé, aucun crédit pour autopaiement n’est offert pour ce mode de paiement. Pour avoir droit au crédit, configurez les prélèvements automatiques par débit.","LABEL_LOADED_OFFERS_CREDIT_TITLE":" : avec des paiements par prélèvement automatique sur carte de crédit, les services suivants donneront droit à un crédit pour autopaiement :","LABEL_LOADED_OFFER_CREDIT_TITLE":" : avec des paiements par prélèvement automatique sur carte de crédit, le service suivant donnera droit à un crédit pour autopaiement :","LABEL_LOADED_OFFERS_DEBIT_TITLE":" - avec des prélèvements automatiques par débit, les services suivants donneront droit à un crédit pour autopaiement :","LABEL_LOADED_OFFER_DEBIT_TITLE":" - avec des prélèvements automatiques par débit, le service suivant donnera droit à un crédit pour autopaiement :","LABEL_LOADED_OFFERS_TRUE_AUTOPAY_TITLE":" – avec des paiements par prélèvement automatique, les services suivants donneront droit à un crédit pour autopaiement :","LABEL_LOADED_OFFER_TRUE_AUTOPAY_TITLE":" – avec des paiements par prélèvement automatique, le service suivant donnera droit à un crédit pour autopaiement :","REVIEW_PAGE_AUTOPAY_CREDIT":"Bonne nouvelle : vous aurez droit à un crédit pour autopaiement.","AUTOPAY_ALERT":"Votre crédit pour autopaiement débutera d’ici une à deux périodes de facturation.","AUTOPAY_CREDITS_TITLE":"Crédits pour autopaiement","BANK_ACCOUNT_SR_TEXT":"Numéro de compte se terminant par {Account}","BANK_ACCOUNT_EXISTING_SR_TEXT":"se terminant par","CREDIT_CARD_SR_TEXT":"Numéro de carte se terminant par {Account}","PAYMENT_METHOD_DEBIT":"Débit","UPDATE_PAYMENT_METHOD_HEADING":"Modifier les renseignements de paiement","UPDATE_BANK_ACCOUNT_LABEL":"Mettre à jour le compte bancaire","SWITCH_TO_DEBIT_PAYMENT":"Passer aux paiements par débit","UPDATE_CREDITCARD_PAYMENT":"Mettre à jour les renseignements de carte de crédit","SWITCH_TO_CREDIT_CARD":"Passer aux paiements par carte de crédit","CANCEL_PAYMENT_LABEL":"Annuler les paiements préautorisés","DEBIT_PAYMENT_OFFER_AMOUNT":"{amount}$/mois","FINAL_PAYMENT":"Paiement final","FINAL_PAYMENT_HEADING":"Un dernier prélèvement automatique sera effectué.","CANCEL_PREAUTH_SUBHEADING_INFO":"Avec les prélèvements automatiques, votre facture Virginplus est toujours payée à temps et vous évitez ainsi des frais de retard.","CANCEL_PREAUTH_SUBHEADING":"Voulez-vous bel et bien annuler les paiements par prélèvement automatique?","SUCCESS_TOAST_MESSAGE":"Vos prélèvements automatiques ont été annulés.","SELECT_BILLS_HEADING_DESC_MANAGE":"Pour quelles factures souhaitez-vous mettre à jour les renseignements de paiement?","NOT_PREAUTHORIZED_NOTIFICATION":"Not on pre-authorized payments","CARD_NUMBER_LABEL":"Numéro de carte","CARD_NAME_LABEL":"Nom du détenteur du compte","LABEL_LOADED_OFFER_REMOVED_DEBIT_TITLE":"Le crédit pour autopaiement suivant sera retiré :","LABEL_LOADED_OFFERS_REMOVED_DEBIT_TITLE":"Les crédits pour autopaiement suivants seront retirés :","ALERT_CANCEL_PAYMENT_SUCCESS":"Les paiements par prélèvement automatique ont été annulés pour le ou les comptes restant de votre demande. L’annulation entrera en vigueur lors de votre prochain cycle de facturation.","ALERT_CANCEL_PAYMENT_FAILURE":"Une erreur est survenue – Nous n’avons pu annuler le paiement par prélèvement automatique pour la facture suivante. Veuillez réessayer.","ALERT_CANCEL_PAYMENT_FAILURES":"Une erreur est survenue – Nous n’avons pu annuler les paiements par prélèvement automatique pour les factures suivantes. Veuillez réessayer.","ALERT_CANCEL_TRY_AGAIN":"Réessayer","CANCEL_PAYMENT_BACK_TO_MY_ACCOUNT":"Retour à Mon Compte","NOT_PREAUTH_NOTE":"<strong>Remarque : </strong>Vous avez également sélectionné le compte {account}, pour lequel les prélèvements automatiques ne sont pas configurés. Aucun changement ne sera apporté.","ALERT_REGFAILURE_HEADING":"The following was not registered for pre-authorized payments due to an error processing your request. Please try again."}}')},function(e){"use strict";e.exports=l},function(e){"use strict";e.exports=c},function(e){"use strict";e.exports=s},function(e,t,n){var r;r=(e,t)=>(()=>{function n(e){var t,r=u[e];return void 0!==r?r.exports:(t=u[e]={exports:{}},a[e].call(t.exports,t,t.exports,n),t.exports)}var r,i,o,a={413:(e,t)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.Doctype=t.CDATA=t.Tag=t.Style=t.Script=t.Comment=t.Directive=t.Text=t.Root=t.isTag=t.ElementType=void 0,function(e){e.Root="root",e.Text="text",e.Directive="directive",e.Comment="comment",e.Script="script",e.Style="style",e.Tag="tag",e.CDATA="cdata",e.Doctype="doctype"}(n=t.ElementType||(t.ElementType={})),t.isTag=function(e){return e.type===n.Tag||e.type===n.Script||e.type===n.Style},t.Root=n.Root,t.Text=n.Text,t.Directive=n.Directive,t.Comment=n.Comment,t.Script=n.Script,t.Style=n.Style,t.Tag=n.Tag,t.CDATA=n.CDATA,t.Doctype=n.Doctype},141:function(e,t,n){"use strict";var r,i,o,a,u=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),l=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||u(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.DomHandler=void 0,r=n(413),i=n(957),l(n(957),t),o={withStartIndices:!1,withEndIndices:!1,xmlMode:!1},a=function(){function e(e,t,n){this.dom=[],this.root=new i.Document(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null,"function"==typeof t&&(n=t,t=o),"object"==typeof e&&(t=e,e=void 0),this.callback=null!=e?e:null,this.options=null!=t?t:o,this.elementCB=null!=n?n:null}return e.prototype.onparserinit=function(e){this.parser=e},e.prototype.onreset=function(){this.dom=[],this.root=new i.Document(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null},e.prototype.onend=function(){this.done||(this.done=!0,this.parser=null,this.handleCallback(null))},e.prototype.onerror=function(e){this.handleCallback(e)},e.prototype.onclosetag=function(){this.lastNode=null;var e=this.tagStack.pop();this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),this.elementCB&&this.elementCB(e)},e.prototype.onopentag=function(e,t){var n=this.options.xmlMode?r.ElementType.Tag:void 0,o=new i.Element(e,t,void 0,n);this.addNode(o),this.tagStack.push(o)},e.prototype.ontext=function(e){var t,n=this.lastNode;n&&n.type===r.ElementType.Text?(n.data+=e,this.options.withEndIndices&&(n.endIndex=this.parser.endIndex)):(t=new i.Text(e),this.addNode(t),this.lastNode=t)},e.prototype.oncomment=function(e){if(this.lastNode&&this.lastNode.type===r.ElementType.Comment)this.lastNode.data+=e;else{var t=new i.Comment(e);this.addNode(t),this.lastNode=t}},e.prototype.oncommentend=function(){this.lastNode=null},e.prototype.oncdatastart=function(){var e=new i.Text(""),t=new i.CDATA([e]);this.addNode(t),e.parent=t,this.lastNode=e},e.prototype.oncdataend=function(){this.lastNode=null},e.prototype.onprocessinginstruction=function(e,t){var n=new i.ProcessingInstruction(e,t);this.addNode(n)},e.prototype.handleCallback=function(e){if("function"==typeof this.callback)this.callback(e,this.dom);else if(e)throw e},e.prototype.addNode=function(e){var t=this.tagStack[this.tagStack.length-1],n=t.children[t.children.length-1];this.options.withStartIndices&&(e.startIndex=this.parser.startIndex),this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),t.children.push(e),n&&(e.prev=n,n.next=e),e.parent=t,this.lastNode=null},e}(),t.DomHandler=a,t.default=a},957:function(e,t,n){"use strict";function r(e){return(0,m.isTag)(e)}function i(e){return e.type===m.ElementType.CDATA}function o(e){return e.type===m.ElementType.Text}function a(e){return e.type===m.ElementType.Comment}function u(e){return e.type===m.ElementType.Directive}function l(e){return e.type===m.ElementType.Root}function c(e,t){var n,c,d,m,v,f;if(void 0===t&&(t=!1),o(e))n=new p(e.data);else if(a(e))n=new E(e.data);else if(r(e))c=t?s(e.children):[],d=new y(e.name,N({},e.attribs),c),c.forEach(function(e){return e.parent=d}),null!=e.namespace&&(d.namespace=e.namespace),e["x-attribsNamespace"]&&(d["x-attribsNamespace"]=N({},e["x-attribsNamespace"])),e["x-attribsPrefix"]&&(d["x-attribsPrefix"]=N({},e["x-attribsPrefix"])),n=d;else if(i(e))c=t?s(e.children):[],m=new _(c),c.forEach(function(e){return e.parent=m}),n=m;else if(l(e))c=t?s(e.children):[],v=new b(c),c.forEach(function(e){return e.parent=v}),e["x-mode"]&&(v["x-mode"]=e["x-mode"]),n=v;else{if(!u(e))throw new Error("Not implemented yet: ".concat(e.type));f=new g(e.name,e.data),null!=e["x-name"]&&(f["x-name"]=e["x-name"],f["x-publicId"]=e["x-publicId"],f["x-systemId"]=e["x-systemId"]),n=f}return n.startIndex=e.startIndex,n.endIndex=e.endIndex,null!=e.sourceCodeLocation&&(n.sourceCodeLocation=e.sourceCodeLocation),n}function s(e){for(var t=e.map(function(e){return c(e,!0)}),n=1;n<t.length;n++)t[n].prev=t[n-1],t[n-1].next=t[n];return t}var d,m,v,f,p,E,g,h,_,b,y,C=this&&this.__extends||(d=function(e,t){return d=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},d(e,t)},function(e,t){function n(){this.constructor=e}if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");d(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),N=this&&this.__assign||function(){return N=Object.assign||function(e){var t,n,r,i;for(n=1,r=arguments.length;n<r;n++)for(i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},N.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.cloneNode=t.hasChildren=t.isDocument=t.isDirective=t.isComment=t.isText=t.isCDATA=t.isTag=t.Element=t.Document=t.CDATA=t.NodeWithChildren=t.ProcessingInstruction=t.Comment=t.Text=t.DataNode=t.Node=void 0,m=n(413),v=function(){function e(){this.parent=null,this.prev=null,this.next=null,this.startIndex=null,this.endIndex=null}return Object.defineProperty(e.prototype,"parentNode",{get:function(){return this.parent},set:function(e){this.parent=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"previousSibling",{get:function(){return this.prev},set:function(e){this.prev=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"nextSibling",{get:function(){return this.next},set:function(e){this.next=e},enumerable:!1,configurable:!0}),e.prototype.cloneNode=function(e){return void 0===e&&(e=!1),c(this,e)},e}(),t.Node=v,f=function(e){function t(t){var n=e.call(this)||this;return n.data=t,n}return C(t,e),Object.defineProperty(t.prototype,"nodeValue",{get:function(){return this.data},set:function(e){this.data=e},enumerable:!1,configurable:!0}),t}(v),t.DataNode=f,p=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.type=m.ElementType.Text,t}return C(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 3},enumerable:!1,configurable:!0}),t}(f),t.Text=p,E=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.type=m.ElementType.Comment,t}return C(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 8},enumerable:!1,configurable:!0}),t}(f),t.Comment=E,g=function(e){function t(t,n){var r=e.call(this,n)||this;return r.name=t,r.type=m.ElementType.Directive,r}return C(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 1},enumerable:!1,configurable:!0}),t}(f),t.ProcessingInstruction=g,h=function(e){function t(t){var n=e.call(this)||this;return n.children=t,n}return C(t,e),Object.defineProperty(t.prototype,"firstChild",{get:function(){var e;return null!==(e=this.children[0])&&void 0!==e?e:null},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"lastChild",{get:function(){return this.children.length>0?this.children[this.children.length-1]:null},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"childNodes",{get:function(){return this.children},set:function(e){this.children=e},enumerable:!1,configurable:!0}),t}(v),t.NodeWithChildren=h,_=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.type=m.ElementType.CDATA,t}return C(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 4},enumerable:!1,configurable:!0}),t}(h),t.CDATA=_,b=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.type=m.ElementType.Root,t}return C(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 9},enumerable:!1,configurable:!0}),t}(h),t.Document=b,y=function(e){function t(t,n,r,i){void 0===r&&(r=[]),void 0===i&&(i="script"===t?m.ElementType.Script:"style"===t?m.ElementType.Style:m.ElementType.Tag);var o=e.call(this,r)||this;return o.name=t,o.attribs=n,o.type=i,o}return C(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 1},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"tagName",{get:function(){return this.name},set:function(e){this.name=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"attributes",{get:function(){var e=this;return Object.keys(this.attribs).map(function(t){var n,r;return{name:t,value:e.attribs[t],namespace:null===(n=e["x-attribsNamespace"])||void 0===n?void 0:n[t],prefix:null===(r=e["x-attribsPrefix"])||void 0===r?void 0:r[t]}})},enumerable:!1,configurable:!0}),t}(h),t.Element=y,t.isTag=r,t.isCDATA=i,t.isText=o,t.isComment=a,t.isDirective=u,t.isDocument=l,t.hasChildren=function(e){return Object.prototype.hasOwnProperty.call(e,"children")},t.cloneNode=c},270:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CASE_SENSITIVE_TAG_NAMES_MAP=t.CASE_SENSITIVE_TAG_NAMES=void 0,t.CASE_SENSITIVE_TAG_NAMES=["animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","linearGradient","radialGradient","textPath"],t.CASE_SENSITIVE_TAG_NAMES_MAP=t.CASE_SENSITIVE_TAG_NAMES.reduce(function(e,t){return e[t.toLowerCase()]=t,e},{})},496:(e,t)=>{"use strict";var n,r,i,o,a,u,l,c,s,d,m,v,f;Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t,s,d,m,f,p=e.match(o),E=p&&p[1]?p[1].toLowerCase():"";switch(E){case n:return d=c(e),a.test(e)||null===(t=null==(f=d.querySelector(r))?void 0:f.parentNode)||void 0===t||t.removeChild(f),u.test(e)||null===(s=null==(f=d.querySelector(i))?void 0:f.parentNode)||void 0===s||s.removeChild(f),d.querySelectorAll(n);case r:case i:return m=l(e).querySelectorAll(E),u.test(e)&&a.test(e)?m[0].parentNode.childNodes:m;default:return v?v(e):(f=l(e,i).querySelector(i)).childNodes}},n="html",r="head",i="body",o=/<([a-zA-Z]+[0-9]?)/,a=/<head[^]*>/i,u=/<body[^]*>/i,l=function(e,t){throw new Error("This browser does not support `document.implementation.createHTMLDocument`")},c=function(e,t){throw new Error("This browser does not support `DOMParser.prototype.parseFromString`")},"function"==typeof(s="object"==typeof window&&window.DOMParser)&&(d=new s,l=c=function(e,t){return t&&(e="<".concat(t,">").concat(e,"</").concat(t,">")),d.parseFromString(e,"text/html")}),"object"==typeof document&&document.implementation&&(m=document.implementation.createHTMLDocument(),l=function(e,t){if(t){var n=m.documentElement.querySelector(t);return n&&(n.innerHTML=e),m}return m.documentElement.innerHTML=e,m}),(f="object"==typeof document&&document.createElement("template"))&&f.content&&(v=function(e){return f.innerHTML=e,f.content.childNodes})},471:function(e,t,n){"use strict";var r,i,o,a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if("string"!=typeof e)throw new TypeError("First argument must be a string");if(!e)return[];var t=e.match(o),n=t?t[1]:void 0;return(0,i.formatDOM)((0,r.default)(e),null,n)},r=a(n(496)),i=n(731),o=/<(![a-zA-Z\s]+)>/},731:(e,t,n)=>{"use strict";function r(e){var t,n,r,i;for(t={},n=0,r=e.length;n<r;n++)t[(i=e[n]).name]=i.value;return t}function i(e){return function(e){return a.CASE_SENSITIVE_TAG_NAMES_MAP[e]}(e=e.toLowerCase())||e}Object.defineProperty(t,"__esModule",{value:!0}),t.formatAttributes=r,t.formatDOM=function e(t,n,a){var u,l,c,s,d,m,v;for(void 0===n&&(n=null),l=[],c=0,s=t.length;c<s;c++){switch((d=t[c]).nodeType){case 1:m=i(d.nodeName),(u=new o.Element(m,r(d.attributes))).children=e("template"===m?d.content.childNodes:d.childNodes,u);break;case 3:u=new o.Text(d.nodeValue);break;case 8:u=new o.Comment(d.nodeValue);break;default:continue}(v=l[c-1]||null)&&(v.next=u),u.parent=n,u.prev=v,u.next=null,l.push(u)}return a&&((u=new o.ProcessingInstruction(a.substring(0,a.indexOf(" ")).toLowerCase(),a)).next=l[0]||null,u.parent=n,l.unshift(u),l[1]&&(l[1].prev=l[0])),l};var o=n(141),a=n(270)},840:(e,t,n)=>{"use strict";function r(e){return i.possibleStandardNames[e]}Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n,c,s,d,m,v,f;for(s in void 0===e&&(e={}),n={},c=Boolean(e.type&&l[e.type]),e)if(d=e[s],(0,i.isCustomAttribute)(s))n[s]=d;else if(v=r(m=s.toLowerCase()))switch(f=(0,i.getPropertyInfo)(v),a.includes(v)&&u.includes(t)&&!c&&(v=r("default"+m)),n[v]=d,f&&f.type){case i.BOOLEAN:n[v]=!0;break;case i.OVERLOADED_BOOLEAN:""===d&&(n[v]=!0)}else o.PRESERVE_CUSTOM_ATTRIBUTES&&(n[s]=d);return(0,o.setStyleProp)(e.style,n),n};var i=n(210),o=n(958),a=["checked","value"],u=["input","select","textarea"],l={reset:!0,submit:!0}},308:function(e,t,n){"use strict";function r(e){return a.PRESERVE_CUSTOM_ATTRIBUTES&&"tag"===e.type&&(0,a.isCustomComponent)(e.name,e.attribs)}var i,o,a,u,l=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function e(t,n){var i,l,c,s,d,m,v,f,p,E,g,h,_,b,y;for(void 0===n&&(n={}),i=[],l="function"==typeof n.replace,c=n.transform||a.returnFirstArg,d=(s=n.library||u).cloneElement,m=s.createElement,v=s.isValidElement,f=t.length,p=0;p<f;p++)if(E=t[p],l&&v(g=n.replace(E,p)))f>1&&(g=d(g,{key:g.key||p})),i.push(c(g,E,p));else if("text"!==E.type){switch(_={},r(h=E)?((0,a.setStyleProp)(h.attribs.style,h.attribs),_=h.attribs):h.attribs&&(_=(0,o.default)(h.attribs,h.name)),b=void 0,E.type){case"script":case"style":E.children[0]&&(_.dangerouslySetInnerHTML={__html:E.children[0].data});break;case"tag":"textarea"===E.name&&E.children[0]?_.defaultValue=E.children[0].data:E.children&&E.children.length&&(b=e(E.children,n));break;default:continue}f>1&&(_.key=p),i.push(c(m(E.name,_,b),E,p))}else{if((y=!E.data.trim().length)&&E.parent&&!(0,a.canTextBeChildOfNode)(E.parent))continue;if(n.trim&&y)continue;i.push(c(E.data,E,p))}return 1===i.length?i[0]:i},i=n(155),o=l(n(840)),a=n(958),u={cloneElement:i.cloneElement,createElement:i.createElement,isValidElement:i.isValidElement}},442:function(e,t,n){"use strict";var r,i,o,a,u,l=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.htmlToDOM=t.domToReact=t.attributesToProps=t.Text=t.ProcessingInstruction=t.Element=t.Comment=void 0,t.default=function(e,t){if("string"!=typeof e)throw new TypeError("First argument must be a string");return e?(0,o.default)((0,r.default)(e,(null==t?void 0:t.htmlparser2)||u),t):[]},r=l(n(471)),t.htmlToDOM=r.default,i=l(n(840)),t.attributesToProps=i.default,o=l(n(308)),t.domToReact=o.default,a=n(141),Object.defineProperty(t,"Comment",{enumerable:!0,get:function(){return a.Comment}}),Object.defineProperty(t,"Element",{enumerable:!0,get:function(){return a.Element}}),Object.defineProperty(t,"ProcessingInstruction",{enumerable:!0,get:function(){return a.ProcessingInstruction}}),Object.defineProperty(t,"Text",{enumerable:!0,get:function(){return a.Text}}),u={lowerCaseAttributeNames:!1}},958:function(e,t,n){"use strict";var r,i,o,a,u=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.returnFirstArg=t.canTextBeChildOfNode=t.ELEMENTS_WITH_NO_TEXT_CHILDREN=t.PRESERVE_CUSTOM_ATTRIBUTES=void 0,t.isCustomComponent=function(e,t){return e.includes("-")?!o.has(e):Boolean(t&&"string"==typeof t.is)},t.setStyleProp=function(e,t){if("string"==typeof e)if(e.trim())try{t.style=(0,i.default)(e,a)}catch(e){t.style={}}else t.style={}},r=n(155),i=u(n(229)),o=new Set(["annotation-xml","color-profile","font-face","font-face-src","font-face-uri","font-face-format","font-face-name","missing-glyph"]),a={reactCompat:!0},t.PRESERVE_CUSTOM_ATTRIBUTES=Number(r.version.split(".")[0])>=16,t.ELEMENTS_WITH_NO_TEXT_CHILDREN=new Set(["tr","tbody","thead","tfoot","colgroup","table","head","html","frameset"]),t.canTextBeChildOfNode=function(e){return!t.ELEMENTS_WITH_NO_TEXT_CHILDREN.has(e.name)},t.returnFirstArg=function(e){return e}},788:e=>{function t(e){return e?e.replace(c,s):s}var n=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,r=/\n/g,i=/^\s*/,o=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,a=/^:\s*/,u=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,l=/^[;\s]*/,c=/^\s+|\s+$/g,s="";e.exports=function(e,c){function d(e){var t,n=e.match(r);n&&(b+=n.length),t=e.lastIndexOf("\n"),y=~t?e.length-t:y+e.length}function m(){var e={line:b,column:y};return function(t){return t.position=new v(e),E(),t}}function v(e){this.start=e,this.end={line:b,column:y},this.source=c.source}function f(t){var n=new Error(c.source+":"+b+":"+y+": "+t);if(n.reason=t,n.filename=c.source,n.line=b,n.column=y,n.source=e,!c.silent)throw n;C.push(n)}function p(t){var n,r=t.exec(e);if(r)return d(n=r[0]),e=e.slice(n.length),r}function E(){p(i)}function g(e){var t;for(e=e||[];t=h();)!1!==t&&e.push(t);return e}function h(){var t,n,r=m();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(t=2;s!=e.charAt(t)&&("*"!=e.charAt(t)||"/"!=e.charAt(t+1));)++t;return t+=2,s===e.charAt(t-1)?f("End of comment missing"):(n=e.slice(2,t-2),y+=2,d(n),e=e.slice(t),y+=2,r({type:"comment",comment:n}))}}function _(){var e,r,i=m(),c=p(o);if(c)return h(),p(a)?(e=p(u),r=i({type:"declaration",property:t(c[0].replace(n,s)),value:e?t(e[0].replace(n,s)):s}),p(l),r):f("property missing ':'")}var b,y,C;if("string"!=typeof e)throw new TypeError("First argument must be a string");return e?(c=c||{},b=1,y=1,v.prototype.content=e,C=[],E(),function(){var e,t=[];for(g(t);e=_();)!1!==e&&(t.push(e),g(t));return t}()):[]}},210:(e,t,n)=>{"use strict";function r(e,t,n,r,i,o,a){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=a}const i={};["children","dangerouslySetInnerHTML","defaultValue","defaultChecked","innerHTML","suppressContentEditableWarning","suppressHydrationWarning","style"].forEach(e=>{i[e]=new r(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(([e,t])=>{i[e]=new r(e,1,!1,t,null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(e=>{i[e]=new r(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(e=>{i[e]=new r(e,2,!1,e,null,!1,!1)}),["allowFullScreen","async","autoFocus","autoPlay","controls","default","defer","disabled","disablePictureInPicture","disableRemotePlayback","formNoValidate","hidden","loop","noModule","noValidate","open","playsInline","readOnly","required","reversed","scoped","seamless","itemScope"].forEach(e=>{i[e]=new r(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(e=>{i[e]=new r(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(e=>{i[e]=new r(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(e=>{i[e]=new r(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(e=>{i[e]=new r(e,5,!1,e.toLowerCase(),null,!1,!1)});const o=/[\-\:]([a-z])/g,a=e=>e[1].toUpperCase();["accent-height","alignment-baseline","arabic-form","baseline-shift","cap-height","clip-path","clip-rule","color-interpolation","color-interpolation-filters","color-profile","color-rendering","dominant-baseline","enable-background","fill-opacity","fill-rule","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","glyph-name","glyph-orientation-horizontal","glyph-orientation-vertical","horiz-adv-x","horiz-origin-x","image-rendering","letter-spacing","lighting-color","marker-end","marker-mid","marker-start","overline-position","overline-thickness","paint-order","panose-1","pointer-events","rendering-intent","shape-rendering","stop-color","stop-opacity","strikethrough-position","strikethrough-thickness","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","text-anchor","text-decoration","text-rendering","underline-position","underline-thickness","unicode-bidi","unicode-range","units-per-em","v-alphabetic","v-hanging","v-ideographic","v-mathematical","vector-effect","vert-adv-y","vert-origin-x","vert-origin-y","word-spacing","writing-mode","xmlns:xlink","x-height"].forEach(e=>{const t=e.replace(o,a);i[t]=new r(t,1,!1,e,null,!1,!1)}),["xlink:actuate","xlink:arcrole","xlink:role","xlink:show","xlink:title","xlink:type"].forEach(e=>{const t=e.replace(o,a);i[t]=new r(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(e=>{const t=e.replace(o,a);i[t]=new r(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(e=>{i[e]=new r(e,1,!1,e.toLowerCase(),null,!1,!1)}),i.xlinkHref=new r("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(e=>{i[e]=new r(e,1,!1,e.toLowerCase(),null,!0,!0)});const{CAMELCASE:u,SAME:l,possibleStandardNames:c}=n(811),s=RegExp.prototype.test.bind(new RegExp("^(data|aria)-[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$")),d=Object.keys(c).reduce((e,t)=>{const n=c[t];return n===l?e[t]=t:n===u?e[t.toLowerCase()]=t:e[t]=n,e},{});t.BOOLEAN=3,t.BOOLEANISH_STRING=2,t.NUMERIC=5,t.OVERLOADED_BOOLEAN=4,t.POSITIVE_NUMERIC=6,t.RESERVED=0,t.STRING=1,t.getPropertyInfo=function(e){return i.hasOwnProperty(e)?i[e]:null},t.isCustomAttribute=s,t.possibleStandardNames=d},811:(e,t)=>{t.SAME=0,t.CAMELCASE=1,t.possibleStandardNames={accept:0,acceptCharset:1,"accept-charset":"acceptCharset",accessKey:1,action:0,allowFullScreen:1,alt:0,as:0,async:0,autoCapitalize:1,autoComplete:1,autoCorrect:1,autoFocus:1,autoPlay:1,autoSave:1,capture:0,cellPadding:1,cellSpacing:1,challenge:0,charSet:1,checked:0,children:0,cite:0,class:"className",classID:1,className:1,cols:0,colSpan:1,content:0,contentEditable:1,contextMenu:1,controls:0,controlsList:1,coords:0,crossOrigin:1,dangerouslySetInnerHTML:1,data:0,dateTime:1,default:0,defaultChecked:1,defaultValue:1,defer:0,dir:0,disabled:0,disablePictureInPicture:1,disableRemotePlayback:1,download:0,draggable:0,encType:1,enterKeyHint:1,for:"htmlFor",form:0,formMethod:1,formAction:1,formEncType:1,formNoValidate:1,formTarget:1,frameBorder:1,headers:0,height:0,hidden:0,high:0,href:0,hrefLang:1,htmlFor:1,httpEquiv:1,"http-equiv":"httpEquiv",icon:0,id:0,innerHTML:1,inputMode:1,integrity:0,is:0,itemID:1,itemProp:1,itemRef:1,itemScope:1,itemType:1,keyParams:1,keyType:1,kind:0,label:0,lang:0,list:0,loop:0,low:0,manifest:0,marginWidth:1,marginHeight:1,max:0,maxLength:1,media:0,mediaGroup:1,method:0,min:0,minLength:1,multiple:0,muted:0,name:0,noModule:1,nonce:0,noValidate:1,open:0,optimum:0,pattern:0,placeholder:0,playsInline:1,poster:0,preload:0,profile:0,radioGroup:1,readOnly:1,referrerPolicy:1,rel:0,required:0,reversed:0,role:0,rows:0,rowSpan:1,sandbox:0,scope:0,scoped:0,scrolling:0,seamless:0,selected:0,shape:0,size:0,sizes:0,span:0,spellCheck:1,src:0,srcDoc:1,srcLang:1,srcSet:1,start:0,step:0,style:0,summary:0,tabIndex:1,target:0,title:0,type:0,useMap:1,value:0,width:0,wmode:0,wrap:0,about:0,accentHeight:1,"accent-height":"accentHeight",accumulate:0,additive:0,alignmentBaseline:1,"alignment-baseline":"alignmentBaseline",allowReorder:1,alphabetic:0,amplitude:0,arabicForm:1,"arabic-form":"arabicForm",ascent:0,attributeName:1,attributeType:1,autoReverse:1,azimuth:0,baseFrequency:1,baselineShift:1,"baseline-shift":"baselineShift",baseProfile:1,bbox:0,begin:0,bias:0,by:0,calcMode:1,capHeight:1,"cap-height":"capHeight",clip:0,clipPath:1,"clip-path":"clipPath",clipPathUnits:1,clipRule:1,"clip-rule":"clipRule",color:0,colorInterpolation:1,"color-interpolation":"colorInterpolation",colorInterpolationFilters:1,"color-interpolation-filters":"colorInterpolationFilters",colorProfile:1,"color-profile":"colorProfile",colorRendering:1,"color-rendering":"colorRendering",contentScriptType:1,contentStyleType:1,cursor:0,cx:0,cy:0,d:0,datatype:0,decelerate:0,descent:0,diffuseConstant:1,direction:0,display:0,divisor:0,dominantBaseline:1,"dominant-baseline":"dominantBaseline",dur:0,dx:0,dy:0,edgeMode:1,elevation:0,enableBackground:1,"enable-background":"enableBackground",end:0,exponent:0,externalResourcesRequired:1,fill:0,fillOpacity:1,"fill-opacity":"fillOpacity",fillRule:1,"fill-rule":"fillRule",filter:0,filterRes:1,filterUnits:1,floodOpacity:1,"flood-opacity":"floodOpacity",floodColor:1,"flood-color":"floodColor",focusable:0,fontFamily:1,"font-family":"fontFamily",fontSize:1,"font-size":"fontSize",fontSizeAdjust:1,"font-size-adjust":"fontSizeAdjust",fontStretch:1,"font-stretch":"fontStretch",fontStyle:1,"font-style":"fontStyle",fontVariant:1,"font-variant":"fontVariant",fontWeight:1,"font-weight":"fontWeight",format:0,from:0,fx:0,fy:0,g1:0,g2:0,glyphName:1,"glyph-name":"glyphName",glyphOrientationHorizontal:1,"glyph-orientation-horizontal":"glyphOrientationHorizontal",glyphOrientationVertical:1,"glyph-orientation-vertical":"glyphOrientationVertical",glyphRef:1,gradientTransform:1,gradientUnits:1,hanging:0,horizAdvX:1,"horiz-adv-x":"horizAdvX",horizOriginX:1,"horiz-origin-x":"horizOriginX",ideographic:0,imageRendering:1,"image-rendering":"imageRendering",in2:0,in:0,inlist:0,intercept:0,k1:0,k2:0,k3:0,k4:0,k:0,kernelMatrix:1,kernelUnitLength:1,kerning:0,keyPoints:1,keySplines:1,keyTimes:1,lengthAdjust:1,letterSpacing:1,"letter-spacing":"letterSpacing",lightingColor:1,"lighting-color":"lightingColor",limitingConeAngle:1,local:0,markerEnd:1,"marker-end":"markerEnd",markerHeight:1,markerMid:1,"marker-mid":"markerMid",markerStart:1,"marker-start":"markerStart",markerUnits:1,markerWidth:1,mask:0,maskContentUnits:1,maskUnits:1,mathematical:0,mode:0,numOctaves:1,offset:0,opacity:0,operator:0,order:0,orient:0,orientation:0,origin:0,overflow:0,overlinePosition:1,"overline-position":"overlinePosition",overlineThickness:1,"overline-thickness":"overlineThickness",paintOrder:1,"paint-order":"paintOrder",panose1:0,"panose-1":"panose1",pathLength:1,patternContentUnits:1,patternTransform:1,patternUnits:1,pointerEvents:1,"pointer-events":"pointerEvents",points:0,pointsAtX:1,pointsAtY:1,pointsAtZ:1,prefix:0,preserveAlpha:1,preserveAspectRatio:1,primitiveUnits:1,property:0,r:0,radius:0,refX:1,refY:1,renderingIntent:1,"rendering-intent":"renderingIntent",repeatCount:1,repeatDur:1,requiredExtensions:1,requiredFeatures:1,resource:0,restart:0,result:0,results:0,rotate:0,rx:0,ry:0,scale:0,security:0,seed:0,shapeRendering:1,"shape-rendering":"shapeRendering",slope:0,spacing:0,specularConstant:1,specularExponent:1,speed:0,spreadMethod:1,startOffset:1,stdDeviation:1,stemh:0,stemv:0,stitchTiles:1,stopColor:1,"stop-color":"stopColor",stopOpacity:1,"stop-opacity":"stopOpacity",strikethroughPosition:1,"strikethrough-position":"strikethroughPosition",strikethroughThickness:1,"strikethrough-thickness":"strikethroughThickness",string:0,stroke:0,strokeDasharray:1,"stroke-dasharray":"strokeDasharray",strokeDashoffset:1,"stroke-dashoffset":"strokeDashoffset",strokeLinecap:1,"stroke-linecap":"strokeLinecap",strokeLinejoin:1,"stroke-linejoin":"strokeLinejoin",strokeMiterlimit:1,"stroke-miterlimit":"strokeMiterlimit",strokeWidth:1,"stroke-width":"strokeWidth",strokeOpacity:1,"stroke-opacity":"strokeOpacity",suppressContentEditableWarning:1,suppressHydrationWarning:1,surfaceScale:1,systemLanguage:1,tableValues:1,targetX:1,targetY:1,textAnchor:1,"text-anchor":"textAnchor",textDecoration:1,"text-decoration":"textDecoration",textLength:1,textRendering:1,"text-rendering":"textRendering",to:0,transform:0,typeof:0,u1:0,u2:0,underlinePosition:1,"underline-position":"underlinePosition",underlineThickness:1,"underline-thickness":"underlineThickness",unicode:0,unicodeBidi:1,"unicode-bidi":"unicodeBidi",unicodeRange:1,"unicode-range":"unicodeRange",unitsPerEm:1,"units-per-em":"unitsPerEm",unselectable:0,vAlphabetic:1,"v-alphabetic":"vAlphabetic",values:0,vectorEffect:1,"vector-effect":"vectorEffect",version:0,vertAdvY:1,"vert-adv-y":"vertAdvY",vertOriginX:1,"vert-origin-x":"vertOriginX",vertOriginY:1,"vert-origin-y":"vertOriginY",vHanging:1,"v-hanging":"vHanging",vIdeographic:1,"v-ideographic":"vIdeographic",viewBox:1,viewTarget:1,visibility:0,vMathematical:1,"v-mathematical":"vMathematical",vocab:0,widths:0,wordSpacing:1,"word-spacing":"wordSpacing",writingMode:1,"writing-mode":"writingMode",x1:0,x2:0,x:0,xChannelSelector:1,xHeight:1,"x-height":"xHeight",xlinkActuate:1,"xlink:actuate":"xlinkActuate",xlinkArcrole:1,"xlink:arcrole":"xlinkArcrole",xlinkHref:1,"xlink:href":"xlinkHref",xlinkRole:1,"xlink:role":"xlinkRole",xlinkShow:1,"xlink:show":"xlinkShow",xlinkTitle:1,"xlink:title":"xlinkTitle",xlinkType:1,"xlink:type":"xlinkType",xmlBase:1,"xml:base":"xmlBase",xmlLang:1,"xml:lang":"xmlLang",xmlns:0,"xml:space":"xmlSpace",xmlnsXlink:1,"xmlns:xlink":"xmlnsXlink",xmlSpace:1,y1:0,y2:0,y:0,yChannelSelector:1,z:0,zoomAndPan:1}},229:function(e,t,n){"use strict";function r(e,t){var n={};return e&&"string"==typeof e?((0,i.default)(e,function(e,r){e&&r&&(n[(0,o.camelCase)(e,t)]=r)}),n):n}var i=(this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(n(133)),o=n(917);r.default=r,e.exports=r},917:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.camelCase=void 0;var n=/^--[a-zA-Z0-9-]+$/,r=/-([a-z])/g,i=/^[^-]+$/,o=/^-(webkit|moz|ms|o|khtml)-/,a=/^-(ms)-/,u=function(e,t){return t.toUpperCase()},l=function(e,t){return"".concat(t,"-")};t.camelCase=function(e,t){return void 0===t&&(t={}),function(e){return!e||i.test(e)||n.test(e)}(e)?e:(e=e.toLowerCase(),(e=t.reactCompat?e.replace(a,l):e.replace(o,l)).replace(r,u))}},133:function(e,t,n){"use strict";var r,i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n,i,o=null;return e&&"string"==typeof e?(n=(0,r.default)(e),i="function"==typeof t,n.forEach(function(e){if("declaration"===e.type){var n=e.property,r=e.value;i?t(n,r,e):r&&((o=o||{})[n]=r)}}),o):o},r=i(n(788))},155:t=>{"use strict";t.exports=e},514:e=>{"use strict";e.exports=t}},u={};return n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},i=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,n.t=function(e,t){var o,a,u;if(1&t&&(e=this(e)),8&t)return e;if("object"==typeof e&&e){if(4&t&&e.__esModule)return e;if(16&t&&"function"==typeof e.then)return e}for(o=Object.create(null),n.r(o),a={},r=r||[null,i({}),i([]),i(i)],u=2&t&&e;"object"==typeof u&&!~r.indexOf(u);u=i(u))Object.getOwnPropertyNames(u).forEach(t=>a[t]=()=>e[t]);return a.default=()=>e,n.d(o,a),o},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o={},(()=>{"use strict";function e(e){e.length=0}function t(e,t,n){return Array.prototype.slice.call(e,t,n)}function r(e){return e.bind.apply(e,[null].concat(t(arguments,1)))}function i(e){return requestAnimationFrame(e)}function a(e,t){return typeof t===e}function u(e){return!l(e)&&a("object",e)}function l(e){return null===e}function c(e){try{return e instanceof(e.ownerDocument.defaultView||window).HTMLElement}catch(e){return!1}}function s(e){return en(e)?e:[e]}function d(e,t){s(e).forEach(t)}function m(e,t){return e.indexOf(t)>-1}function v(e,t){return e.push.apply(e,s(t)),e}function f(e,t,n){e&&d(t,function(t){t&&e.classList[n?"add":"remove"](t)})}function p(e,t){f(e,nn(t)?t.split(" "):t,!0)}function E(e,t){d(t,e.appendChild.bind(e))}function g(e,t){d(e,function(e){var n=(t||e).parentNode;n&&n.insertBefore(e,t)})}function h(e,t){return c(e)&&(e.msMatchesSelector||e.matches).call(e,t)}function _(e,n){var r=e?t(e.children):[];return n?r.filter(function(e){return h(e,n)}):r}function b(e,t){return t?_(e,t)[0]:e.firstElementChild}function y(e,t,n){return e&&(n?on(e).reverse():on(e)).forEach(function(n){"__proto__"!==n&&t(e[n],n)}),e}function C(e){return t(arguments,1).forEach(function(t){y(t,function(n,r){e[r]=t[r]})}),e}function N(e){return t(arguments,1).forEach(function(t){y(t,function(t,n){en(t)?e[n]=t.slice():u(t)?e[n]=N({},u(e[n])?e[n]:{},t):e[n]=t})}),e}function A(e,t){d(t||on(e),function(t){delete e[t]})}function T(e,t){d(e,function(e){d(t,function(t){e&&e.removeAttribute(t)})})}function O(e,t,n){u(t)?y(t,function(t,n){O(e,n,t)}):d(e,function(e){l(n)||""===n?T(e,t):e.setAttribute(t,String(n))})}function R(e,t,n){var r=document.createElement(e);return t&&(nn(t)?p(r,t):O(r,t)),n&&E(n,r),r}function S(e,t,n){if(rn(n))return getComputedStyle(e)[t];l(n)||(e.style[t]=""+n)}function x(e,t){S(e,"display",t)}function I(e){e.setActive&&e.setActive()||e.focus({preventScroll:!0})}function L(e,t){return e.getAttribute(t)}function w(e,t){return e&&e.classList.contains(t)}function D(e){return e.getBoundingClientRect()}function M(e){d(e,function(e){e&&e.parentNode&&e.parentNode.removeChild(e)})}function P(e){return b((new DOMParser).parseFromString(e,"text/html").body)}function k(e,t){e.preventDefault(),t&&(e.stopPropagation(),e.stopImmediatePropagation())}function B(e,t){return e&&e.querySelector(t)}function U(e,n){return n?t(e.querySelectorAll(n)):[]}function F(e,t){f(e,t,!1)}function j(e){return e.timeStamp}function H(e){return nn(e)?e:e?e+"px":""}function Y(e,t){if(!e)throw new Error("["+an+"] "+(t||""))}function z(e,t,n){return mn(e-t)<n}function V(e,t,n,r){var i=ln(t,n),o=cn(t,n);return r?i<e&&e<o:i<=e&&e<=o}function G(e,t,n){var r=ln(t,n),i=cn(t,n);return ln(cn(r,e),i)}function q(e){return+(e>0)-+(e<0)}function W(e,t){return d(t,function(t){e=e.replace("%s",""+t)}),e}function K(e){return e<10?"0"+e:""+e}function X(){function t(e,t,n){d(e,function(e){e&&d(t,function(t){t.split(" ").forEach(function(t){var r=t.split(".");n(e,r[0],r[1])})})})}var n=[];return{bind:function(e,r,i,o){t(e,r,function(e,t,r){var a="addEventListener"in e,u=a?e.removeEventListener.bind(e,t,i,o):e.removeListener.bind(e,i);a?e.addEventListener(t,i,o):e.addListener(i),n.push([e,t,r,i,u])})},unbind:function(e,r,i){t(e,r,function(e,t,r){n=n.filter(function(n){return!!(n[0]!==e||n[1]!==t||n[2]!==r||i&&n[3]!==i)||(n[4](),!1)})})},dispatch:function(e,t,n){var r,i=!0;return"function"==typeof CustomEvent?r=new CustomEvent(t,{bubbles:i,detail:n}):(r=document.createEvent("CustomEvent")).initCustomEvent(t,i,!1,n),e.dispatchEvent(r),r},destroy:function(){n.forEach(function(e){e[4]()}),e(n)}}}function $(e){var n=e?e.event.bus:document.createDocumentFragment(),i=X();return e&&e.event.on(wn,i.destroy),C(i,{bus:n,on:function(e,t){i.bind(n,s(e).join(" "),function(e){t.apply(t,en(e.detail)?e.detail:[])})},off:r(i.unbind,n),emit:function(e){i.dispatch(n,e,t(arguments,1))}})}function Q(e,t,n,r){function o(){if(!m){if(d=e?ln((s()-l)/e,1):1,n&&n(d),d>=1&&(t(),l=s(),r&&++v>=r))return a();c=i(o)}}function a(){m=!0}function u(){c&&cancelAnimationFrame(c),d=0,c=0,m=!0}var l,c,s=Date.now,d=0,m=!0,v=0;return{start:function(t){t||u(),l=s()-(t?d*e:0),m=!1,c=i(o)},rewind:function(){l=s(),d=0,n&&n(d)},pause:a,cancel:u,set:function(t){e=t},isPaused:function(){return m}}}function Z(e){return e=nn(e)?e:e.key,qr[e]||e}function J(e,t,n){function r(){i.forEach(function(e){e.style("transform","translateX(-"+100*e.index+"%)")})}var i=t.Slides;return{mount:function(){$(e).on([fn,Nn],r)},start:function(e,t){i.style("transition","opacity "+n.speed+"ms "+n.easing),Zt(t)},cancel:Jt}}function ee(e,t,n){function i(){s(""),l.cancel()}var o,a=t.Move,u=t.Controller,l=t.Scroll,c=t.Elements.list,s=r(S,c,"transition");return{mount:function(){$(e).bind(c,"transitionend",function(e){e.target===c&&o&&(i(),o())})},start:function(t,r){var i=a.toPosition(t,!0),c=a.getPosition(),d=function(t){var r,i,o=n.rewindSpeed;return e.is(Hr)&&o&&(r=u.getIndex(!0),i=u.getEnd(),0===r&&t>=i||r>=i&&0===t)?o:n.speed}(t);mn(i-c)>=1&&d>=1?n.useScroll?l.scroll(i,d,!1,r):(s("transform "+d+"ms "+n.easing),a.translate(i,!0),o=r):(a.jump(t),r())},cancel:i}}function te(...e){return e.filter(Boolean).join(" ")}function ne(e){return null!==e&&"object"==typeof e}function re(e,t){if(Array.isArray(e)&&Array.isArray(t))return e.length===t.length&&!e.some((e,n)=>!re(e,t[n]));if(ne(e)&&ne(t)){const n=Object.keys(e),r=Object.keys(t);return n.length===r.length&&!n.some(n=>!Object.prototype.hasOwnProperty.call(t,n)||!re(e[n],t[n]))}return e===t}function ie(e,t){const n=e;return function(e,t){if(e){const n=Object.keys(e);for(let r=0;r<n.length;r++){const i=n[r];if("__proto__"!==i&&!1===t(e[i],i))break}}}(t,(e,t)=>{Array.isArray(e)?n[t]=e.slice():ne(e)?n[t]=ie(ne(n[t])?n[t]:{},e):n[t]=e}),n}function oe(){return oe=Object.assign?Object.assign.bind():function(e){var t,n,r;for(t=1;t<arguments.length;t++)for(r in n=arguments[t])({}).hasOwnProperty.call(n,r)&&(e[r]=n[r]);return e},oe.apply(null,arguments)}function ae(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}function ue(e){return e}function le(e,t){void 0===t&&(t=ue);var n=[],r=!1;return{read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(e){var i=t(e,r);return n.push(i),function(){n=n.filter(function(e){return e!==i})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){var t,i,o,a;r=!0,t=[],n.length&&(i=n,n=[],i.forEach(e),t=n),o=function(){var n=t;t=[],n.forEach(e)},(a=function(){return Promise.resolve().then(o)})(),n={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),n}}}}}function ce(e,t){return void 0===t&&(t=ue),le(e,t)}function se(e,t){return se=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},se(e,t)}function de(e){return de="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},de(e)}function me(e){if(!e)return null;if("undefined"==typeof WeakRef)return function(){return e||null};var t=e?new WeakRef(e):null;return function(){return(null==t?void 0:t.deref())||null}}function ve(e){setTimeout(e,1)}function fe(e,t,n,r){var i,o=null,a=e;do{if((i=r[a]).guard)i.node.dataset.focusAutoGuard&&(o=i);else{if(!i.lockItem)break;if(a!==e)return;o=null}}while((a+=n)!==t);o&&(o.node.tabIndex=0)}function pe(){return"undefined"!=typeof window}function Ee(e){return _e(e)?(e.nodeName||"").toLowerCase():"#document"}function ge(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function he(e){var t;return null==(t=(_e(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function _e(e){return!!pe()&&(e instanceof Node||e instanceof ge(e).Node)}function be(e){return!!pe()&&(e instanceof Element||e instanceof ge(e).Element)}function ye(e){return!!pe()&&(e instanceof HTMLElement||e instanceof ge(e).HTMLElement)}function Ce(e){return!(!pe()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof ge(e).ShadowRoot)}function Ne(e){const{overflow:t,overflowX:n,overflowY:r,display:i}=xe(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function Ae(e){return["table","td","th"].includes(Ee(e))}function Te(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function Oe(e){const t=Re(),n=be(e)?xe(e):e;return"none"!==n.transform||"none"!==n.perspective||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function Re(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function Se(e){return["html","body","#document"].includes(Ee(e))}function xe(e){return ge(e).getComputedStyle(e)}function Ie(e){return be(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Le(e){if("html"===Ee(e))return e;const t=e.assignedSlot||e.parentNode||Ce(e)&&e.host||he(e);return Ce(t)?t.host:t}function we(e){const t=Le(e);return Se(t)?e.ownerDocument?e.ownerDocument.body:e.body:ye(t)&&Ne(t)?t:we(t)}function De(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const i=we(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),a=ge(i);if(o){const e=Me(a);return t.concat(a,a.visualViewport||[],Ne(i)?i:[],e&&n?De(e):[])}return t.concat(i,De(i,[],n))}function Me(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Pe(e){let t=e.activeElement;for(;null!=(null==(n=t)||null==(n=n.shadowRoot)?void 0:n.activeElement);){var n;t=t.shadowRoot.activeElement}return t}function ke(e,t){if(!e||!t)return!1;const n=null==t.getRootNode?void 0:t.getRootNode();if(e.contains(t))return!0;if(n&&Ce(n)){let n=t;for(;n;){if(e===n)return!0;n=n.parentNode||n.host}}return!1}function Be(){const e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map(e=>{let{brand:t,version:n}=e;return t+"/"+n}).join(" "):navigator.userAgent}function Ue(){const e=/android/i;return e.test(function(){const e=navigator.userAgentData;return null!=e&&e.platform?e.platform:navigator.platform}())||e.test(Be())}function Fe(e,t){const n=["mouse","pen"];return t||n.push("",void 0),n.includes(e)}function je(e){return(null==e?void 0:e.ownerDocument)||document}function He(e,t){if(null==t)return!1;if("composedPath"in e)return e.composedPath().includes(t);const n=e;return null!=n.target&&t.contains(n.target)}function Ye(e){return"composedPath"in e?e.composedPath()[0]:e.target}function ze(e){return ye(e)&&e.matches("input:not([type='hidden']):not([disabled]),[contenteditable]:not([contenteditable='false']),textarea:not([disabled])")}function Ve(e){e.preventDefault(),e.stopPropagation()}function Ge(e,t,n){return il(e,rl(t,n))}function qe(e,t){return"function"==typeof e?e(t):e}function We(e){return e.split("-")[0]}function Ke(e){return e.split("-")[1]}function Xe(e){return"x"===e?"y":"x"}function $e(e){return"y"===e?"height":"width"}function Qe(e){return["top","bottom"].includes(We(e))?"y":"x"}function Ze(e){return Xe(Qe(e))}function Je(e){return e.replace(/start|end/g,e=>cl[e])}function et(e){return e.replace(/left|right|bottom|top/g,e=>ll[e])}function tt(e){return"number"!=typeof e?function(e){return{top:0,right:0,bottom:0,left:0,...e}}(e):{top:e,right:e,bottom:e,left:e}}function nt(e){const{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function rt(e,t,n){let{reference:r,floating:i}=e;const o=Qe(t),a=Ze(t),u=$e(a),l=We(t),c="y"===o,s=r.x+r.width/2-i.width/2,d=r.y+r.height/2-i.height/2,m=r[u]/2-i[u]/2;let v;switch(l){case"top":v={x:s,y:r.y-i.height};break;case"bottom":v={x:s,y:r.y+r.height};break;case"right":v={x:r.x+r.width,y:d};break;case"left":v={x:r.x-i.width,y:d};break;default:v={x:r.x,y:r.y}}switch(Ke(t)){case"start":v[a]-=m*(n&&c?-1:1);break;case"end":v[a]+=m*(n&&c?-1:1)}return v}async function it(e,t){var n;void 0===t&&(t={});const{x:r,y:i,platform:o,rects:a,elements:u,strategy:l}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:m=!1,padding:v=0}=qe(t,e),f=tt(v),p=u[m?"floating"===d?"reference":"floating":d],E=nt(await o.getClippingRect({element:null==(n=await(null==o.isElement?void 0:o.isElement(p)))||n?p:p.contextElement||await(null==o.getDocumentElement?void 0:o.getDocumentElement(u.floating)),boundary:c,rootBoundary:s,strategy:l})),g="floating"===d?{x:r,y:i,width:a.floating.width,height:a.floating.height}:a.reference,h=await(null==o.getOffsetParent?void 0:o.getOffsetParent(u.floating)),_=await(null==o.isElement?void 0:o.isElement(h))&&await(null==o.getScale?void 0:o.getScale(h))||{x:1,y:1},b=nt(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:u,rect:g,offsetParent:h,strategy:l}):g);return{top:(E.top-b.top+f.top)/_.y,bottom:(b.bottom-E.bottom+f.bottom)/_.y,left:(E.left-b.left+f.left)/_.x,right:(b.right-E.right+f.right)/_.x}}function ot(e){const t=xe(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const i=ye(e),o=i?e.offsetWidth:n,a=i?e.offsetHeight:r,u=ol(n)!==o||ol(r)!==a;return u&&(n=o,r=a),{width:n,height:r,$:u}}function at(e){return be(e)?e:e.contextElement}function ut(e){const t=at(e);if(!ye(t))return ul(1);const n=t.getBoundingClientRect(),{width:r,height:i,$:o}=ot(t);let a=(o?ol(n.width):n.width)/r,u=(o?ol(n.height):n.height)/i;return a&&Number.isFinite(a)||(a=1),u&&Number.isFinite(u)||(u=1),{x:a,y:u}}function lt(e){const t=ge(e);return Re()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:sl}function ct(e,t,n,r){void 0===t&&(t=!1),void 0===n&&(n=!1);const i=e.getBoundingClientRect(),o=at(e);let a=ul(1);t&&(r?be(r)&&(a=ut(r)):a=ut(e));const u=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==ge(e))&&t}(o,n,r)?lt(o):ul(0);let l=(i.left+u.x)/a.x,c=(i.top+u.y)/a.y,s=i.width/a.x,d=i.height/a.y;if(o){const e=ge(o),t=r&&be(r)?ge(r):r;let n=e,i=Me(n);for(;i&&r&&t!==n;){const e=ut(i),t=i.getBoundingClientRect(),r=xe(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;l*=e.x,c*=e.y,s*=e.x,d*=e.y,l+=o,c+=a,n=ge(i),i=Me(n)}}return nt({width:s,height:d,x:l,y:c})}function st(e,t){const n=Ie(e).scrollLeft;return t?t.left+n:ct(he(e)).left+n}function dt(e,t,n){let r;if("viewport"===t)r=function(e,t){const n=ge(e),r=he(e),i=n.visualViewport;let o=r.clientWidth,a=r.clientHeight,u=0,l=0;if(i){o=i.width,a=i.height;const e=Re();(!e||e&&"fixed"===t)&&(u=i.offsetLeft,l=i.offsetTop)}return{width:o,height:a,x:u,y:l}}(e,n);else if("document"===t)r=function(e){const t=he(e),n=Ie(e),r=e.ownerDocument.body,i=il(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=il(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let a=-n.scrollLeft+st(e);const u=-n.scrollTop;return"rtl"===xe(r).direction&&(a+=il(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:a,y:u}}(he(e));else if(be(t))r=function(e,t){const n=ct(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=ye(e)?ut(e):ul(1);return{width:e.clientWidth*o.x,height:e.clientHeight*o.y,x:i*o.x,y:r*o.y}}(t,n);else{const n=lt(e);r={...t,x:t.x-n.x,y:t.y-n.y}}return nt(r)}function mt(e,t){const n=Le(e);return!(n===t||!be(n)||Se(n))&&("fixed"===xe(n).position||mt(n,t))}function vt(e,t,n){const r=ye(t),i=he(t),o="fixed"===n,a=ct(e,!0,o,t);let u={scrollLeft:0,scrollTop:0};const l=ul(0);if(r||!r&&!o)if(("body"!==Ee(t)||Ne(i))&&(u=Ie(t)),r){const e=ct(t,!0,o,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else i&&(l.x=st(i));let c=0,s=0;if(i&&!r&&!o){const e=i.getBoundingClientRect();s=e.top+u.scrollTop,c=e.left+u.scrollLeft-st(i,e)}return{x:a.left+u.scrollLeft-l.x-c,y:a.top+u.scrollTop-l.y-s,width:a.width,height:a.height}}function ft(e){return"static"===xe(e).position}function pt(e,t){if(!ye(e)||"fixed"===xe(e).position)return null;if(t)return t(e);let n=e.offsetParent;return he(e)===n&&(n=n.ownerDocument.body),n}function Et(e,t){const n=ge(e);if(Te(e))return n;if(!ye(e)){let t=Le(e);for(;t&&!Se(t);){if(be(t)&&!ft(t))return t;t=Le(t)}return n}let r=pt(e,t);for(;r&&Ae(r)&&ft(r);)r=pt(r,t);return r&&Se(r)&&ft(r)&&!Oe(r)?n:r||function(e){let t=Le(e);for(;ye(t)&&!Se(t);){if(Oe(t))return t;if(Te(t))return null;t=Le(t)}return null}(e)||n}function gt(e,t,n,r){void 0===r&&(r={});const{ancestorScroll:i=!0,ancestorResize:o=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:l=!1}=r,c=at(e),s=i||o?[...c?De(c):[],...De(t)]:[];s.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),o&&e.addEventListener("resize",n)});const d=c&&u?function(e,t){function n(){var e;clearTimeout(r),null==(e=i)||e.disconnect(),i=null}let r,i=null;const o=he(e);return function a(u,l){function c(e){const t=e[0].intersectionRatio;if(t!==l){if(!p)return a();t?a(!1,t):r=setTimeout(()=>{a(!1,1e-7)},1e3)}p=!1}void 0===u&&(u=!1),void 0===l&&(l=1),n();const{left:s,top:d,width:m,height:v}=e.getBoundingClientRect();if(u||t(),!m||!v)return;const f={rootMargin:-al(d)+"px "+-al(o.clientWidth-(s+m))+"px "+-al(o.clientHeight-(d+v))+"px "+-al(s)+"px",threshold:il(0,rl(1,l))||1};let p=!0;try{i=new IntersectionObserver(c,{...f,root:o.ownerDocument})}catch(e){i=new IntersectionObserver(c,f)}i.observe(e)}(!0),n}(c,n):null;let m,v=-1,f=null;a&&(f=new ResizeObserver(e=>{let[r]=e;r&&r.target===c&&f&&(f.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=f)||e.observe(t)})),n()}),c&&!l&&f.observe(c),f.observe(t));let p=l?ct(e):null;return l&&function t(){const r=ct(e);!p||r.x===p.x&&r.y===p.y&&r.width===p.width&&r.height===p.height||n(),p=r,m=requestAnimationFrame(t)}(),n(),()=>{var e;s.forEach(e=>{i&&e.removeEventListener("scroll",n),o&&e.removeEventListener("resize",n)}),null==d||d(),null==(e=f)||e.disconnect(),f=null,l&&cancelAnimationFrame(m)}}function ht(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;let n,r,i;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;0!=r--;)if(!ht(e[r],t[r]))return!1;return!0}if(i=Object.keys(e),n=i.length,n!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!{}.hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){const n=i[r];if(!("_owner"===n&&e.$$typeof||ht(e[n],t[n])))return!1}return!0}return e!=e&&t!=t}function _t(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function bt(e,t){const n=_t(e);return Math.round(t*n)/n}function yt(e){const t=Yt.useRef(e);return Qo(()=>{t.current=e}),t}function Ct(e){return Yt.useMemo(()=>e.every(e=>null==e)?null:t=>{e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})},e)}function Nt(e){const t=Yt.useRef(()=>{});return yl(()=>{t.current=e}),Yt.useCallback(function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)},[])}function At(e,t){void 0===t&&(t={});const{preventScroll:n=!1,cancelPrevious:r=!0,sync:i=!1}=t;r&&cancelAnimationFrame(Cl);const o=()=>null==e?void 0:e.focus({preventScroll:n});i?o():Cl=requestAnimationFrame(o)}function Tt(){return Tt=Object.assign?Object.assign.bind():function(e){var t,n,r;for(t=1;t<arguments.length;t++)for(r in n=arguments[t])Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r]);return e},Tt.apply(this,arguments)}function Ot(e){return"data-floating-ui-"+e}function Rt(e){const t=(0,Yt.useRef)(e);return Zo(()=>{t.current=e}),t}function St(e,t,n){return n&&!Fe(n)?0:"number"==typeof e?e:null==e?void 0:e[t]}function xt(e,t){let n=e.filter(e=>{var n;return e.parentId===t&&(null==(n=e.context)?void 0:n.open)}),r=n;for(;r.length;)r=e.filter(e=>{var t;return null==(t=r)?void 0:t.some(t=>{var n;return e.parentId===t.id&&(null==(n=e.context)?void 0:n.open)})}),n=n.concat(r);return n}function It(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);const r=je(e[0]).body;return function(e,t,n,r){const i="data-floating-ui-inert",o=r?"inert":n?"aria-hidden":null,a=(u=t,e.map(e=>{if(u.contains(e))return e;const t=Bl(e);return u.contains(t)?t:null}).filter(e=>null!=e));var u;const l=new Set,c=new Set(a),s=[];Pl[i]||(Pl[i]=new WeakMap);const d=Pl[i];return a.forEach(function e(t){t&&!l.has(t)&&(l.add(t),t.parentNode&&e(t.parentNode))}),function e(t){t&&!c.has(t)&&[].forEach.call(t.children,t=>{if("script"!==Ee(t))if(l.has(t))e(t);else{const e=o?t.getAttribute(o):null,n=null!==e&&"false"!==e,r=(Dl.get(t)||0)+1,a=(d.get(t)||0)+1;Dl.set(t,r),d.set(t,a),s.push(t),1===r&&n&&Ml.add(t),1===a&&t.setAttribute(i,""),!n&&o&&t.setAttribute(o,"true")}})}(t),l.clear(),kl++,()=>{s.forEach(e=>{const t=(Dl.get(e)||0)-1,n=(d.get(e)||0)-1;Dl.set(e,t),d.set(e,n),t||(!Ml.has(e)&&o&&e.removeAttribute(o),Ml.delete(e)),n||e.removeAttribute(i)}),kl--,kl||(Dl=new WeakMap,Dl=new WeakMap,Ml=new WeakSet,Pl={})}}(e.concat(Array.from(r.querySelectorAll("[aria-live]"))),r,t,n)}function Lt(e,t){const n=$o(e,Ul());"prev"===t&&n.reverse();const r=n.indexOf(Pe(je(e)));return n.slice(r+1)[0]}function wt(e,t){const n=t||e.currentTarget,r=e.relatedTarget;return!r||!ke(n,r)}function Dt(e){"Tab"===e.key&&(e.target,clearTimeout(void 0))}function Mt(e){zl=zl.filter(e=>e.isConnected);let t=e;if(t&&"body"!==Ee(t)){if(!function(e,t){if(t=t||{},!e)throw new Error("No node provided");return!1!==Bo.call(e,Po)&&Wo(t,e)}(t,Ul())){const e=$o(t,Ul())[0];e&&(t=e)}zl.push(t),zl.length>20&&(zl=zl.slice(-20))}}function Pt(){return zl.slice().reverse().find(e=>e.isConnected)}function kt(e){function t(e){return!a&&v&&m?Yt.createElement(Vl,{ref:"start"===e?w:D,onClick:e=>h(!1,e.nativeEvent)},"string"==typeof v?v:"Dismiss"):null}var n,r;const{context:i,children:o,disabled:a=!1,order:u=["content"],guards:l=!0,initialFocus:c=0,returnFocus:s=!0,restoreFocus:d=!1,modal:m=!0,visuallyHiddenDismiss:v=!1,closeOnFocusOut:f=!0}=e,{open:p,refs:E,nodeId:g,onOpenChange:h,events:_,dataRef:b,floatingId:y,elements:{domReference:C,floating:N}}=i,A="number"==typeof c&&c<0,T=!!(n=C)&&"combobox"===n.getAttribute("role")&&ze(n)&&A,O="undefined"==typeof HTMLElement||!("inert"in HTMLElement.prototype)||l,R=Rt(u),S=Rt(c),x=Rt(s),I=Ll(),L=Yt.useContext(Hl),w=Yt.useRef(null),D=Yt.useRef(null),M=Yt.useRef(!1),P=Yt.useRef(!1),k=Yt.useRef(-1),B=null!=L,U=(r=N)?r.hasAttribute(Yl)?r:r.querySelector("["+Yl+"]")||r:null,F=Nt(function(e){return void 0===e&&(e=U),e?$o(e,Ul()):[]}),j=Nt(e=>{const t=F(e);return R.current.map(e=>C&&"reference"===e?C:U&&"floating"===e?U:t).filter(Boolean).flat()});Yt.useEffect(()=>{M.current=!1},[a]),Yt.useEffect(()=>{function e(e){if("Tab"===e.key){ke(U,Pe(je(U)))&&0===F().length&&!T&&Ve(e);const t=j(),n=Ye(e);"reference"===R.current[0]&&n===C&&(Ve(e),e.shiftKey?At(t[t.length-1]):At(t[1])),"floating"===R.current[1]&&n===U&&e.shiftKey&&(Ve(e),At(t[0]))}}if(a)return;if(!m)return;const t=je(U);return t.addEventListener("keydown",e),()=>{t.removeEventListener("keydown",e)}},[a,C,U,m,R,T,F,j]),Yt.useEffect(()=>{function e(e){const t=Ye(e),n=F().indexOf(t);-1!==n&&(k.current=n)}if(!a&&N)return N.addEventListener("focusin",e),()=>{N.removeEventListener("focusin",e)}},[a,N,F]),Yt.useEffect(()=>{function e(){P.current=!0,setTimeout(()=>{P.current=!1})}function t(e){const t=e.relatedTarget;queueMicrotask(()=>{const n=!(ke(C,t)||ke(N,t)||ke(t,N)||ke(null==L?void 0:L.portalNode,t)||null!=t&&t.hasAttribute(Ot("focus-guard"))||I&&(xt(I.nodesRef.current,g).find(e=>{var n,r;return ke(null==(n=e.context)?void 0:n.elements.floating,t)||ke(null==(r=e.context)?void 0:r.elements.domReference,t)})||function(e,t){var n;let r=[],i=null==(n=e.find(e=>e.id===t))?void 0:n.parentId;for(;i;){const t=e.find(e=>e.id===i);i=null==t?void 0:t.parentId,t&&(r=r.concat(t))}return r}(I.nodesRef.current,g).find(e=>{var n,r;return(null==(n=e.context)?void 0:n.elements.floating)===t||(null==(r=e.context)?void 0:r.elements.domReference)===t})));if(d&&n&&Pe(je(U))===je(U).body){ye(U)&&U.focus();const e=k.current,t=F(),n=t[e]||t[t.length-1]||U;ye(n)&&n.focus()}!T&&m||!t||!n||P.current||t===Pt()||(M.current=!0,h(!1,e,"focus-out"))})}if(!a&&f)return N&&ye(C)?(C.addEventListener("focusout",t),C.addEventListener("pointerdown",e),N.addEventListener("focusout",t),()=>{C.removeEventListener("focusout",t),C.removeEventListener("pointerdown",e),N.removeEventListener("focusout",t)}):void 0},[a,C,N,U,m,g,I,L,h,f,d,F,T]),Yt.useEffect(()=>{var e;if(a)return;const t=Array.from((null==L||null==(e=L.portalNode)?void 0:e.querySelectorAll("["+Ot("portal")+"]"))||[]);if(N){const e=[N,...t,w.current,D.current,R.current.includes("reference")||T?C:null].filter(e=>null!=e),n=m||T?It(e,O,!O):It(e);return()=>{n()}}},[a,C,N,m,R,L,T,O]),Zo(()=>{if(a||!ye(U))return;const e=Pe(je(U));queueMicrotask(()=>{const t=j(U),n=S.current,r=("number"==typeof n?t[n]:n.current)||U,i=ke(U,e);A||i||!p||At(r,{preventScroll:r===U})})},[a,p,U,A,j,S]),Zo(()=>{function e(e){let{open:n,reason:r,event:o,nested:a}=e;n&&(i=o),"escape-key"===r&&E.domReference.current&&Mt(E.domReference.current),"hover"===r&&"mouseleave"===o.type&&(M.current=!0),"outside-press"===r&&(a?(M.current=!1,t=!0):M.current=!(function(e){return!(0!==e.mozInputSource||!e.isTrusted)||(Ue()&&e.pointerType?"click"===e.type&&1===e.buttons:0===e.detail&&!e.pointerType)}(o)||function(e){return!Be().includes("jsdom/")&&(!Ue()&&0===e.width&&0===e.height||Ue()&&1===e.width&&1===e.height&&0===e.pressure&&0===e.detail&&"mouse"===e.pointerType||e.width<1&&e.height<1&&0===e.pressure&&0===e.detail&&"touch"===e.pointerType)}(o)))}if(a||!U)return;let t=!1;const n=je(U),r=Pe(n);let i=b.current.openEvent;Mt(r),_.on("openchange",e);const o=n.createElement("span");return o.setAttribute("tabindex","-1"),o.setAttribute("aria-hidden","true"),Object.assign(o.style,Fl),B&&C&&C.insertAdjacentElement("afterend",o),()=>{_.off("openchange",e);const r=Pe(n),a=ke(N,r)||I&&xt(I.nodesRef.current,g).some(e=>{var t;return ke(null==(t=e.context)?void 0:t.elements.floating,r)});(a||i&&["click","mousedown"].includes(i.type))&&E.domReference.current&&Mt(E.domReference.current);const u=Pt()||o;queueMicrotask(()=>{x.current&&!M.current&&ye(u)&&(u===r||r===n.body||a)&&u.focus({preventScroll:t}),o.remove()})}},[a,N,U,x,b,E,_,I,g,B,C]),Zo(()=>{if(!a&&L)return L.setFocusManagerState({modal:m,closeOnFocusOut:f,open:p,onOpenChange:h,refs:E}),()=>{L.setFocusManagerState(null)}},[a,L,m,p,h,E,f]),Zo(()=>{if(a)return;if(!U)return;if("function"!=typeof MutationObserver)return;if(A)return;const e=()=>{const e=U.getAttribute("tabindex"),t=F(),n=Pe(je(N)),r=t.indexOf(n);-1!==r&&(k.current=r),R.current.includes("floating")||n!==E.domReference.current&&0===t.length?"0"!==e&&U.setAttribute("tabindex","0"):"-1"!==e&&U.setAttribute("tabindex","-1")};e();const t=new MutationObserver(e);return t.observe(U,{childList:!0,subtree:!0,attributes:!0}),()=>{t.disconnect()}},[a,N,U,E,R,F,A]);const H=!a&&O&&(!m||!T)&&(B||m);return Yt.createElement(Yt.Fragment,null,H&&Yt.createElement(jl,{"data-type":"inside",ref:null==L?void 0:L.beforeInsideRef,onFocus:e=>{if(m){const e=j();At("reference"===u[0]?e[0]:e[e.length-1])}else if(null!=L&&L.preserveTabOrder&&L.portalNode)if(M.current=!1,wt(e,L.portalNode)){const e=Lt(document.body,"next")||C;null==e||e.focus()}else{var t;null==(t=L.beforeOutsideRef.current)||t.focus()}}}),!T&&t("start"),o,t("end"),H&&Yt.createElement(jl,{"data-type":"inside",ref:null==L?void 0:L.afterInsideRef,onFocus:e=>{if(m)At(j()[0]);else if(null!=L&&L.preserveTabOrder&&L.portalNode)if(f&&(M.current=!0),wt(e,L.portalNode)){const e=Lt(document.body,"prev")||C;null==e||e.focus()}else{var t;null==(t=L.afterOutsideRef.current)||t.focus()}}}))}function Bt(e){return ye(e.target)&&"BUTTON"===e.target.tagName}function Ut(e){return ze(e)}function Ft(e,t,n){const r=new Map,i="item"===n;let o=e;if(i&&e){const{[Kl]:t,[Xl]:n,...r}=e;o=r}return{..."floating"===n&&{tabIndex:-1,[Yl]:""},...o,...t.map(t=>{const r=t?t[n]:null;return"function"==typeof r?e?r(e):null:r}).concat(e).reduce((e,t)=>t?(Object.entries(t).forEach(t=>{let[n,o]=t;var a;i&&[Kl,Xl].includes(n)||(0===n.indexOf("on")?(r.has(n)||r.set(n,[]),"function"==typeof o&&(null==(a=r.get(n))||a.push(o),e[n]=function(){for(var e,t=arguments.length,i=new Array(t),o=0;o<t;o++)i[o]=arguments[o];return null==(e=r.get(n))?void 0:e.map(e=>e(...i)).find(e=>void 0!==e)})):e[n]=o)}),e):e,{})}}function jt(e,t){const[n,r]=e;let i=!1;const o=t.length;for(let a=0,u=o-1;a<o;u=a++){const[e,o]=t[a]||[0,0],[l,c]=t[u]||[0,0];o>=r!=c>=r&&n<=(l-e)*(r-o)/(c-o)+e&&(i=!i)}return i}function Ht(e){void 0===e&&(e={});const{buffer:t=.5,blockPointerEvents:n=!1,requireIntent:r=!0}=e;let i,o=!1,a=null,u=null,l=performance.now();const c=e=>{let{x:n,y:c,placement:s,elements:d,onClose:m,nodeId:v,tree:f}=e;return function(e){function p(){clearTimeout(i),m()}if(clearTimeout(i),!d.domReference||!d.floating||null==s||null==n||null==c)return;const{clientX:E,clientY:g}=e,h=[E,g],_=Ye(e),b="mouseleave"===e.type,y=ke(d.floating,_),C=ke(d.domReference,_),N=d.domReference.getBoundingClientRect(),A=d.floating.getBoundingClientRect(),T=s.split("-")[0],O=n>A.right-A.width/2,R=c>A.bottom-A.height/2,S=function(e,t){return e[0]>=t.x&&e[0]<=t.x+t.width&&e[1]>=t.y&&e[1]<=t.y+t.height}(h,N),x=A.width>N.width,I=A.height>N.height,L=(x?N:A).left,w=(x?N:A).right,D=(I?N:A).top,M=(I?N:A).bottom;if(y&&(o=!0,!b))return;if(C&&(o=!1),C&&!b)return void(o=!0);if(b&&be(e.relatedTarget)&&ke(d.floating,e.relatedTarget))return;if(f&&xt(f.nodesRef.current,v).some(e=>{let{context:t}=e;return null==t?void 0:t.open}))return;if("top"===T&&c>=N.bottom-1||"bottom"===T&&c<=N.top+1||"left"===T&&n>=N.right-1||"right"===T&&n<=N.left+1)return p();let P=[];switch(T){case"top":P=[[L,N.top+1],[L,A.bottom-1],[w,A.bottom-1],[w,N.top+1]];break;case"bottom":P=[[L,A.top+1],[L,N.bottom-1],[w,N.bottom-1],[w,A.top+1]];break;case"left":P=[[A.right-1,M],[A.right-1,D],[N.left+1,D],[N.left+1,M]];break;case"right":P=[[N.right-1,M],[N.right-1,D],[A.left+1,D],[A.left+1,M]]}if(!jt([E,g],P)){if(o&&!S)return p();if(!b&&r){const t=function(e,t){const n=performance.now(),r=n-l;if(null===a||null===u||0===r)return a=e,u=t,l=n,null;const i=e-a,o=t-u,c=Math.sqrt(i*i+o*o);return a=e,u=t,l=n,c/r}(e.clientX,e.clientY);if(null!==t&&t<.1)return p()}jt([E,g],function(e){let[n,r]=e;switch(T){case"top":return[[x?n+t/2:O?n+4*t:n-4*t,r+t+1],[x?n-t/2:O?n+4*t:n-4*t,r+t+1],[A.left,O||x?A.bottom-t:A.top],[A.right,O?x?A.bottom-t:A.top:A.bottom-t]];case"bottom":return[[x?n+t/2:O?n+4*t:n-4*t,r-t],[x?n-t/2:O?n+4*t:n-4*t,r-t],[A.left,O||x?A.top+t:A.bottom],[A.right,O?x?A.top+t:A.bottom:A.top+t]];case"left":{const e=[n+t+1,I?r+t/2:R?r+4*t:r-4*t],i=[n+t+1,I?r-t/2:R?r+4*t:r-4*t];return[[R||I?A.right-t:A.left,A.top],[R?I?A.right-t:A.left:A.right-t,A.bottom],e,i]}case"right":return[[n-t,I?r+t/2:R?r+4*t:r-4*t],[n-t,I?r-t/2:R?r+4*t:r-4*t],[R||I?A.left+t:A.right,A.top],[R?I?A.left+t:A.right:A.left+t,A.bottom]]}}([n,c]))?!o&&r&&(i=window.setTimeout(p,40)):p()}}};return c.__options={blockPointerEvents:n},c}var Yt,zt,Vt,Gt,qt,Wt,Kt,Xt,$t,Qt,Zt,Jt,en,tn,nn,rn,on,an,un,ln,cn,sn,dn,mn,vn,fn,pn,En,gn,hn,_n,bn,yn,Cn,Nn,An,Tn,On,Rn,Sn,xn,In,Ln,wn,Dn,Mn,Pn,kn,Bn,Un,Fn,jn,Hn,Yn,zn,Vn,Gn,qn,Wn,Kn,Xn,$n,Qn,Zn,Jn,er,tr,nr,rr,ir,or,ar,ur,lr,cr,sr,dr,mr,vr,fr,pr,Er,gr,hr,_r,br,yr,Cr,Nr,Ar,Tr,Or,Rr,Sr,xr,Ir,Lr,wr,Dr,Mr,Pr,kr,Br,Ur,Fr,jr,Hr,Yr,zr,Vr,Gr,qr,Wr,Kr,Xr,$r,Qr,Zr,Jr,ei,ti,ni,ri,ii,oi,ai,ui,li,ci,si,di,mi,vi,fi,pi,Ei,gi,hi,_i,bi,yi,Ci,Ni,Ai,Ti,Oi,Ri,Si,xi,Ii,Li,wi,Di,Mi,Pi,ki,Bi,Ui,Fi,ji,Hi,Yi,zi,Vi,Gi,qi,Wi,Ki,Xi,$i,Qi,Zi,Ji,eo,to,no,ro,io,oo,ao,uo,lo,co,so,mo,vo,fo,po,Eo,go,ho,_o,bo,yo,Co,No,Ao,To,Oo,Ro,So,xo,Io,Lo,wo,Do,Mo,Po,ko,Bo,Uo,Fo,jo,Ho,Yo,zo,Vo,Go,qo,Wo,Ko,Xo,$o,Qo,Zo,Jo;n.r(o),n.d(o,{Accordion:()=>ta,AccordionContent:()=>da,AccordionIcon:()=>va,AccordionItem:()=>ra,AccordionToggleTitle:()=>pa,AccordionTrigger:()=>fa,AddOnCard:()=>hc,Alert:()=>ba,Banner:()=>ss,BannerCopy:()=>ds,BannerImageContainer:()=>vs,Button:()=>Aa,Card:()=>ga,CardCarousel:()=>Oa,Carousel:()=>Ia,CarouselArrows:()=>La,CarouselContent:()=>wa,CarouselItem:()=>Da,CarouselPagination:()=>Ma,Checkbox:()=>Ha,CheckboxCard:()=>Ec,CheckboxCardBody:()=>yc,CheckboxCardInput:()=>vc,CheckboxCardPrice:()=>Cc,CheckboxInput:()=>ka,ComboBox:()=>za,ComboBoxDropDown:()=>qa,ComboBoxInput:()=>Ka,ComboBoxOption:()=>Xa,Container:()=>ru,Divider:()=>$a,DockBar:()=>ys,DynamicRadioContent:()=>Qa,Fixed:()=>Cs,FooterLegal:()=>eu,Footnote:()=>gs,FootnoteContent:()=>_s,FootnoteTrigger:()=>hs,FormControl:()=>Oc,FormGroup:()=>nu,GenericStep:()=>uu,GenericStepHeading:()=>su,HardStop:()=>fu,HardStopMessage:()=>Eu,HardStopTitle:()=>pu,Heading:()=>cu,HeadingStep:()=>dc,ICONS:()=>la,Icon:()=>ma,IconButton:()=>xc,IconLink:()=>Su,InputError:()=>Fa,InputText:()=>wc,Label:()=>Ac,Link:()=>Ru,ListItem:()=>Za,Loader:()=>Nu,Modal:()=>Mc,ModalBody:()=>kc,ModalContent:()=>jc,ModalFooter:()=>Yc,ModalHeader:()=>Vc,PillFilter:()=>Iu,Popover:()=>qc,PopoverContent:()=>Qc,PopoverTrigger:()=>Xc,Price:()=>Mu,ProgressStep:()=>ns,RadioButton:()=>Vu,RadioButtonInput:()=>Yu,RadioCard:()=>Fu,RadioCardBody:()=>Gu,RadioCardCarousel:()=>_c,RadioCardInput:()=>ku,RadioCardPartialCarousel:()=>bc,RadioCardPrice:()=>qu,RadioColor:()=>ws,RadioColorInput:()=>Rs,RangeSlider:()=>Ku,RangeSliderInput:()=>Xu,RangeSliderMarkers:()=>Qu,RangeSliderOption:()=>Zu,RatePlanCard:()=>Ju,SameHeightGroup:()=>Ts,SameHeightItem:()=>Os,Select:()=>rc,SelectCustom:()=>Jl,SelectDropdown:()=>tc,SelectNativeHidden:()=>Ql,SelectOption:()=>ic,SessionTimerModal:()=>as,SimpleFooter:()=>iu,SimpleHeader:()=>ou,SrOnly:()=>ha,Static:()=>Ns,Step:()=>is,Tab:()=>cc,TabList:()=>uc,TabPanel:()=>sc,Tabs:()=>ac,Tag:()=>ju,Text:()=>Ba,TimerBox:()=>os,default:()=>Ds,iconChecker:()=>ca,useBodyHeightObserver:()=>sa,useHeightResizeObserver:()=>aa,useWindowResize:()=>ia}),Yt=n(155),zt=n.t(Yt,2),Vt=n.n(Yt);const ea=(0,Yt.createContext)({activeItems:null,toggleAccordionItems:null}),ta=({mode:e,children:t,onActive:n,onInactive:r})=>{const[i,o]=(0,Yt.useState)([]);return(0,Yt.useEffect)(()=>{0===i.length?r&&r():n&&n()},[i]),Vt().createElement(ea.Provider,{value:{activeItems:i,toggleAccordionItems:t=>{"multiple"===e?i.includes(t)?o(e=>e.filter(e=>e!==t)):o(e=>[...e,t]):i.includes(t)?o([]):(o([]),o(e=>[...e,t]))}}},t)},na=(0,Yt.createContext)(null),ra=({children:e,index:t,activeByDefault:n})=>{const{toggleAccordionItems:r}=(0,Yt.useContext)(ea);return(0,Yt.useEffect)(()=>{n&&r&&r(t)},[n]),Vt().createElement(na.Provider,{value:t},e)},ia=(e=300)=>{const[t,n]=(0,Yt.useState)({width:window.innerWidth,height:window.innerHeight});return(0,Yt.useEffect)(()=>{const t=()=>{let t=null;t&&clearTimeout(t),t=setTimeout(()=>{n({width:window.innerWidth,height:window.innerHeight})},e)};return window.addEventListener("resize",t),()=>{window.removeEventListener("resize",t)}},[e]),t};Gt=new WeakMap,qt=new WeakMap,Wt={},Kt=0,Xt=function(e){return e&&(e.host||Xt(e.parentNode))},$t=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),i=t||function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body}(e);return i?(r.push.apply(r,Array.from(i.querySelectorAll("[aria-live]"))),function(e,t,n,r){var i,o,a,u,l,c,s=function(e,t){return t.map(function(t){if(e.contains(t))return t;var n=Xt(t);return n&&e.contains(n)?n:null}).filter(function(e){return Boolean(e)})}(t,Array.isArray(e)?e:[e]);return Wt[n]||(Wt[n]=new WeakMap),i=Wt[n],o=[],a=new Set,u=new Set(s),l=function(e){e&&!a.has(e)&&(a.add(e),l(e.parentNode))},s.forEach(l),c=function(e){e&&!u.has(e)&&Array.prototype.forEach.call(e.children,function(e){if(a.has(e))c(e);else try{var t=e.getAttribute(r),u=null!==t&&"false"!==t,l=(Gt.get(e)||0)+1,s=(i.get(e)||0)+1;Gt.set(e,l),i.set(e,s),o.push(e),1===l&&u&&qt.set(e,!0),1===s&&e.setAttribute(n,"true"),u||e.setAttribute(r,"true")}catch(t){}})},c(t),a.clear(),Kt++,function(){o.forEach(function(e){var t=Gt.get(e)-1,o=i.get(e)-1;Gt.set(e,t),i.set(e,o),t||(qt.has(e)||e.removeAttribute(r),qt.delete(e)),o||e.removeAttribute(n)}),--Kt||(Gt=new WeakMap,Gt=new WeakMap,qt=new WeakMap,Wt={})}}(r,i,n,"aria-hidden")):function(){return null}};const oa=(e,t)=>{(0,Yt.useEffect)(()=>{if(t)return;document.body.hasAttribute("data-active-modals")||(document.body.dataset.activeModals="0");let n,r=Number(document.body.dataset.activeModals);if(r<1){document.body.hasAttribute("data-overflow")||(document.body.dataset.overflow=document.body.style.overflow),document.body.hasAttribute("data-padding-right")||(document.body.dataset.paddingRight=document.body.style.paddingRight);const t=window.innerWidth-document.body.offsetWidth;document.body.style.overflow="hidden",document.body.classList.add("modal-open"),t>0&&(document.body.style.paddingRight=`${t}px`),e.current&&(n=$t(e.current))}return document.body.dataset.activeModals=(r+1).toString(),()=>{let e=Number(document.body.dataset.activeModals);e--,e<1&&(document.body.style.overflow=document.body.dataset.overflow||"",document.body.style.paddingRight=document.body.dataset.paddingRight||"",document.body.classList.contains("modal-open")&&document.body.classList.remove("modal-open"),setTimeout(()=>{n&&n()},0)),document.body.dataset.activeModals=e.toString()}},[t,e.current])},aa=(e,t,n)=>{(0,Yt.useEffect)(()=>{const r=new ResizeObserver(r=>{for(const i of r)if(i.target===e.current){const e=i.contentRect.height;e!==t&&n(e)}});return e.current&&r.observe(e.current),()=>{r.disconnect()}},[])},ua=(e,t)=>(0,Yt.useMemo)(()=>e<768?t.mobile:e>=768&&e<992?t.tablet:t.desktop,[e,t]),la={vi_logo:{icon:"vi_vrui",name:"vi_logo"},vi_4g:{icon:"vi_vrui",name:"vi_4g"},"vi_4gnt-d":{icon:"vi_vrui",name:"vi_4GNT-D"},vi_arrow:{icon:"vi_vrui",name:"vi_arrow"},vi_box:{icon:"vi_vrui",name:"vi_box"},vi_box_sc:{icon:"vi_vrui",name:"vi_box_sc"},vi_chat:{icon:"vi_vrui",name:"vi_chat"},"vi_intt-d":{icon:"vi_vrui",name:"vi_INTT-D"},vi_int:{icon:"vi_vrui",name:"vi_INT"},vi_cwt:{icon:"vi_vrui",name:"vi_CWT"},vi_delivery:{icon:"vi_vrui",name:"vi_delivery"},vi_delivery_sc:{icon:"vi_vrui",name:"vi_delivery_sc"},vi_hotspot:{icon:"vi_vrui",name:"vi_hotspot"},"vi_htsp-d":{icon:"vi_vrui",name:"vi_HTSP-D"},vi_phonecall:{icon:"vi_vrui",name:"vi_phonecall"},vi_call:{icon:"vi_vrui",name:"vi_CALL"},vi_piggy:{icon:"vi_vrui",name:"vi_piggy"},vi_piggy_sc:{icon:"vi_vrui",name:"vi_piggy_sc"},vi_play:{icon:"vi_vrui",name:"vi_play"},vi_sds:{icon:"vi_vrui",name:"vi_SDS"},vi_hds:{icon:"vi_vrui",name:"vi_HDS"},vi_play_sc:{icon:"vi_vrui",name:"vi_play_sc"},vi_youll_love_us:{icon:"vi_vrui",name:"vi_youll_love_us"},vi_youll_love_us_sc:{icon:"vi_vrui",name:"vi_youll_love_us_sc"},vi_expand_plus_c_tk:{icon:"vi_vrui",name:"vi_expand_plus_c_tk"},vi_collapse_minus_c_tk:{icon:"vi_vrui",name:"vi_collapse_minus_c_tk"},vi_close:{icon:"vi_vrui",name:"vi_close"},vi_arrow_left_c:{icon:"vi_vrui",name:"vi_arrow_left_c"},vi_arrow_right_c:{icon:"vi_vrui",name:"vi_arrow_right_c"},vi_info_cf:{icon:"vi_vrui",name:"vi_info_cf"},vi_error_c_tk:{icon:"vi_vrui",name:"vi_error_c_tk"},vi_edit:{icon:"vi_vrui",name:"vi_edit"},vi_check_c:{icon:"vi_vrui",name:"vi_check_c"},vi_arrow_up:{icon:"vi_vrui",name:"vi_arrow_up"},vi_arrow_down:{icon:"vi_vrui",name:"vi_arrow_down"},vi_check_c_tk:{icon:"vi_vrui",name:"vi_check_c_tk"},vi_cart_mc:{icon:"vi_vrui",name:"vi_cart_mc"},vi_scan_barcode_mc:{icon:"vi_vrui",name:"vi_scan_barcode_mc"},vi_checked:{icon:"vi_vrui",name:"vi_checked"},vi_arrow_dropdown_up_f:{icon:"vi_vrui",name:"vi_arrow_dropdown_up_f"},vi_arrow_dropdown_f:{icon:"vi_vrui",name:"vi_arrow_dropdown_f"},vi_mobile_loader_mc:{icon:"vi_vrui",name:"vi_mobile_loader_mc"},vi_popcorn_loader_mc:{icon:"vi_vrui",name:"vi_popcorn_loader_mc"},vi_suitcase_loader_mc:{icon:"vi_vrui",name:"vi_suitcase_loader_mc"},vi_tv_loader_mc:{icon:"vi_vrui",name:"vi_tv_loader_mc"},vi_small_check:{icon:"vi_vrui",name:"vi_small_check"},vi_check_tk:{icon:"vi_vrui",name:"vi_check_tk"},vi_print_tk:{icon:"vi_vrui",name:"vi_print_tk"},vi_info_c_tk:{icon:"vi_vrui",name:"vi_info_c_tk"},vi_warning_c:{icon:"vi_vrui",name:"vi_warning_c"},vi_promo_f:{icon:"vi_vrui",name:"vi_promo_f"},vi_info_mc:{icon:"vi_vrui",name:"vi_info_mc"},vi_check_cf:{icon:"vi_vrui",name:"vi_check_cf"},vi_close_mc:{icon:"vi_vrui",name:"vi_close_mc"},vi_information_tn:{icon:"vi_vrui",name:"vi_information_tn"},vi_coin_dollar_mc:{icon:"vi_vrui",name:"vi_coin_dollar_mc"},vi_5gn:{icon:"vi_vrui",name:"vi_5GN"},vi_3gn:{icon:"vi_vrui",name:"vi_3GN"},vi_download:{icon:"vi_vrui",name:"vi_download"},vi_expand_plus_c_tn:{icon:"vi_vrui",name:"vi_expand_plus_c_tn"},vi_collapse_minus_c_tn:{icon:"vi_vrui",name:"vi_collapse_minus_c_tn"},vi_search:{icon:"vi_vrui",name:"vi_search"},vi_remove_cf:{icon:"vi_vrui",name:"vi_remove_cf"},"vi_arrow-right":{icon:"vi_vrui",name:"vi_arrow-right"},vi_sim_card_cf:{icon:"vi_vrui",name:"vi_sim_card_cf"},vi_activation_esim_cf:{icon:"vi_vrui",name:"vi_activation_esim_cf"},vi_deals:{icon:"vi_vrui",name:"vi_deals"},vi_small_check_v2:{icon:"vi_vrui",name:"vi_small_check_v2"},vi_small_warning:{icon:"vi_vrui",name:"vi_small_warning"},vi_movie_camera:{icon:"vi_vrui",name:"vi_movie_camera"},vi_mobile_phone_hd:{icon:"vi_vrui",name:"vi_mobile_phone_hd"},vi_delivery_tn:{icon:"vi_vrui",name:"vi_delivery_tn"},vi_dollar_sign_tn:{icon:"vi_vrui",name:"vi_dollar_sign_tn"},vi_circular_arrow_box:{icon:"vi_vrui",name:"vi_circular_arrow_box"},vi_camera_mc:{icon:"vi_vrui",name:"vi_camera_mc"},vi_box_tn_mc:{icon:"vi_vrui",name:"vi_box_tn_mc"},vi_deals_tn_mc:{icon:"vi_vrui",name:"vi_deals_tn_mc"},vi_delivery_tn_mc:{icon:"vi_vrui",name:"vi_delivery_tn_mc"},vi_safe_tn_mc:{icon:"vi_vrui",name:"vi_safe_tn_mc"}},ca=e=>{const t=`vi_${e.toLowerCase()}`;return la[t]?.name},sa=(e=300)=>{const[t,n]=(0,Yt.useState)(0);return(0,Yt.useEffect)(()=>{const t=new ResizeObserver(t=>{const r=t[0].target;let i=null;i&&clearTimeout(i),i=setTimeout(()=>{n(r.scrollHeight)},e)});return t.observe(document.body),()=>{t.disconnect()}},[e]),t},da=({children:e,id:t,className:n,collapseHeight:r,expandHeight:i,"aria-labelledby":o,...a})=>{const{activeItems:u}=(0,Yt.useContext)(ea),l=(0,Yt.useContext)(na),c=(0,Yt.useRef)(null),s=u?.includes(l),[d,m]=(0,Yt.useState)(c.current?.scrollHeight||0),v=i?parseInt(i,10):d,f=s?`${Math.min(d,v)}px`:r;return aa(c,d,m),Vt().createElement("div",{role:"region",className:["vrui-overflow-hidden vrui-transition-height vrui-ease-in-out vrui-duration-200 vrui-h-0 vrui-group vrui-max-h-full print:!vrui-visible print:!vrui-h-full",r?.trim()?"vrui-scrollbar":"",i?.trim()?"vrui-scrollbar":"",u?.includes(l)||r?"":"vrui-collapse",n].join(" ").trim(),id:t,"aria-labelledby":o,...a,style:{height:f}},Vt().createElement("div",{className:[s||i?"":"vrui-hidden","print:!vrui-block"].join(" ").trim(),ref:c},e))},ma=({iconClass:e,iconName:t,className:n,...r})=>Vt().createElement("span",{className:[t,e,n].join(" ").trim(),role:"img","aria-hidden":"true","aria-label":" ",...r}),va=({iconCollapse:e,iconExpand:t,iconClass:n,className:r})=>{const{activeItems:i}=(0,Yt.useContext)(ea),o=(0,Yt.useContext)(na);return Vt().createElement(ma,{className:r,iconClass:i?.includes(o)?t:e,iconName:n})},fa=({children:e,id:t,className:n,"aria-controls":r,...i})=>{const{activeItems:o,toggleAccordionItems:a}=(0,Yt.useContext)(ea),u=(0,Yt.useContext)(na);return Vt().createElement("button",{"aria-expanded":!!o?.includes(u),className:["focus-visible:vrui-outline-blue focus-visible:vrui-outline focus-visible:vrui-outline-2 focus-visible:vrui-outline-offset-3 focus-visible:vrui-rounded-6",n].join(" ").trim(),onClick:()=>{a&&a(u)},id:t,"aria-controls":r,...i},e)},pa=({titleCollapse:e,titleExpand:t,className:n})=>{const{activeItems:r}=(0,Yt.useContext)(ea),i=(0,Yt.useContext)(na);return Vt().createElement("div",{className:n},r?.includes(i)?e:t)},Ea={gray:"vrui-bg-gray-1",red:"vrui-bg-pink",yellow:"vrui-bg-yellow vrui-border vrui-border-yellow-1",solidGray:"vrui-bg-gray-1",solidBlue:"vrui-bg-blue",solidRed:"vrui-bg-red-2",solidYellow:"vrui-bg-yellow vrui-border vrui-border-yellow-1",solidWhite:"vrui-bg-white",default:""},ga=({variant:e,radius:t,children:n,className:r,defaultPadding:i=!1,...o})=>Vt().createElement("div",{className:[Ea[e],t?"vrui-rounded-16":"",i?"vrui-p-16":"",r].join(" ").trim(),...o},n),ha=({children:e,className:t})=>Vt().createElement("span",{className:["vrui-sr-only",t].join(" ").trim()},e),_a={error:{alertCss:"",iconCss:"vrui-text-red",srText:"Error",iconName:"vi_warning_c",cardVariant:"red"},warning:{alertCss:"",iconCss:"vrui-text-yellow-1",srText:"Warning",iconName:"vi_warning_c",cardVariant:"yellow"},warningWhite:{alertCss:"vrui-border",iconCss:"vrui-text-yellow-1",srText:"Warning",iconName:"vi_warning_c",cardVariant:"default"},success:{alertCss:"vrui-border",iconCss:"vrui-text-green",iconName:"vi_check_c",srText:"Success",cardVariant:"default"},info:{alertCss:"vrui-border",iconCss:"vrui-text-darkblue",iconName:"vi_info_c_tk",srText:"Information",cardVariant:"default"}},ba=({variant:e,children:t,className:n,iconSize:r="32",role:i,screenReaderText:o,...a})=>Vt().createElement(ga,{className:[_a[e].alertCss,n].join(" ").trim(),variant:_a[e].cardVariant,role:i||"alert",...a},Vt().createElement("div",null,Vt().createElement(ma,{iconName:_a[e].iconName,iconClass:"vi_vrui",className:_a[e].iconCss,role:"img","aria-hidden":"true",style:{fontSize:`${r}px`}}),Vt().createElement(ha,null,o||_a[e].srText)),t),ya={solidRed:"vrui-font-poppins-Semibold vrui-inline-block vrui-rounded-4 vrui-bg-red vrui-text-white vrui-border-red vrui-border-2 vrui-border-solid enabled:hover:vrui-bg-red-1 enabled:hover:vrui-border-red-1 focus:vrui-bg-red-1 focus:vrui-border-red-1 focus:vrui-outline-blue focus:vrui-outline focus:vrui-outline-2 focus:vrui-outline-offset-3 disabled:vrui-opacity-40",outlinedBlack:"vrui-font-poppins-Semibold vrui-inline-block vrui-rounded-4 vrui-bg-transparent vrui-text-darkblue  vrui-border-darkblue vrui-border-2 vrui-border-solid enabled:hover:vrui-bg-transparent-1 focus:vrui-outline-blue focus:vrui-outline focus:vrui-outline-2 focus:vrui-outline-offset-3 disabled:vrui-opacity-40",outlinedGray:"vrui-font-poppins-Regular vrui-rounded-6 vrui-bg-transparent vrui-text-darkblue vrui-border-gray-3 vrui-border-1 vrui-border-solid hover:vrui-bg-transparent-1 focus:vrui-outline-blue focus:vrui-outline focus:vrui-outline-2 focus:vrui-outline-offset-3 vrui-inline-block",solidWhite:"vrui-font-poppins-Semibold vrui-inline-block vrui-rounded-4 vrui-bg-white vrui-text-darkblue vrui-border-white vrui-border-2 vrui-border-solid enabled:hover:vrui-bg-pink enabled:hover:vrui-border-pink focus:vrui-bg-pink focus:vrui-border-pink focus:vrui-outline-white focus:vrui-outline focus:vrui-outline-2 focus:vrui-outline-offset-3 disabled:vrui-opacity-40",outlinedWhite:"vrui-font-poppins-Semibold vrui-inline-block vrui-rounded-4 vrui-bg-transparent vrui-text-white  vrui-border-white vrui-border-2 vrui-border-solid enabled:hover:vrui-bg-transparent-1 focus:vrui-bg-transparent-1 focus:vrui-outline-white focus:vrui-outline focus:vrui-outline-2 focus:vrui-outline-offset-3 disabled:vrui-opacity-40",textRed:"vrui-font-poppins-Semibold vrui-inline-block vrui-rounded-4 vrui-bg-transparent vrui-text-red vrui-underline vrui-underline-offset-4 enabled:hover:vrui-text-red-1 enabled:hover:vrui-no-underline focus:vrui-text-red-1 focus:vrui-no-underline focus:vrui-outline-blue focus:vrui-outline focus:vrui-outline-2 focus:vrui-outline-offset-3 !vrui-p-0 disabled:vrui-opacity-40",textBlue:"vrui-font-poppins-Semibold vrui-inline-block vrui-rounded-4 vrui-bg-transparent vrui-text-blue vrui-underline vrui-underline-offset-4 enabled:hover:vrui-text-blue-1 enabled:hover:vrui-no-underline focus:vrui-text-blue-1 focus:vrui-no-underline focus:vrui-outline-blue focus:vrui-outline focus:vrui-outline-2 focus:vrui-outline-offset-3 !vrui-p-0 disabled:vrui-opacity-40",textWhite:"vrui-font-poppins-Semibold vrui-inline-block vrui-bg-transparent vrui-text-white vrui-underline vrui-underline-offset-4 enabled:hover:vrui-text-pink enabled:hover:vrui-no-underline focus:vrui-text-pink focus:vrui-no-underline focus:vrui-outline-white focus:vrui-outline focus:vrui-outline-2 focus:vrui-outline-offset-3 !vrui-p-0 disabled:vrui-opacity-40",icon:"focus:vrui-outline-blue focus:vrui-outline focus:vrui-outline-2 focus:vrui-outline-offset-3",default:""},Ca={regular:"vrui-text-16 vrui-leading-18 vrui-py-11 vrui-px-28",small:"vrui-text-14 vrui-leading-18 vrui-py-5 vrui-px-14",default:""},Na=(0,Yt.forwardRef)(function({variant:e="solidRed",size:t="regular",className:n,...r},i){const o=(0,Yt.useMemo)(()=>({buttonStyle:[Ca[t],ya[e],n].join(" ").trim()}),[e,t]);return Vt().createElement("button",{className:o.buttonStyle,...r,ref:i})}),Aa=Na;Qt="(prefers-reduced-motion: reduce)",Zt=setTimeout,Jt=function(){},en=Array.isArray,tn=r(a,"function"),nn=r(a,"string"),rn=r(a,"undefined"),on=Object.keys,un="data-"+(an="splide"),ln=Math.min,cn=Math.max,sn=Math.floor,dn=Math.ceil,mn=Math.abs,vn={},fn="mounted",pn="ready",En="move",gn="moved",hn="click",_n="active",bn="inactive",yn="visible",Cn="hidden",Nn="refresh",An="updated",Tn="resize",On="resized",Rn="drag",Sn="dragging",xn="dragged",In="scroll",Ln="scrolled",wn="destroy",Dn="arrows:mounted",Mn="arrows:updated",Pn="pagination:mounted",kn="pagination:updated",Bn="navigation:mounted",Un="autoplay:play",Fn="autoplay:playing",jn="autoplay:pause",Hn="lazyload:loaded",Yn="ei",Kn="ttb",Xn={width:["height"],left:["top","right"],right:["bottom","left"],x:["y"],X:["Y"],Y:["X"],ArrowLeft:[qn=(zn="Arrow")+"Up",Gn=zn+"Right"],ArrowRight:[Wn=zn+"Down",Vn=zn+"Left"]},tr=(Zn="aria-")+"selected",ur=Zn+"live",lr=Zn+"busy",cr=Zn+"atomic",sr=[$n="role",Qn="tabindex","disabled",Jn=Zn+"controls",er=Zn+"current",nr=Zn+"label",rr=Zn+"labelledby",ir=Zn+"hidden",or=Zn+"orientation",ar=Zn+"roledescription"],vr=an,fr=(dr=an+"__")+"track",pr=dr+"list",hr=(Er=dr+"slide")+"__container",Tr=dr+"progress__bar",Or=dr+"toggle",Rr=dr+"sr",Sr=(mr="is-")+"initialized",kr=[xr=mr+"active",wr=mr+"visible",Ir=mr+"prev",Lr=mr+"next",Dr=mr+"loading",Mr=mr+"focus-in",Pr=mr+"overflow"],Br={slide:Er,clone:gr=Er+"--clone",arrows:_r=dr+"arrows",arrow:br=dr+"arrow",prev:yr=br+"--prev",next:Cr=br+"--next",pagination:Nr=dr+"pagination",page:Ar=Nr+"__page",spinner:dr+"spinner"},Ur="touchstart mousedown",Fr="touchmove mousemove",jr="touchend touchcancel mouseup click",Hr="slide",Yr="loop",zr="fade",Vr=un+"-interval",Gr={passive:!1,capture:!0},qr={Spacebar:" ",Right:Gn,Left:Vn,Up:qn,Down:Wn},Wr="keydown",$r="["+(Kr=un+"-lazy")+"], ["+(Xr=Kr+"-srcset")+"]",Qr=[" ","Enter"],Zr=Object.freeze({__proto__:null,Media:function(e,t,n){function r(e){e&&s.destroy()}function i(e,t){var n=matchMedia(t);s.bind(n,"change",o),d.push([e,n])}function o(){var t=u.is(7),i=n.direction,o=d.reduce(function(e,t){return N(e,t[1].matches?t[0]:{})},{});A(n),a(o),n.destroy?e.destroy("completely"===n.destroy):t?(r(!0),e.mount()):i!==n.direction&&e.refresh()}function a(t,r,i){N(n,t),r&&N(Object.getPrototypeOf(n),t),!i&&u.is(1)||e.emit(An,n)}var u=e.state,l=n.breakpoints||{},c=n.reducedMotion||{},s=X(),d=[];return{setup:function(){var e="min"===n.mediaQuery;on(l).sort(function(t,n){return e?+t-+n:+n-+t}).forEach(function(t){i(l[t],"("+(e?"min":"max")+"-width:"+t+"px)")}),i(c,Qt),o()},destroy:r,reduce:function(e){matchMedia(Qt).matches&&(e?N(n,c):A(n,on(c)))},set:a}},Direction:function(e,t,n){return{resolve:function(e,t,r){var i="rtl"!==(r=r||n.direction)||t?r===Kn?0:-1:1;return Xn[e]&&Xn[e][i]||e.replace(/width|left|right/i,function(e,t){var n=Xn[e.toLowerCase()][i]||e;return t>0?n.charAt(0).toUpperCase()+n.slice(1):n})},orient:function(e){return e*("rtl"===n.direction?1:-1)}}},Elements:function(t,n,r){function i(){var e,t,n;c=u("."+fr),s=b(c,"."+pr),Y(c&&s,"A track/list element is missing."),v(S,_(s,"."+Er+":not(."+gr+")")),y({arrows:_r,pagination:Nr,prev:yr,next:Cr,bar:Tr,toggle:Or},function(e,t){R[t]=u("."+e)}),C(R,{root:N,track:c,list:s,slides:S}),t=N.id||""+(e=an)+K(vn[e]=(vn[e]||0)+1),n=r.role,N.id=t,c.id=c.id||t+"-track",s.id=s.id||t+"-list",!L(N,$n)&&"SECTION"!==N.tagName&&n&&O(N,$n,n),O(N,ar,A.carousel),O(s,$n,"presentation"),a()}function o(t){var n=sr.concat("style");e(S),F(N,x),F(c,I),T([c,s],n),T(N,t?n:["style",ar])}function a(){F(N,x),F(c,I),x=l(vr),I=l(fr),p(N,x),p(c,I),O(N,nr,r.label),O(N,rr,r.labelledby)}function u(e){var t=B(N,e);return t&&function(e,t){if(tn(e.closest))return e.closest(t);for(var n=e;n&&1===n.nodeType&&!h(n,t);)n=n.parentElement;return n}(t,"."+vr)===N?t:void 0}function l(e){return[e+"--"+r.type,e+"--"+r.direction,r.drag&&e+"--draggable",r.isNavigation&&e+"--nav",e===vr&&xr]}var c,s,d,m=$(t),E=m.on,g=m.bind,N=t.root,A=r.i18n,R={},S=[],x=[],I=[];return C(R,{setup:i,mount:function(){E(Nn,o),E(Nn,i),E(An,a),g(document,Ur+" keydown",function(e){d="keydown"===e.type},{capture:!0}),g(N,"focusin",function(){f(N,Mr,!!d)})},destroy:o})},Slides:function(t,n,i){function o(){x.forEach(function(e,t){u(e,t,-1)})}function a(){v(function(e){e.destroy()}),e(B)}function u(e,n,i){var o=function(e,t,n,i){function o(){var r=e.splides.map(function(e){var n=e.splide.Components.Slides.getAt(t);return n?n.slide.id:""}).join(" ");O(i,nr,W(y.slideX,(M?n:t)+1)),O(i,Jn,r),O(i,$n,N?"button":""),N&&T(i,ar)}function a(){c||u()}function u(){var n,r;c||(n=e.index,(r=l())!==w(i,xr)&&(f(i,xr,r),O(i,er,h&&r||""),m(r?_n:bn,k)),function(){var t,n=function(){if(e.is(zr))return l();var t=D(p.Elements.track),n=D(i),r=A("left",!0),o=A("right",!0);return sn(t[r])<=dn(n[r])&&sn(n[o])<=dn(t[o])}(),r=!n&&(!l()||M);e.state.is([4,5])||O(i,ir,r||""),O(U(i,g.focusableNodes||""),Qn,r?-1:""),N&&O(i,Qn,r?-1:0),n!==w(i,wr)&&(f(i,wr,n),m(n?yn:Cn,k)),n||document.activeElement!==i||(t=p.Slides.getAt(e.index))&&I(t.slide)}(),f(i,Ir,t===n-1),f(i,Lr,t===n+1))}function l(){var r=e.index;return r===t||g.cloneStatus&&r===n}var c,s=$(e),d=s.on,m=s.emit,v=s.bind,p=e.Components,E=e.root,g=e.options,h=g.isNavigation,_=g.updateOnMove,y=g.i18n,C=g.pagination,N=g.slideFocus,A=p.Direction.resolve,R=L(i,"style"),x=L(i,nr),M=n>-1,P=b(i,"."+hr),k={index:t,slideIndex:n,slide:i,container:P,isClone:M,mount:function(){M||(i.id=E.id+"-slide"+K(t+1),O(i,$n,C?"tabpanel":"group"),O(i,ar,y.slide),O(i,nr,x||W(y.slideLabel,[t+1,e.length]))),v(i,"click",r(m,hn,k)),v(i,"keydown",r(m,"sk",k)),d([gn,"sh",Ln],u),d(Bn,o),_&&d(En,a)},destroy:function(){c=!0,s.destroy(),F(i,kr),T(i,sr),O(i,"style",R),O(i,nr,x||"")},update:u,style:function(e,t,n){S(n&&P||i,e,t)},isWithin:function(n,r){var i=mn(n-t);return M||!g.rewind&&!e.is(Yr)||(i=ln(i,e.length-i)),i<=r}};return k}(t,n,i,e);o.mount(),B.push(o),B.sort(function(e,t){return e.index-t.index})}function l(e){return e?_(function(e){return!e.isClone}):B}function v(e,t){l(t).forEach(e)}function _(e){return B.filter(tn(e)?e:function(t){return nn(e)?h(t.slide,e):m(s(e),t.index)})}var y=$(t),C=y.on,N=y.emit,A=y.bind,R=n.Elements,x=R.slides,k=R.list,B=[];return{mount:function(){o(),C(Nn,a),C(Nn,o)},destroy:a,update:function(){v(function(e){e.update()})},register:u,get:l,getIn:function(e){var t=n.Controller,r=t.toIndex(e),o=t.hasFocus()?1:i.perPage;return _(function(e){return V(e.index,r,r+o-1)})},getAt:function(e){return _(e)[0]},add:function(e,t){d(e,function(e){var n,o,a,u,l;nn(e)&&(e=P(e)),c(e)&&((n=x[t])?g(e,n):E(k,e),p(e,i.classes.slide),o=e,a=r(N,Tn),u=U(o,"img"),(l=u.length)?u.forEach(function(e){A(e,"load error",function(){--l||a()})}):a())}),N(Nn)},remove:function(e){M(_(e).map(function(e){return e.slide})),N(Nn)},forEach:v,filter:_,style:function(e,t,n){v(function(r){r.style(e,t,n)})},getLength:function(e){return e?x.length:B.length},isEnough:function(){return B.length>i.perPage}}},Layout:function(e,t,n){function i(){g=n.direction===Kn,S(R,"maxWidth",H(n.width)),S(x,T("paddingLeft"),a(!1)),S(x,T("paddingRight"),a(!0)),o(!0)}function o(e){var t,r=D(R);(e||h.width!==r.width||h.height!==r.height)&&(S(x,"height",(t="",g&&(Y(t=l(),"height or heightRatio is missing."),t="calc("+t+" - "+a(!1)+" - "+a(!0)+")"),t)),w(T("marginRight"),H(n.gap)),w("width",n.autoWidth?null:H(n.fixedWidth)||(g?"":c())),w("height",H(n.fixedHeight)||(g?n.autoHeight?null:c():l()),!0),h=r,N(On),_!==(_=E())&&(f(R,Pr,_),N("overflow",_)))}function a(e){var t=n.padding,r=T(e?"right":"left");return t&&H(t[r]||(u(t)?0:t))||"0px"}function l(){return H(n.height||D(I).width*n.heightRatio)}function c(){var e=H(n.gap);return"calc((100%"+(e&&" + "+e)+")/"+(n.perPage||1)+(e&&" - "+e)+")"}function s(){return D(I)[T("width")]}function d(e,t){var n=L(e||0);return n?D(n.slide)[T("width")]+(t?0:p()):0}function m(e,t){var n,r,i=L(e);return i?(n=D(i.slide)[T("right")],r=D(I)[T("left")],mn(n-r)+(t?0:p())):0}function v(t){return m(e.length-1)-m(0)+d(0,t)}function p(){var e=L(0);return e&&parseFloat(S(e.slide,T("marginRight")))||0}function E(){return e.is(zr)||v(!0)>s()}var g,h,_,b=$(e),y=b.on,C=b.bind,N=b.emit,A=t.Slides,T=t.Direction.resolve,O=t.Elements,R=O.root,x=O.track,I=O.list,L=A.getAt,w=A.style;return{mount:function(){var e,t;i(),C(window,"resize load",(e=r(N,Tn),t=Q(0,e,null,1),function(){t.isPaused()&&t.start()})),y([An,Nn],i),y(Tn,o)},resize:o,listSize:s,slideSize:d,sliderSize:v,totalSize:m,getPadding:function(e){return parseFloat(S(x,T("padding"+(e?"Right":"Left"))))||0},isOverflow:E}},Clones:function(t,n,r){function i(){d(Nn,o),d([An,Tn],u),(c=l())&&(function(e){var n=f.get().slice(),i=n.length;if(i){for(;n.length<e;)v(n,n);v(n.slice(-e),n.slice(0,e)).forEach(function(o,a){var u=a<e,l=function(e,n){var i=e.cloneNode(!0);return p(i,r.classes.clone),i.id=t.root.id+"-clone"+K(n+1),i}(o.slide,a);u?g(l,n[0].slide):E(m.list,l),v(_,l),f.register(l,a-e+(u?0:i),o.index)})}}(c),n.Layout.resize(!0))}function o(){a(),i()}function a(){M(_),e(_),s.destroy()}function u(){var e=l();c!==e&&(c<e||!e)&&s.emit(Nn)}function l(){var e,i=r.clones;return t.is(Yr)?rn(i)&&(i=(e=r[h("fixedWidth")]&&n.Layout.slideSize(0))&&dn(D(m.track)[h("width")]/e)||r[h("autoWidth")]&&t.length||2*r.perPage):i=0,i}var c,s=$(t),d=s.on,m=n.Elements,f=n.Slides,h=n.Direction.resolve,_=[];return{mount:i,destroy:a}},Move:function(e,t,n){function r(){t.Controller.isBusy()||(t.Scroll.cancel(),i(e.index),t.Slides.update())}function i(e){o(c(e,!0))}function o(n,r){if(!e.is(zr)){var i=r?n:function(n){if(e.is(Yr)){var r=l(n),i=r>t.Controller.getEnd();(r<0||i)&&(n=a(n,i))}return n}(n);S(R,"transform","translate"+A("X")+"("+i+"px)"),n!==i&&p("sh")}}function a(e,t){var n=e-d(t),r=C();return e-T(r*(dn(mn(n)/r)||1))*(t?1:-1)}function u(){o(s(),!0),m.cancel()}function l(e){var n,r,i,o,a,u;for(n=t.Slides.get(),r=0,i=1/0,o=0;o<n.length&&(a=n[o].index,(u=mn(c(a,!0)-e))<=i);o++)i=u,r=a;return r}function c(t,r){var i=T(b(t-1)-function(e){var t=n.focus;return"center"===t?(y()-h(e,!0))/2:+t*h(e)||0}(t));return r?function(t){return n.trimSpace&&e.is(Hr)&&(t=G(t,0,T(C(!0)-y()))),t}(i):i}function s(){var e=A("left");return D(R)[e]-D(x)[e]+T(_(!1))}function d(e){return c(e?t.Controller.getEnd():0,!!n.trimSpace)}var m,v=$(e),f=v.on,p=v.emit,E=e.state.set,g=t.Layout,h=g.slideSize,_=g.getPadding,b=g.totalSize,y=g.listSize,C=g.sliderSize,N=t.Direction,A=N.resolve,T=N.orient,O=t.Elements,R=O.list,x=O.track;return{mount:function(){m=t.Transition,f([fn,On,An,Nn],r)},move:function(e,t,n,r){var i,l;e!==t&&(i=e>n,l=T(a(s(),i)),i?l>=0:l<=R[A("scrollWidth")]-D(x)[A("width")])&&(u(),o(a(s(),e>n),!0)),E(4),p(En,t,n,e),m.start(t,function(){E(3),p(gn,t,n,e),r&&r()})},jump:i,translate:o,shift:a,cancel:u,toIndex:l,toPosition:c,getPosition:s,getLimit:d,exceededLimit:function(e,t){t=rn(t)?s():t;var n=!0!==e&&T(t)<T(d(!1)),r=!1!==e&&T(t)>T(d(!0));return n||r},reposition:r}},Controller:function(e,t,n){function i(){E=S(!0),g=n.perMove,h=n.perPage,p=c();var e=G(M,0,x?p:E-1);e!==M&&(M=e,C.reposition())}function o(){p!==c()&&y(Yn)}function a(e,t){var n=g||(v()?1:h),r=u(M+n*(e?-1:1),M,!(g||v()));return-1===r&&L&&!z(N(),A(!e),1)?e?0:p:t?r:l(r)}function u(t,r,i){if(R()||v()){var o=function(t){if(L&&"move"===n.trimSpace&&t!==M)for(var r=N();r===T(t,!0)&&V(t,0,e.length-1,!n.rewind);)t<M?--t:++t;return t}(t);o!==t&&(r=t,t=o,i=!1),t<0||t>p?t=g||!V(0,t,r,!0)&&!V(p,r,t,!0)?I?i?t<0?-(E%h||h):E:t:n.rewind?t<0?p:0:-1:s(d(t)):i&&t!==r&&(t=s(d(r)+(t<r?-1:1)))}else t=-1;return t}function l(e){return I?(e+E)%E||0:e}function c(){for(var e=E-(v()||I&&g?1:h);x&&e-- >0;)if(T(E-1,!0)!==T(e,!0)){e++;break}return G(e,0,E-1)}function s(e){return G(v()?e:h*e,0,p)}function d(e){return v()?ln(e,p):sn((e>=p?E-1:e)/h)}function m(e){e!==M&&(P=M,M=e)}function v(){return!rn(n.focus)||n.isNavigation}function f(){return e.state.is([4,5])&&!!n.waitForTransition}var p,E,g,h,_=$(e),b=_.on,y=_.emit,C=t.Move,N=C.getPosition,A=C.getLimit,T=C.toPosition,O=t.Slides,R=O.isEnough,S=O.getLength,x=n.omitEnd,I=e.is(Yr),L=e.is(Hr),w=r(a,!1),D=r(a,!0),M=n.start||0,P=M;return{mount:function(){i(),b([An,Nn,Yn],i),b(On,o)},go:function(e,t,n){if(!f()){var r=function(e){var t,n,r,i=M;return nn(e)?(n=(t=e.match(/([+\-<>])(\d+)?/)||[])[1],r=t[2],"+"===n||"-"===n?i=u(M+ +(""+n+(+r||1)),M):">"===n?i=r?s(+r):w(!0):"<"===n&&(i=D(!0))):i=I?e:G(e,0,p),i}(e),i=l(r);i>-1&&(t||i!==M)&&(m(i),C.move(r,i,P,n))}},scroll:function(e,n,r,i){t.Scroll.scroll(e,n,r,function(){var e=l(C.toIndex(N()));m(x?ln(e,p):e),i&&i()})},getNext:w,getPrev:D,getAdjacent:a,getEnd:c,setIndex:m,getIndex:function(e){return e?P:M},toIndex:s,toPage:d,toDest:function(e){var t=C.toIndex(e);return L?G(t,0,p):t},hasFocus:v,isBusy:f}},Arrows:function(e,t,n){function i(){var e;!(e=n.arrows)||L&&w||(I=A||R("div",_.arrows),L=l(!0),w=l(!1),s=!0,E(I,[L,w]),!A&&g(I,S)),L&&w&&(C(D,{prev:L,next:w}),x(I,e?"":"none"),p(I,d=_r+"--"+n.direction),e&&(v([fn,gn,Nn,Ln,Yn],c),f(w,"click",r(u,">")),f(L,"click",r(u,"<")),c(),O([L,w],Jn,S.id),h(Dn,L,w))),v(An,o)}function o(){a(),i()}function a(){m.destroy(),F(I,d),s?(M(A?[L,w]:I),L=w=null):T([L,w],sr)}function u(e){N.go(e,!0)}function l(e){return P('<button class="'+_.arrow+" "+(e?_.prev:_.next)+'" type="button"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40" width="40" height="40" focusable="false"><path d="'+(n.arrowPath||"m15.5 0.932-4.3 4.38 14.5 14.6-14.5 14.5 4.3 4.4 14.6-14.6 4.4-4.3-4.4-4.4-14.6-14.6z")+'" />')}function c(){if(L&&w){var t=e.index,n=N.getPrev(),r=N.getNext(),i=n>-1&&t<n?b.last:b.prev,o=r>-1&&t>r?b.first:b.next;L.disabled=n<0,w.disabled=r<0,O(L,nr,i),O(w,nr,o),h(Mn,L,w,n,r)}}var s,d,m=$(e),v=m.on,f=m.bind,h=m.emit,_=n.classes,b=n.i18n,y=t.Elements,N=t.Controller,A=y.arrows,S=y.track,I=A,L=y.prev,w=y.next,D={};return{arrows:D,mount:i,destroy:a,update:c}},Autoplay:function(e,t,n){function r(){E()&&t.Slides.isEnough()&&(p.start(!n.resetProgress),c=l=C=!1,a(),v(Un))}function i(e){void 0===e&&(e=!0),C=!!e,a(),E()||(p.pause(),v(jn))}function o(){C||(l||c?i(!1):r())}function a(){b&&(f(b,xr,!C),O(b,nr,n.i18n[C?"play":"pause"]))}function u(e){var r=t.Slides.getAt(e);p.set(r&&+L(r.slide,Vr)||n.interval)}var l,c,s=$(e),d=s.on,m=s.bind,v=s.emit,p=Q(n.interval,e.go.bind(e,">"),function(e){var t=g.bar;t&&S(t,"width",100*e+"%"),v(Fn,e)}),E=p.isPaused,g=t.Elements,h=t.Elements,_=h.root,b=h.toggle,y=n.autoplay,C="pause"===y;return{mount:function(){y&&(n.pauseOnHover&&m(_,"mouseenter mouseleave",function(e){l="mouseenter"===e.type,o()}),n.pauseOnFocus&&m(_,"focusin focusout",function(e){c="focusin"===e.type,o()}),b&&m(b,"click",function(){C?r():i(!0)}),d([En,In,Nn],p.rewind),d(En,u),b&&O(b,Jn,g.track.id),C||r(),a())},destroy:p.cancel,play:r,pause:i,isPaused:E}},Cover:function(e,t,n){function i(e){t.Slides.forEach(function(t){var n=b(t.container||t.slide,"img");n&&n.src&&o(e,n,t)})}function o(e,t,n){n.style("background",e?'center/cover no-repeat url("'+t.src+'")':"",!0),x(t,e?"none":"")}var a=$(e).on;return{mount:function(){n.cover&&(a(Hn,r(o,!0)),a([fn,An,Nn],r(i,!0)))},destroy:r(i,!1)}},Scroll:function(e,t,n){function i(e,n,i,l,d){var m,g,_,C=E();u(),!i||b&&h()||(m=t.Layout.sliderSize(),g=q(e)*m*sn(mn(e)/m)||0,e=p.toPosition(t.Controller.toDest(e%m))+g),_=z(C,e,1),y=1,n=_?0:n||cn(mn(e-C)/1.5,800),s=l,c=Q(n,o,r(a,C,e,d),1),f(5),v(In),c.start()}function o(){f(3),s&&s(),v(Ln)}function a(e,t,r,o){var a,u,l=E(),c=(e+(t-e)*(a=o,(u=n.easingFunc)?u(a):1-Math.pow(1-a,4))-l)*y;_(l+c),b&&!r&&h()&&(y*=.6,mn(c)<10&&i(g(h(!0)),600,!1,s,!0))}function u(){c&&c.cancel()}function l(){c&&!c.isPaused()&&(u(),o())}var c,s,d=$(e),m=d.on,v=d.emit,f=e.state.set,p=t.Move,E=p.getPosition,g=p.getLimit,h=p.exceededLimit,_=p.translate,b=e.is(Hr),y=1;return{mount:function(){m(En,u),m([An,Nn],l)},destroy:u,scroll:i,cancel:l}},Drag:function(e,t,n){function r(){var e=n.drag;p(!e),b="free"===e}function i(e){var t,r,i;C=!1,N||(t=f(e),r=e.target,i=n.noDrag,h(r,"."+Ar+", ."+br)||i&&h(r,i)||!t&&e.button||(D.isBusy()?k(e,!0):(A=t?M:window,y=I.is([4,5]),_=null,S(A,Fr,o,Gr),S(A,jr,a,Gr),L.cancel(),w.cancel(),c(e))))}function o(t){if(I.is(6)||(I.set(6),R(Rn)),t.cancelable)if(y){L.translate(E+s(t)/(z&&e.is(Hr)?5:1));var r=d(t)>200,i=z!==(z=Y());(r||i)&&c(t),C=!0,R(Sn),k(t)}else(function(e){return mn(s(e))>mn(s(e,!0))})(t)&&(y=function(e){var t=n.dragMinThreshold,r=u(t),i=r&&t.mouse||0,o=(r?t.touch:+t)||10;return mn(s(e))>(f(e)?o:i)}(t),k(t))}function a(r){I.is(6)&&(I.set(3),R(xn)),y&&(function(r){var i=function(t){if(e.is(Yr)||!z){var n=d(t);if(n&&n<200)return s(t)/n}return 0}(r),o=function(e){return H()+q(e)*ln(mn(e)*(n.flickPower||600),b?1/0:t.Layout.listSize()*(n.flickMaxPages||1))}(i),a=n.rewind&&n.rewindByDrag;P(!1),b?D.scroll(o,0,n.snap):e.is(zr)?D.go(F(q(i))<0?a?"<":"-":a?">":"+"):e.is(Hr)&&z&&a?D.go(Y(!0)?">":"<"):D.go(D.toDest(o),!0),P(!0)}(r),k(r)),x(A,Fr,o),x(A,jr,a),y=!1}function l(e){!N&&C&&k(e,!0)}function c(e){_=g,g=e,E=H()}function s(e,t){return v(e,t)-v(m(e),t)}function d(e){return j(e)-j(m(e))}function m(e){return g===e&&_||g}function v(e,t){return(f(e)?e.changedTouches[0]:e)["page"+U(t?"Y":"X")]}function f(e){return"undefined"!=typeof TouchEvent&&e instanceof TouchEvent}function p(e){N=e}var E,g,_,b,y,C,N,A,T=$(e),O=T.on,R=T.emit,S=T.bind,x=T.unbind,I=e.state,L=t.Move,w=t.Scroll,D=t.Controller,M=t.Elements.track,P=t.Media.reduce,B=t.Direction,U=B.resolve,F=B.orient,H=L.getPosition,Y=L.exceededLimit,z=!1;return{mount:function(){S(M,Fr,Jt,Gr),S(M,jr,Jt,Gr),S(M,Ur,i,Gr),S(M,"click",l,{capture:!0}),S(M,"dragstart",k),O([fn,An],r)},disable:p,isDragging:function(){return y}}},Keyboard:function(e,t,n){function r(){var e=n.keyboard;e&&(u="global"===e?window:v,d(u,Wr,a))}function i(){m(u,Wr)}function o(){var e=l;l=!0,Zt(function(){l=e})}function a(t){if(!l){var n=Z(t);n===f(Vn)?e.go("<"):n===f(Gn)&&e.go(">")}}var u,l,c=$(e),s=c.on,d=c.bind,m=c.unbind,v=e.root,f=t.Direction.resolve;return{mount:function(){r(),s(An,i),s(An,r),s(En,o)},destroy:i,disable:function(e){l=e}}},LazyLoad:function(t,n,i){function o(){e(h),n.Slides.forEach(function(e){U(e.slide,$r).forEach(function(t){var n,r,o,a=L(t,Kr),u=L(t,Xr);a===t.src&&u===t.srcset||(n=i.classes.spinner,o=b(r=t.parentElement,"."+n)||R("span",n,r),h.push([t,e,o]),t.src||x(t,"none"))})}),E?c():(m(g),d(g,a),a())}function a(){(h=h.filter(function(e){var n=i.perPage*((i.preloadPages||1)+1)-1;return!e[1].isWithin(t.index,n)||u(e)})).length||m(g)}function u(e){var t=e[0];p(e[1].slide,Dr),v(t,"load error",r(l,e)),O(t,"src",L(t,Kr)),O(t,"srcset",L(t,Xr)),T(t,Kr),T(t,Xr)}function l(e,t){var n=e[0],r=e[1];F(r.slide,Dr),"error"!==t.type&&(M(e[2]),x(n,""),f(Hn,n,r),f(Tn)),E&&c()}function c(){h.length&&u(h.shift())}var s=$(t),d=s.on,m=s.off,v=s.bind,f=s.emit,E="sequential"===i.lazyLoad,g=[gn,Ln],h=[];return{mount:function(){i.lazyLoad&&(o(),d(Nn,o))},destroy:r(e,h),check:a}},Pagination:function(n,i,o){function a(){m&&(M(L?t(m.children):m),F(m,v),e(w),m=null),f.destroy()}function u(e){A(">"+e,!0)}function l(e,t){var n,r=w.length,i=Z(t),o=c(),a=-1;i===S(Gn,!1,o)?a=++e%r:i===S(Vn,!1,o)?a=(--e+r)%r:"Home"===i?a=0:"End"===i&&(a=r-1),(n=w[a])&&(I(n.button),A(">"+a),k(t,!0))}function c(){return o.paginationDirection||o.direction}function s(e){return w[y.toPage(e)]}function d(){var e,t,n=s(N(!0)),r=s(N());n&&(F(e=n.button,xr),T(e,tr),O(e,Qn,-1)),r&&(p(t=r.button,xr),O(t,tr,!0),O(t,Qn,"")),g(kn,{list:m,items:w},n,r)}var m,v,f=$(n),E=f.on,g=f.emit,h=f.bind,_=i.Slides,b=i.Elements,y=i.Controller,C=y.hasFocus,N=y.getIndex,A=y.go,S=i.Direction.resolve,L=b.pagination,w=[];return{items:w,mount:function e(){a(),E([An,Nn,Yn],e);var t=o.pagination;L&&x(L,t?"":"none"),t&&(E([En,In,Ln],d),function(){var e,t,i,a,s,d=n.length,f=o.classes,E=o.i18n,g=o.perPage,N=C()?y.getEnd()+1:dn(d/g);for(p(m=L||R("ul",f.pagination,b.track.parentElement),v=Nr+"--"+c()),O(m,$n,"tablist"),O(m,nr,E.select),O(m,or,c()===Kn?"vertical":""),e=0;e<N;e++)t=R("li",null,m),i=R("button",{class:f.page,type:"button"},t),a=_.getIn(e).map(function(e){return e.slide.id}),s=!C()&&g>1?E.pageX:E.slideX,h(i,"click",r(u,e)),o.paginationKeyboard&&h(i,"keydown",r(l,e)),O(t,$n,"presentation"),O(i,$n,"tab"),O(i,Jn,a.join(" ")),O(i,nr,W(s,e+1)),O(i,Qn,-1),w.push({li:t,button:i,page:e})}(),d(),g(Pn,{list:m,items:w},s(n.index)))},destroy:a,getAt:s,update:d}},Sync:function(t,n,i){function o(){var e,n;t.splides.forEach(function(e){e.isParent||(u(t,e.splide),u(e.splide,t))}),d&&((n=(e=$(t)).on)(hn,c),n("sk",s),n([fn,An],l),f.push(e),e.emit(Bn,t.splides))}function a(){f.forEach(function(e){e.destroy()}),e(f)}function u(e,t){var n=$(e);n.on(En,function(e,n,r){t.go(t.is(Yr)?r:e)}),f.push(n)}function l(){O(n.Elements.list,or,i.direction===Kn?"vertical":"")}function c(e){t.go(e.index)}function s(e,t){m(Qr,Z(t))&&(c(e),k(t))}var d=i.isNavigation,v=i.slideFocus,f=[];return{setup:r(n.Media.set,{slideFocus:rn(v)?d:v},!0),mount:o,destroy:a,remount:function(){a(),o()}}},Wheel:function(e,t,n){function r(r){if(r.cancelable){var i=r.deltaY,a=i<0,u=j(r),l=n.wheelMinThreshold||0,c=n.wheelSleep||0;mn(i)>l&&u-o>c&&(e.go(a?"<":">"),o=u),function(r){return!n.releaseWheel||e.state.is(4)||-1!==t.Controller.getAdjacent(r)}(a)&&k(r)}}var i=$(e).bind,o=0;return{mount:function(){n.wheel&&i(t.Elements.track,"wheel",r,Gr)}}},Live:function(e,t,n){function i(e){O(u,lr,e),e?(E(u,c),s.start()):(M(c),s.cancel())}function o(e){l&&O(u,ur,e?"off":"polite")}var a=$(e).on,u=t.Elements.track,l=n.live&&!n.isNavigation,c=R("span",Rr),s=Q(90,r(i,!1));return{mount:function(){l&&(o(!t.Autoplay.isPaused()),O(u,cr,!0),c.textContent="…",a(Un,r(o,!0)),a(jn,r(o,!1)),a([gn,Ln],r(i,!0)))},disable:o,destroy:function(){T(u,[ur,cr,lr]),M(c)}}}}),Jr={type:"slide",role:"region",speed:400,perPage:1,cloneStatus:!0,arrows:!0,pagination:!0,paginationKeyboard:!0,interval:5e3,pauseOnHover:!0,pauseOnFocus:!0,resetProgress:!0,easing:"cubic-bezier(0.25, 1, 0.5, 1)",drag:!0,direction:"ltr",trimSpace:!0,focusableNodes:"a, button, textarea, input, select, iframe",live:!0,classes:Br,i18n:{prev:"Previous slide",next:"Next slide",first:"Go to first slide",last:"Go to last slide",slideX:"Go to slide %s",pageX:"Go to page %s",play:"Start autoplay",pause:"Pause autoplay",carousel:"carousel",slide:"slide",select:"Select a slide to show",slideLabel:"%s of %s"},reducedMotion:{speed:0,rewindSpeed:0,autoplay:"pause"}},ei=function(){function n(e,t){var r,i;this.event=$(),this.Components={},this.state=(r=1,{set:function(e){r=e},is:function(e){return m(s(e),r)}}),this.splides=[],this._o={},this._E={},Y(i=nn(e)?B(document,e):e,i+" is invalid."),this.root=i,t=N({label:L(i,nr)||"",labelledby:L(i,rr)||""},Jr,n.defaults,t||{});try{N(t,JSON.parse(L(i,un)))}catch(n){Y(!1,"Invalid JSON")}this._o=Object.create(N({},t))}var r,i,o=n.prototype;return o.mount=function(e,t){var n=this,r=this.state,i=this.Components;return Y(r.is([1,7]),"Already mounted!"),r.set(1),this._C=i,this._T=t||this._T||(this.is(zr)?J:ee),this._E=e||this._E,y(C({},Zr,this._E,{Transition:this._T}),function(e,t){var r=e(n,i,n._o);i[t]=r,r.setup&&r.setup()}),y(i,function(e){e.mount&&e.mount()}),this.emit(fn),p(this.root,Sr),r.set(3),this.emit(pn),this},o.sync=function(e){return this.splides.push({splide:e}),e.splides.push({splide:this,isParent:!0}),this.state.is(3)&&(this._C.Sync.remount(),e.Components.Sync.remount()),this},o.go=function(e){return this._C.Controller.go(e),this},o.on=function(e,t){return this.event.on(e,t),this},o.off=function(e){return this.event.off(e),this},o.emit=function(e){var n;return(n=this.event).emit.apply(n,[e].concat(t(arguments,1))),this},o.add=function(e,t){return this._C.Slides.add(e,t),this},o.remove=function(e){return this._C.Slides.remove(e),this},o.is=function(e){return this._o.type===e},o.refresh=function(){return this.emit(Nn),this},o.destroy=function(t){void 0===t&&(t=!0);var n=this.event,r=this.state;return r.is(1)?$(this).on(pn,this.destroy.bind(this,t)):(y(this._C,function(e){e.destroy&&e.destroy(t)},!0),n.emit(wn),n.destroy(),t&&e(this.splides),r.set(7)),this},r=n,(i=[{key:"options",get:function(){return this._o},set:function(e){this._C.Media.set(e,!0,!0)}},{key:"length",get:function(){return this._C.Slides.getLength(!0)}},{key:"index",get:function(){return this._C.Controller.getIndex()}}])&&function(e,t){var n,r;for(n=0;n<t.length;n++)(r=t[n]).enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}(r.prototype,i),Object.defineProperty(r,"prototype",{writable:!1}),n}(),(ti=ei).defaults={},ti.STATES={CREATED:1,MOUNTED:2,IDLE:3,MOVING:4,SCROLLING:5,DRAGGING:6,DESTROYED:7},ni=[[fn,"onMounted"],[pn,"onReady"],[En,"onMove"],[gn,"onMoved"],[hn,"onClick"],[_n,"onActive"],[bn,"onInactive"],[yn,"onVisible"],[Cn,"onHidden"],[Nn,"onRefresh"],[An,"onUpdated"],[Tn,"onResize"],[On,"onResized"],[Rn,"onDrag"],[Sn,"onDragging"],[xn,"onDragged"],[In,"onScroll"],[Ln,"onScrolled"],[wn,"onDestroy"],[Dn,"onArrowsMounted"],[Mn,"onArrowsUpdated"],[Pn,"onPaginationMounted"],[kn,"onPaginationUpdated"],[Bn,"onNavigationMounted"],[Un,"onAutoplayPlay"],[Fn,"onAutoplayPlaying"],[jn,"onAutoplayPause"],[Hn,"onLazyLoadLoaded"]],ri=({children:e,className:t,...n})=>Vt().createElement("div",{className:te("splide__track",t),...n},Vt().createElement("ul",{className:"splide__list"},e)),ii=class extends Vt().Component{constructor(){super(...arguments),this.splideRef=Vt().createRef(),this.slides=[]}componentDidMount(){const{options:e,extensions:t,transition:n}=this.props,{current:r}=this.splideRef;r&&(this.splide=new ti(r,e),this.bind(this.splide),this.splide.mount(t,n),this.options=ie({},e||{}),this.slides=this.getSlides())}componentWillUnmount(){this.splide&&(this.splide.destroy(),this.splide=void 0),this.options=void 0,this.slides.length=0}componentDidUpdate(){if(!this.splide)return;const{options:e}=this.props;e&&!re(this.options,e)&&(this.splide.options=e,this.options=ie({},e));const t=this.getSlides();var n,r;n=this.slides,r=t,(n.length!==r.length||n.some((e,t)=>e!==r[t]))&&(this.splide.refresh(),this.slides=t)}sync(e){var t;null==(t=this.splide)||t.sync(e)}go(e){var t;null==(t=this.splide)||t.go(e)}getSlides(){var e;if(this.splide){const t=null==(e=this.splide.Components.Elements)?void 0:e.list.children;return t&&Array.prototype.slice.call(t)||[]}return[]}bind(e){ni.forEach(([t,n])=>{const r=this.props[n];"function"==typeof r&&e.on(t,(...t)=>{r(e,...t)})})}omit(e,t){return t.forEach(t=>{Object.prototype.hasOwnProperty.call(e,t)&&delete e[t]}),e}render(){const{className:e,tag:t="div",hasTrack:n=!0,children:r,...i}=this.props;return Vt().createElement(t,{className:te("splide",e),ref:this.splideRef,...this.omit(i,["options",...ni.map(e=>e[1])])},n?Vt().createElement(ri,null,r):r)}},oi=({children:e,className:t,...n})=>Vt().createElement("li",{className:te("splide__slide",t),...n},e);const Ta=(e,t)=>{t&&t.forEach((n,r)=>{const i=e.replace("{current}",`${r+1}`).replace("{total}",`${t.length}`);n.setAttribute("aria-label",i)})},Oa=({id:e,children:t,"aria-labelledby":n,"aria-label":r,arrowButtonsLabel:i,slideDescription:o="slide",dotsLabel:a})=>{const u=(0,Yt.useRef)(null),{width:l}=ia(100),[c,s]=(0,Yt.useState)({}),d=(0,Yt.useRef)(null),m=(0,Yt.useMemo)(()=>Yt.Children.toArray(t).filter(e=>null!=e).length,[t]);let v=!1,f="";l<768&&m>1?v=!0:l>=768&&l<992&&m>2?(v=!0,f="vrui-grid sm:vrui-grid-cols-2 vrui-gap-x-16"):l>=992&&m>3&&(v=!0,f="vrui-grid sm:vrui-grid-cols-3 vrui-gap-x-16"),v||(1===m?f="":2===m?f="vrui-grid sm:vrui-grid-cols-2 vrui-gap-x-16":3===m&&(f="vrui-grid sm:vrui-grid-cols-3 vrui-gap-x-16"));const p=(0,Yt.useMemo)(()=>l<768?{marginRight:"-16px",marginLeft:"-16px"}:l>=768&&l<992?{marginRight:"-32px",marginLeft:"-32px"}:{marginRight:"0px",marginLeft:"0px"},[l]);(0,Yt.useEffect)(()=>{const{splide:e}=u.current||{};e&&(()=>{const e=u.current?.splide?.index,t=u.current?.splide?.length||0;window.innerWidth<768?s(0===e?{left:"16px",right:"32px"}:e===t-1?{left:"32px",right:"16px"}:{left:"24px",right:"24px"}):window.innerWidth>=768&&window.innerWidth<992?s(0===e?{left:"32px",right:"42px"}:e===t-1?{left:"42px",right:"32px"}:{left:"32px",right:"32px"}):s({left:"0px",right:"0px"})})()},[l]);const E=(0,Yt.useMemo)(()=>{const e=Vt().Children.map(t,(e,t)=>Vt().isValidElement(e)?Vt().createElement(oi,{key:t,"data-id":t},Vt().cloneElement(e)):e);return e},[t]);return Vt().createElement(Vt().Fragment,null,Vt().createElement("div",{className:v?"":"vrui-hidden"},Vt().createElement(ii,{options:{mediaQuery:"min",destroy:!1,pagination:!0,drag:!0,live:!1,focusableNodes:"button, input, a",gap:16,padding:c,paginationKeyboard:!0,classes:{arrows:"splide__arrows",arrow:"splide__arrow",prev:"splide__arrow--prev !vrui-h-40 !vrui-w-40 !vrui-bg-white !vrui-border-solid !vrui-border-2 !vrui-border-blue !vrui-text-blue !vrui-border-rounded-60 hover:!vrui-border-blue-1 !-vrui-left-10 focus:!vrui-outline-blue focus:!vrui-outline focus:!vrui-outline-2 focus:!vrui-outline-offset-3 !vrui-opacity-100 -vrui-mt-10",next:"splide__arrow--next !vrui-h-40 !vrui-w-40 !vrui-bg-white !vrui-border-solid !vrui-border-2 !vrui-border-blue !vrui-text-blue !vrui-border-rounded-60 hover:!vrui-border-blue-1 !-vrui-right-10 focus:!vrui-outline-blue focus:!vrui-outline focus:!vrui-outline-2 focus:!vrui-outline-offset-3 !vrui-opacity-100 -vrui-mt-10",pagination:"splide__pagination",page:"splide__pagination__page !vrui-bg-white"},breakpoints:{320:{perPage:1,perMove:1,arrows:!1},768:{perPage:2,perMove:2,arrows:!1},992:{perPage:3,perMove:3,arrows:!0}},i18n:{prev:i&&i.previous?i.previous:"Previous slide",next:i&&i.next?i.next:"Next slide",slide:o}},ref:u,hasTrack:!1,"aria-label":r,"aria-labelledby":n,id:`${e}-parent`,onMove:()=>{(()=>{let t={};const n=u.current?.splide?.index,r=u.current?.splide?.length||0,i=(n,r)=>{const i=document.querySelector(`#${e}-parent .splide__track`);i&&(t={paddingLeft:n,paddingRight:r},Object.assign(i.style,t))};window.innerWidth<768?0===n?i("16px","32px"):n===r-1?i("32px","16px"):i("24px","24px"):window.innerWidth>=768&&window.innerWidth<992?0===n?i("32px","42px"):i(n===r-1?"42px":"32px","32px"):i("0px","0px")})()},onPaginationMounted:()=>{(()=>{if(d&&d.current){const e=d.current.querySelectorAll(".splide__pagination__page");a&&e?.length>0&&Ta(a,e),d.current.removeAttribute("aria-label")}})()}},Vt().createElement(ri,{id:e,className:"-vrui-mt-10 vrui-pt-10 vrui-pb-28",style:{...p}},E),Vt().createElement("div",{className:"splide__arrows"}),Vt().createElement("ul",{id:`${e}-pagination`,ref:d,className:"splide__pagination !-vrui-bottom-16 -vrui-translate-y-16"}))),Vt().createElement("div",{className:[v?"vrui-hidden":"",f].join(" ").trim()},t))},Ra=(0,Yt.createContext)({trackMarginStyle:{marginLeft:"0px",marginRight:"0px"},trackPaddingStyle:{paddingLeft:"0px",paddingRight:"0px"},splideRef:Vt().createRef(),focusRadioOnPaginationClick:!1}),Sa={first:{paddingLeft:"0px",paddingRight:"0px",marginLeft:"0px",marginRight:"0px"},middle:{paddingLeft:"0px",paddingRight:"0px",marginLeft:"0px",marginRight:"0px"},last:{paddingLeft:"0px",paddingRight:"0px",marginLeft:"0px",marginRight:"0px"}},xa=e=>e||Sa,Ia=({children:e,config:t,id:n,"aria-label":r,"aria-labelledby":i,slideRole:o,slideLabel:a="%s of %s",paginationLabel:u="",paginationRole:l,paginationButtonLabel:c="",paginationButtonCurrent:s,arrowButtonsLabel:d,focusRadioOnPaginationClick:m=!1,omitEnd:v=!1,live:f=!1})=>{const{mobile:p,tablet:E,desktop:g}=t,h=(0,Yt.useRef)(null),{width:_}=ia(),[b,y]=(0,Yt.useState)({}),[C,N]=(0,Yt.useState)({marginLeft:"0px",marginRight:"0px"}),[A,T]=(0,Yt.useState)({paddingLeft:"0px",paddingRight:"0px"}),O=p?.isActive??!0,R=E?.isActive??!0,S=g?.isActive??!0,x=xa(p?.trackSpacing),I=xa(E?.trackSpacing),L=xa(g?.trackSpacing);(0,Yt.useEffect)(()=>{const{splide:e}=h.current||{},t=h.current?.splide?.index,n=h.current?.splide?.length||0;e&&(_<768?0===t?(y({left:x.first.paddingLeft,right:x.first.paddingRight}),N({marginLeft:x.first.marginLeft,marginRight:x.first.marginRight})):t===n-1?(y({left:x.last.paddingLeft,right:x.last.paddingRight}),N({marginLeft:x.last.marginLeft,marginRight:x.last.marginRight})):(y({left:x.middle.paddingLeft,right:x.middle.paddingRight}),N({marginLeft:x.middle.marginLeft,marginRight:x.middle.marginRight})):_>=768&&_<992?0===t?(y({left:I.first.paddingLeft,right:I.first.paddingRight}),N({marginLeft:I.first.marginLeft,marginRight:I.first.marginRight})):t>=n-2?(y({left:I.last.paddingLeft,right:I.last.paddingRight}),N({marginLeft:I.last.marginLeft,marginRight:I.last.marginRight})):(y({left:I.middle.paddingLeft,right:I.middle.paddingRight}),N({marginLeft:I.middle.marginLeft,marginRight:I.middle.marginRight})):(y({left:L.middle.paddingLeft,right:L.middle.paddingRight}),N({marginLeft:L.middle.marginLeft,marginRight:L.middle.marginRight})))},[_]),(0,Yt.useEffect)(()=>{const e=h.current?.splide;return e&&(D(),e.on("move",()=>{D(),setTimeout(()=>{M()},1)})),()=>{e&&e.off("move")}},[_]),(0,Yt.useEffect)(()=>{const e=h.current?.splide;if(e&&void 0!==o&&""!==o&&(()=>{const t=e.Components?.Elements?.slides;t&&t.forEach(e=>{e.setAttribute("role",o)})})(),e){const t=()=>{if(null!=l&&""!==l){const t=e.Components?.Elements?.pagination;t?.setAttribute("role",l)}},n=()=>{t(),M()};e.on("pagination:mounted",n)}return()=>{e&&e.off("pagination:mounted")}},[_,h]);const w=e=>{if(h.current){h.current.go(e);const t=h.current?.splide?.Components.Elements.slides,n=t[e];if(n){const e=n.querySelector('input[type="radio"]');setTimeout(()=>{e?.focus()},500)}}};(0,Yt.useEffect)(()=>{if(!m)return;const e=h.current?.splide?.Components.Elements.pagination;if(e){const t=e.querySelectorAll("li button"),n=e=>()=>w(e);return t.forEach((e,t)=>{e.addEventListener("click",n(t))}),()=>{t.forEach((e,t)=>{e.removeEventListener("click",n(t))})}}},[h,w,m]);const D=()=>{const e=h.current?.splide?.index,t=h.current?.splide?.length||0;_<768&&O?0===e?(T({paddingLeft:x.first.paddingLeft,paddingRight:x.first.paddingRight}),N({marginLeft:x.first.marginLeft,marginRight:x.first.marginRight})):e===t-1?(T({paddingLeft:x.last.paddingLeft,paddingRight:x.last.paddingRight}),N({marginLeft:x.last.marginLeft,marginRight:x.last.marginRight})):(T({paddingLeft:x.middle.paddingLeft,paddingRight:x.middle.paddingRight}),N({marginLeft:x.middle.marginLeft,marginRight:x.middle.marginRight})):_>=768&&_<992&&R?0===e?(T({paddingLeft:I.first.paddingLeft,paddingRight:I.first.paddingRight}),N({marginLeft:I.first.marginLeft,marginRight:I.first.marginRight})):e>=t-2?(T({paddingLeft:I.last.paddingLeft,paddingRight:I.last.paddingRight}),N({marginLeft:I.last.marginLeft,marginRight:I.last.marginRight})):(T({paddingLeft:I.middle.paddingLeft,paddingRight:I.middle.paddingRight}),N({marginLeft:I.middle.marginLeft,marginRight:I.middle.marginRight})):_>=992&&S&&(T({paddingLeft:L.middle.paddingLeft,paddingRight:L.middle.paddingRight}),N({marginLeft:L.middle.marginLeft,marginRight:L.middle.marginRight}))},M=()=>{const e=h.current?.splide;if(e){const t=e.Components?.Elements?.pagination,n=t?.querySelectorAll(".splide__pagination__page");n&&n.forEach((e,t)=>{if(e.removeAttribute("role"),e.removeAttribute("tabindex"),e.removeAttribute("aria-controls"),e.removeAttribute("aria-selected"),e.classList.contains("is-active")&&s&&c){const r=c.replace("{current}",`${t+1}`).replace("{total}",`${n.length}`);e.setAttribute("aria-label",`${r} ${s}`)}else c&&s&&e.setAttribute("aria-label",c.replace("{current}",`${t+1}`).replace("{total}",`${n.length}`))})}};return Vt().createElement(Ra.Provider,{value:{trackMarginStyle:C,trackPaddingStyle:A,splideRef:h,focusRadioOnPaginationClick:m}},Vt().createElement(ii,{id:`${n}-parent`,ref:h,className:"vrui-h-full","aria-label":r,"aria-labelledby":i,hasTrack:!1,options:{i18n:{slideLabel:a,select:u,slideX:c||"Go to slide %s",pageX:c||"Go to page %s",prev:d&&d.previous?d.previous:"Previous slide",next:d&&d.next?d.next:"Next slide"},padding:b,mediaQuery:"min",destroy:!1,focusableNodes:"button, input",paginationKeyboard:!1,omitEnd:v,live:f,classes:{arrows:"splide__arrows",arrow:"splide__arrow",prev:"splide__arrow--prev !vrui-h-40 !vrui-w-40 !vrui-bg-white !vrui-border-solid !vrui-border-2 !vrui-border-blue !vrui-text-blue !vrui-border-rounded-60 hover:!vrui-border-blue-1 !-vrui-left-10 focus:!vrui-outline-blue focus:!vrui-outline focus:!vrui-outline-2 focus:!vrui-outline-offset-3 !vrui-opacity-100",next:"splide__arrow--next !vrui-h-40 !vrui-w-40 !vrui-bg-white !vrui-border-solid !vrui-border-2 !vrui-border-blue !vrui-text-blue !vrui-border-rounded-60 hover:!vrui-border-blue-1 !-vrui-right-10 focus:!vrui-outline-blue focus:!vrui-outline focus:!vrui-outline-2 focus:!vrui-outline-offset-3 !vrui-opacity-100",pagination:"splide__pagination",page:"splide__pagination__page !vrui-bg-white !vrui-block"},breakpoints:{320:{perPage:p?.perPage,perMove:p?.perMove,arrows:p?.arrows,gap:p?.gap,destroy:!O,focus:p?.focus},768:{perPage:E?.perPage,perMove:E?.perMove,arrows:E?.arrows,gap:E?.gap,destroy:!R,focus:E?.focus},992:{perPage:g?.perPage,perMove:g?.perMove,arrows:g?.arrows,gap:g?.gap,destroy:!S,focus:g?.focus}}}},e))},La=()=>Vt().createElement("div",{className:"splide__arrows"}),wa=({children:e,className:t})=>{const{trackMarginStyle:n,trackPaddingStyle:r,splideRef:i,focusRadioOnPaginationClick:o}=(0,Yt.useContext)(Ra),a={...n,...r},u=(0,Yt.useCallback)(e=>{const t=(e,t)=>{const n=e?.querySelector('input[type="radio"]:not([disabled])');setTimeout(()=>{i.current?.go(t),n?.click(),n?.focus()},100)},n=Array.from(e.currentTarget.parentElement.children),r=n.indexOf(e.currentTarget);if("ArrowLeft"===e.key||"ArrowUp"===e.key){const e=(r-1+n.length)%n.length;t(n[e],e)}else if("ArrowDown"===e.key||"ArrowRight"===e.key){const e=(r+1)%n.length;t(n[e],e)}},[i]),l=(e,t)=>({...e,currentTarget:t,target:t,nativeEvent:e,isDefaultPrevented:()=>e.defaultPrevented,isPropagationStopped:()=>(e.stopPropagation(),!0),persist:()=>{},view:{...e.view||window,styleMedia:"styleMedia"in(e.view||window)?(e.view||window).styleMedia:{matchMedium:()=>!1}},locale:navigator.language,key:e.key});return(0,Yt.useEffect)(()=>{if(o)return document.querySelectorAll(".splide__slide").forEach(e=>{const t=e;t.addEventListener("keydown",e=>{u(l(e,t))})}),()=>{document.querySelectorAll(".splide__slide").forEach(e=>{const t=e;t.removeEventListener("keydown",e=>{u(l(e,t))})})}},[i,u,o]),Vt().createElement("div",{className:["splide__track splide__track-custom",t].join(" ").trim(),style:a},Vt().createElement("div",{className:"splide__list"},e))},Da=({children:e})=>Vt().createElement("div",{className:"splide__slide"},e),Ma=({className:e})=>Vt().createElement("ul",{className:["splide__pagination",e].join(" ").trim()}),Pa={default:"group-has-[:focus-visible]/inputcheckbox:vrui-outline-blue group-has-[:focus-visible]/inputcheckbox:vrui-outline group-has-[:focus-visible]/inputcheckbox:vrui-outline-2 group-has-[:focus-visible]/inputcheckbox:vrui-outline-offset-3",boxedInMobile:"group-has-[:focus-visible]/inputcheckbox:sm:vrui-outline-blue group-has-[:focus-visible]/inputcheckbox:sm:vrui-outline group-has-[:focus-visible]/inputcheckbox:sm:vrui-outline-2 group-has-[:focus-visible]/inputcheckbox:sm:vrui-outline-offset-3",alignMiddle:"group-has-[:focus-visible]/inputcheckbox:vrui-outline-blue group-has-[:focus-visible]/inputcheckbox:vrui-outline group-has-[:focus-visible]/inputcheckbox:vrui-outline-2 group-has-[:focus-visible]/inputcheckbox:vrui-outline-offset-3"},ka=(0,Yt.forwardRef)(function({id:e,name:t,value:n,hasError:r=!1,variant:i,...o},a){return Vt().createElement("div",{className:"vrui-relative vrui-group/inputcheckbox"},Vt().createElement("input",{type:"checkbox",id:e,name:t,value:n,className:"vrui-absolute vrui-size-full vrui-opacity-0 enabled:vrui-cursor-pointer disabled:vrui-cursor-default vrui-z-10",...o,ref:a}),Vt().createElement("div",null,Vt().createElement("div",{className:["vrui-size-24 vrui-rounded-2 vrui-border group-has-[:disabled]/inputcheckbox:vrui-bg-gray-5 group-has-[:disabled]/inputcheckbox:vrui-border-gray-10 group-has-[:checked]/inputcheckbox:vrui-bg-blue group-has-[:checked]/inputcheckbox:vrui-border-blue group-has-[:checked]/label:vrui-font-bold group-has-[:disabled]/inputcheckbox:vrui-opacity-40",r?"vrui-border-red":"vrui-border-gray-2",Pa[i]].join(" ").trim()}),Vt().createElement(ma,{className:"group-has-[:checked]/inputcheckbox:vrui-block vrui-absolute vrui-hidden vrui-text-white group-has-[:checked:disabled]/inputcheckbox:vrui-text-gray-7 vrui-text-12 vrui-transform vrui-top-1/2 vrui-left-1/2 -vrui-translate-x-1/2 -vrui-translate-y-1/2 group-has-[:disabled]/inputcheckbox:vrui-opacity-40",iconClass:"vi_vrui",iconName:"vi_checked"})))}),Ba=({elementType:e,children:t,className:n,...r})=>{const i=e||"span";return Vt().createElement(i,{className:n,...r},t)},Ua=(ai=n(442)).default||ai,Fa=({id:e,iconName:t="vi_vrui",iconClass:n="vi_warning_c",errorMessage:r,errorMessageClassName:i})=>Vt().createElement("div",{className:["vrui-flex vrui-items-center vrui-mt-10",i].join(" ").trim()},Vt().createElement(ma,{iconClass:n,iconName:t,className:"vrui-text-16 vrui-text-red-2 vrui-mr-10"}),Vt().createElement(Ba,{id:e,elementType:"div",className:"vrui-box-border vrui-text-red-2 vrui-text-12 vrui-leading-14"},Ua(r))),ja=(0,Yt.forwardRef)(function({id:e,name:t,value:n,children:r,variant:i,hasError:o=!1,"aria-describedby":a,errorMessage:u,boldLabelOnCheck:l,labelClassNames:c,...s},d){const m={default:{wrapper:"vrui-group/label vrui-relative vrui-inline-flex vrui-w-full vrui-items-start vrui-content-start vrui-border-0",label:"vrui-pl-12 vrui-text-14 vrui-leading-19"},boxedInMobile:{wrapper:o?"vrui-group/label vrui-relative vrui-inline-flex vrui-w-full vrui-border-2 vrui-border-red vrui-items-center vrui-content-center vrui-rounded-6 vrui-border-1 sm:vrui-border-0 has-[:checked]:sm:vrui-border-0 has-[:checked]:vrui-border-2 has-[:checked]:vrui-border-blue vrui-p-15 sm:vrui-p-0":"vrui-group/label vrui-relative vrui-inline-flex vrui-w-full vrui-items-center vrui-content-center vrui-rounded-6 vrui-border-1 sm:vrui-border-0 has-[:checked]:sm:vrui-border-0 has-[:checked]:vrui-border-2 has-[:checked]:vrui-border-blue vrui-p-15 sm:vrui-p-0 vrui-border-gray-3 has-[input:focus-visible]:vrui-outline has-[input:focus-visible]:vrui-outline-2 has-[input:focus-visible]:vrui-outline-offset-3 has-[input:focus-visible]:vrui-outline-blue has-[input:focus-visible]:sm:vrui-outline-0",label:"vrui-pl-12 vrui-text-14 vrui-leading-19"},alignMiddle:{wrapper:"vrui-group/label vrui-relative vrui-inline-flex vrui-w-full vrui-items-start vrui-content-start vrui-border-0",label:"vrui-pl-12 vrui-text-14 vrui-leading-19 vrui-self-center"}},v=u&&o?`error-${e}`:"",f=(0,Yt.useMemo)(()=>a?[a||"",v].join(" ").trim():v,[a,v]);return Vt().createElement(Vt().Fragment,null,Vt().createElement("div",{className:[m[i].wrapper].join(" ").trim()},Vt().createElement(ka,{id:e,name:t,value:n,ref:d,hasError:o,"aria-describedby":f||"",variant:i,...s}),Vt().createElement("label",{htmlFor:e,className:["vrui-cursor-pointer group-has-[:disabled]/label:vrui-cursor-default",m[i].label,l?"group-has-[:checked]/label:vrui-font-bold":"",c].join(" ").trim()},r)),o&&Vt().createElement(Fa,{id:v,iconClass:"vi_warning_c",iconName:"vi_vrui",errorMessage:u||""}))}),Ha=ja,Ya=(0,Yt.createContext)(null),za=({className:e,children:t,...n})=>{const[r,i]=(0,Yt.useState)([]),[o,a]=(0,Yt.useState)([]),[u,l]=(0,Yt.useState)(""),[c,s]=(0,Yt.useState)(""),[d,m]=(0,Yt.useState)(!1),[v,f]=(0,Yt.useState)(null),[p,E]=(0,Yt.useState)(""),[g,h]=(0,Yt.useState)(!1),[_,b]=(0,Yt.useState)(!1),y=(0,Yt.useRef)(null);return Vt().createElement(Ya.Provider,{value:{options:r,initializeOptions:e=>{i(t=>[...t,e])},filteredOptions:o,updateFilteredOptions:e=>{a(e)},isOpen:g,setIsOpen:h,selectedOptionValue:u,setSelectedOptionValue:l,selectedOptionDisplayName:c,setSelectedOptionDisplayName:s,isNavigating:d,setIsNavigating:m,navigatorIndex:v,setNavigatorIndex:f,inputValue:p,setInputValue:E,inputRef:y,isNoResultOpen:_,setIsNoResultOpen:b}},Vt().createElement("div",{className:e,...n},t))},Va=()=>{const e=(0,Yt.useContext)(Ya);if(null===e)throw new Error("Select components must be wrapped in <ComboBox />");return e},Ga="vrui-bg-white vrui-px-10 vrui-py-8 vrui-text-14 vrui-leading-18 vrui-rounded-b-4 vrui-shadow-2sm vrui-w-full vrui-z-[1001] vrui-overflow-y-auto vrui-scrollbar-2",qa=({id:e,children:t,dropDownHeight:n,dropDownLabel:r,noResultText:i})=>{const{isOpen:o,isNoResultOpen:a}=Va(),{width:u}=ia();return Vt().createElement("div",{className:"vrui-relative vrui-w-full vrui-z-[1001]"},i?Vt().createElement(Vt().Fragment,null,Vt().createElement("div",{className:"vrui-bg-white vrui-px-20 vrui-py-10 vrui-text-12 vrui-leading-18 vrui-rounded-b-4 vrui-shadow-2sm vrui-w-full vrui-z-[1001] vrui-overflow-y-auto "+(a?"vrui-absolute":"vrui-hidden")},Vt().createElement("span",null,i)),Vt().createElement("ul",{id:e,role:"listbox",className:[Ga,o&&!a?"vrui-absolute":"vrui-hidden"].join(" ").trim(),style:n?ua(u,n):void 0,"aria-label":r||""},t)):Vt().createElement("ul",{id:e,role:"listbox",className:[Ga,o?"vrui-absolute":"vrui-hidden"].join(" ").trim(),style:n?ua(u,n):void 0,"aria-label":r||""},t))},Wa=(0,Yt.forwardRef)(function({children:e,isError:t,dropDownHeight:n,id:r,iconClass:i="vi_warning_c",iconName:o="vi_vrui",errorMessage:a,"aria-describedby":u,dropDownLabel:l,onKeyDownEnter:c,onChange:s,onBlur:d,noResultText:m,showClearButton:v,showSearchIcon:f,clearButtonAriaLabel:p,filterMethod:E="includes",noResultOnEmptyClick:g,noDropdownIcon:h,..._},b){const{isOpen:y,setIsOpen:C,options:N,filteredOptions:A,updateFilteredOptions:T,inputValue:O,setInputValue:R,navigatorIndex:S,isNavigating:x,setIsNavigating:I,setNavigatorIndex:L,inputRef:w,setIsNoResultOpen:D}=Va(),[M,P]=(0,Yt.useState)(""),k=`dropdown-${r}`,B=void 0!==a?`error-${r}`:"",U=[B||"",u||""].join(" ").trim(),F=(0,Yt.useRef)(null),j=(0,Yt.useRef)([]),H=e=>{const t=[...N],n="startsWith"===E?String.prototype.startsWith:String.prototype.includes;return t.filter(t=>{if(t){const r=t.displayName.toLowerCase(),i=e.toLowerCase();return n.call(r,i)}return!1})},Y=e=>{const t=document.getElementById(`${e}`);t&&(t.scrollIntoView({behavior:"smooth",block:"nearest"}),P(e))},z=(e,t)=>{if(t.length>0){let n;if("ArrowDown"===e.key||"ArrowUp"===e.key)e.preventDefault(),C(!0),I(!0),"ArrowDown"===e.key?(n=null===S?0:S+1,L(n>t.length-1?t.length-1:n),Y(t[n>t.length-1?t.length-1:n].id)):"ArrowUp"===e.key&&(n=null===S?0:S-1,L(n<0?0:n),Y(t[n<0?0:n].id));else if("Enter"===e.key){if(null!==S&&-1!==S){if(w.current)if(A.length>0&&""!==O?.trim()){w.current.value=t[S].displayName,w.current.setAttribute("data-value",t[S].value),R(t[S].displayName);const n=H(A[S].displayName);T(n),C(!1),L(null),c?.(e)}else if(""===O?.trim()){w.current.value=t[S].displayName,w.current.setAttribute("data-value",t[S].value),R(t[S].displayName);const n=H(t[S].displayName);T(n),C(!1),L(null),c?.(e)}C(!1),I(!1),L(null)}}else"Escape"===e.key&&(C(!1),I(!1),L(null),m&&D(!1))}else 0===t.length&&(C(!1),m&&("Enter"===e.key&&(R(""),w.current&&(w.current.value="")),D(!1)))},V=()=>{q(!0);const e=A.length>0&&""!==O?.trim();g&&""===O?.trim()?C(!1):e?C(!0):A.length<1&&""!==O?.trim()?(C(!1),m&&D(!0)):C(!0)},[G,q]=(0,Yt.useState)(!1);return(0,Yt.useEffect)(()=>{const e=e=>{if(m&&F.current&&!F.current.contains(e.target)){const e=j.current.length>0,t=j.current.some(e=>e.displayName.toLowerCase()===(O?.toLowerCase()||""));e&&t?j.current.length<1&&(R(""),w.current&&(w.current.value="")):(R(""),w.current&&(w.current.value=""))}};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[O,m]),(0,Yt.useEffect)(()=>{j.current=A},[A]),Vt().createElement(Vt().Fragment,null,Vt().createElement("div",{className:"vrui-relative"},Vt().createElement("input",{id:r,role:"combobox","aria-autocomplete":"list","aria-expanded":y,"aria-controls":k,"aria-activedescendant":x&&y?M:void 0,"aria-describedby":U,type:"text",autoComplete:"off",className:["vrui-font-poppins-Regular vrui-font-normal vrui-text-darkblue vrui-placeholder-text-gray-2  focus:vrui-text-darkblue vrui-text-14 vrui-leading-19 disabled:hover:vrui-bg-gray-5 hover:vrui-bg-gray-6 vrui-rounded-t vrui-border-solid vrui-box-border disabled:vrui-border-gray-3 focus:vrui-border-blue focus:vrui-outline-1 focus:vrui-outline-blue focus:vrui-outline-offset-2 vrui-h-44 vrui-w-full vrui-py-5 vrui-pl-10 vrui-bg-gray-5 placeholder-shown:vrui-truncate",v?"vrui-pr-64":"vrui-pr-36",t?"invalid:vrui-border-red-2 vrui-border-red-2 vrui-border-b-2":"vrui-border-b vrui-border-darkblue focus:vrui-border-b-2"].join(" ").trim(),onBlur:e=>{d&&d(e),C(!1),m&&D(!1),""===O?.trim()&&q(!1)},onFocus:()=>{""===O?.trim()?(C(!1),m&&D(!1)):V()},onChange:e=>{const t=e.target.value;if(s&&s(e),t&&t.trim().length>0){const e=H(t);T(e),m&&0===e.length?D(!0):e.length>0?C(!0):(C(!1),m&&D(!0))}else T([]),m&&(D(!1),C(!1));R(e=>t??e),L(null)},onKeyDown:e=>{A.length>0&&""!==O?.trim()?z(e,A):A.length<1&&""!==O?.trim()?z(e,[]):z(e,N)},onClick:V,ref:((...e)=>t=>{for(const n of e)"function"==typeof n?n(t):null!==n&&(n.current=t)})(w,b,F),value:O,..._}),v&&G&&Vt().createElement("button",{onClick:()=>{R(""),w.current&&w.current.focus(),C(!1),m&&D(!1)},className:["vrui-text-12 vrui-ml-auto vrui-top-1/2 vrui-transform -vrui-translate-y-1/2 vrui-transition-transform vrui-right-30 vrui-px-5 vrui-mr-5 vrui-absolute","vrui-text-gray-9 focus:vrui-border-blue focus:vrui-outline-1 focus:vrui-outline-blue focus:vrui-outline-offset-2"].join(" ").trim(),"aria-label":p},Vt().createElement(ma,{className:"vrui-text-12 vrui-text-darkblue",iconClass:"vi_vrui",iconName:"vi_remove_cf"})),f?Vt().createElement(ma,{className:["vrui-text-12 vrui-ml-auto vrui-top-1/2 vrui-transform -vrui-translate-y-1/2 vrui-transition-transform vrui-right-15 vrui-absolute vrui-text-gray-9"].join(" ").trim(),iconClass:"vi_vrui",iconName:"vi_search"}):h?null:Vt().createElement(ma,{className:["vrui-text-12 vrui-ml-auto vrui-top-1/2 vrui-transform -vrui-translate-y-1/2 vrui-transition-transform vrui-right-15 vrui-absolute",t?"vrui-text-red":"vrui-text-blue"].join(" ").trim(),iconClass:"vi_vrui",iconName:y?"vi_arrow_dropdown_up_f":"vi_arrow_dropdown_f"}),Vt().createElement(qa,{id:k,dropDownHeight:n||void 0,dropDownLabel:l,noResultText:m},e)),t&&a&&Vt().createElement(Fa,{id:B,iconClass:i,iconName:o,errorMessage:a||""}))}),Ka=Wa,Xa=({displayName:e,value:t,id:n,className:r,onMouseDown:i,...o})=>{const{options:a,initializeOptions:u,filteredOptions:l,updateFilteredOptions:c,inputValue:s,setInputValue:d,isNavigating:m,navigatorIndex:v,setNavigatorIndex:f,inputRef:p,setIsOpen:E}=Va(),[g,h]=(0,Yt.useState)(!0);(0,Yt.useEffect)(()=>{a.findIndex(e=>e.value===t)<0&&u({value:t,id:n,displayName:e})},[]),(0,Yt.useEffect)(()=>{if(""===s?.trim())h(!0);else if(l.length>0){const e=l.some(e=>e.id===n);h(e)}else h(!1)},[l,s]);const _=l.length>0&&""!==s?.trim(),b=null!==v?_?l[v]?.id:a[v]?.id:null;return Vt().createElement("li",{id:n,value:t,role:"option",className:["vrui-px-8 vrui-py-6 hover:vrui-bg-gray-5 hover:vrui-border-l-2 hover:vrui-border-blue hover:vrui-pl-6 vrui-cursor-pointer",r,g?"vrui-block":"vrui-hidden",g&&m&&null!==v&&b===n?"vrui-bg-gray-5 vrui-border-l-2 vrui-border-blue vrui-pl-[6px]":""].join(" ").trim(),onMouseDown:n=>((e,t,n)=>{if(p.current&&p.current.value!==n){p.current.value=n,p.current.setAttribute("data-value",t);const r=[...a].filter(e=>e&&e.displayName.toLowerCase().includes(n.toLowerCase()));c(r),d(n),i?.(e)}E(!1),f(null)})(n,t,e),...o},e)},$a=({direction:e="horizontal",width:t=2,className:n,style:r,...i})=>Vt().createElement("div",{className:["vrui-bg-gray-1","horizontal"===e?"vrui-w-full":"vrui-flex",n].join(" ").trim(),style:{..."horizontal"===e?{height:t}:{width:t},...r||{}},...i}),Qa=({children:e,className:t,...n})=>Vt().createElement("div",{className:["vrui-hidden group-has-[:checked]:vrui-block",t].join(" ").trim(),...n},e),Za=({children:e,...t})=>Vt().createElement("li",{...t},e),Ja={simple:{divClass:"vrui-mb-32 sm:vrui-mb-0 vrui-text-center sm:vrui-text-left",navClass:"vrui-mb-16 sm:vrui-mb-8 vrui-text-12 vrui-leading-14"},default:{divClass:"vrui-text-center sm:vrui-text-left",navClass:"vrui-text-12 vrui-leading-14"}},eu=({ariaLabel:e,children:t,copyRight:n,variant:r})=>Vt().createElement("div",{className:Ja[r].divClass},Vt().createElement("nav",{className:Ja[r].navClass,"aria-label":e},Vt().createElement("ul",{className:"sm:-vrui-mx-8 sm:vrui-inline-flex"},Vt().Children.map(t,(e,t)=>Vt().createElement(Za,{className:"vrui-mb-5 last-of-type:vrui-mb-0 sm:vrui-mb-0 sm:after:vrui-inline-block sm:after:vrui-align-middle sm:after:vrui-h-12 sm:after:vrui-w-px sm:after:vrui-bg-gray-3 sm:after:last-of-type:vrui-w-0",key:t},e)))),n&&Vt().createElement(Ba,{elementType:"div",className:"vrui-text-gray-4 vrui-text-12 vrui-leading-14"},n)),tu=(0,Yt.createContext)(void 0),nu=({children:e,hasError:t,errorMessage:n,className:r})=>{const i=(0,Yt.useId)();return Vt().createElement(tu.Provider,{value:{formGroupHasError:t,formGroupErrorMessage:n,inputErrorId:i}},Vt().createElement("div",{className:r},e),t&&void 0!==n&&""!==n&&Vt().createElement(Fa,{id:i,iconClass:"vi_warning_c",iconName:"vi_vrui",errorMessage:n||""}))},ru=({children:e,className:t,...n})=>Vt().createElement("div",{className:["vrui-px-16 sm:vrui-px-30 md:vrui-px-16 lg:vrui-container",t].join(" ").trim(),...n},e),iu=({elementType:e,children:t,className:n,...r})=>{if(e&&"div"!==(i=e)&&"footer"!==i)throw new Error(`Invalid elementType: ${e}. Must be "div" or "footer".`);var i;const o=e||"footer";return Vt().createElement(o,{...r},Vt().createElement(ru,null,Vt().createElement("div",{className:n},t)))},ou=({elementType:e,leftButton:t,heading:n,rightButton:r,centerMobile:i})=>{if(e&&"div"!==(o=e)&&"header"!==o)throw new Error(`Invalid elementType: ${e}. Must be "div" or "header".`);var o;const a=e||"header",u=(0,Yt.useMemo)(()=>i?"vrui-text-center max-sm:vrui-absolute max-sm:vrui-left-1/2 max-sm:vrui-top-1/2 max-sm:vrui-transform max-sm:-vrui-translate-x-1/2 max-sm:-vrui-translate-y-1/2":"vrui-ps-16 sm:vrui-ps-0",[i]);return Vt().createElement(a,{className:"vrui-bg-red"},Vt().createElement(ru,null,Vt().createElement("div",{className:"vrui-h-[54px] sm:vrui-h-[75px] vrui-flex vrui-flex-row vrui-items-center vrui-text-white vrui-relative vrui-py-5"},Vt().createElement("div",{className:"sm:vrui-basis-1/3"},t),Vt().createElement("div",{className:["sm:vrui-basis-1/3 sm:vrui-text-center",u].join(" ").trim()},n),Vt().createElement("div",{className:"sm:vrui-basis-1/3 vrui-hidden sm:vrui-block vrui-text-right"},r))))},au={active:"vrui-group active",complete:"vrui-group complete",inactive:"vrui-text-gray-2 vrui-group inactive"},uu=({children:e,className:t,status:n,...r})=>{const i=(0,Yt.useMemo)(()=>[au[n],t].join(" ").trim(),[n]);return Vt().createElement("div",{className:i,...r},e)},lu={xs:"vrui-text-18 vrui-leading-20",sm:"vrui-text-20 vrui-leading-22 md:vrui-text-22 md:vrui-leading-28",md:"vrui-text-22 vrui-leading-24 sm:vrui-text-24 sm:vrui-leading-26 md:vrui-text-26 md:vrui-leading-[31px]",lg:"vrui-text-26 vrui-leading-28 -vrui-tracking-0.3 sm:vrui-text-30 sm:vrui-leading-36 sm:-vrui-tracking-0.6 md:vrui-text-32 md:vrui-leading-38",xl:"vrui-text-30 vrui-leading-32 -vrui-tracking-0.4 sm:vrui-text-34 sm:vrui-leading-40 sm:-vrui-tracking-0.6 md:vrui-text-40 md:vrui-leading-46",default:""},cu=({level:e,variant:t,children:n,className:r,...i})=>Vt().createElement(Vt().Fragment,null,Vt().createElement(e,{className:[lu[t],r,"vrui-font-poppins-Semibold"].join(" "),...i},n)),su=({level:e,screenReaderText:t,subtitle:n,title:r})=>Vt().createElement(cu,{level:e,variant:"sm"},Vt().createElement(Ba,{elementType:"span",className:"vrui-flex vrui-flex-col"},Vt().createElement("span",{className:"vrui-text-14 vrui-leading-18 vrui-mb-4 group-[.inactive]:vrui-text-gray-2 group-[.active]:vrui-text-red group-[.complete]:vrui-text-red"},n," "),r,Vt().createElement(ha,null," ",t))),du=(0,Yt.createContext)(null),mu={warning:"vrui-text-yellow-1",error:"vrui-text-red-2",information:"vrui-text-blue",default:""},vu={16:"vrui-text-16",32:"vrui-text-32",64:"vrui-text-64"},fu=({severity:e,iconSize:t=32,iconName:n="vi_warning_c",children:r,...i})=>Vt().createElement(du.Provider,{value:e},Vt().createElement("div",{...i},Vt().createElement(ma,{className:[mu[e],vu[t],"vrui-inline-block vrui-mb-30 sm:vrui-mb-32"].join(" ").trim(),iconClass:"vi_vrui",iconName:n}),r)),pu=({children:e,level:t="h2"})=>{const n=(0,Yt.useContext)(du);return Vt().createElement(cu,{variant:"default",level:t,className:"vrui-text-26 vrui-leading-28 sm:vrui-text-30 sm:vrui-leading-36 md:vrui-text-[32px] md:vrui-leading-[38px] vrui-mb-15 sm:vrui-mb-16"},Vt().createElement(ha,{className:"vrui-capitalize"},n,": "),e)},Eu=({children:e})=>Vt().createElement(Vt().Fragment,null,e);ui="data-focus-lock",li="data-focus-lock-disabled",ci="undefined"!=typeof window?Yt.useLayoutEffect:Yt.useEffect,si=new WeakMap,di={width:"1px",height:"0px",padding:0,overflow:"hidden",position:"fixed",top:"1px",left:"1px"},mi=function(){return mi=Object.assign||function(e){var t,n,r,i;for(n=1,r=arguments.length;n<r;n++)for(i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},mi.apply(this,arguments)},Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError,vi=ce({},function(e){return{target:e.target,currentTarget:e.currentTarget}}),fi=ce(),pi=ce(),Ei=function(e){void 0===e&&(e={});var t=le(null);return t.options=mi({async:!0,ssr:!1},e),t}({async:!0,ssr:"undefined"!=typeof document}),gi=(0,Yt.createContext)(void 0),hi=[],_i=Yt.forwardRef(function(e,t){var n,r,i,o,a,u=Yt.useState(),l=u[0],c=u[1],s=Yt.useRef(),d=Yt.useRef(!1),m=Yt.useRef(null),v=Yt.useState({})[1],f=e.children,p=e.disabled,E=void 0!==p&&p,g=e.noFocusGuards,h=void 0!==g&&g,_=e.persistentFocus,b=void 0!==_&&_,y=e.crossFrame,C=void 0===y||y,N=e.autoFocus,A=void 0===N||N,T=(e.allowTextSelection,e.group),O=e.className,R=e.whiteList,S=e.hasPositiveIndices,x=e.shards,I=void 0===x?hi:x,L=e.as,w=void 0===L?"div":L,D=e.lockProps,M=void 0===D?{}:D,P=e.sideCar,k=e.returnFocus,B=void 0!==k&&k,U=e.focusOptions,F=e.onActivation,j=e.onDeactivation,H=Yt.useState({})[0],Y=Yt.useCallback(function(e){var t,n,r=e.captureFocusRestore;m.current||(n=null==(t=document)?void 0:t.activeElement,m.current=n,n!==document.body&&(m.current=r(n))),s.current&&F&&F(s.current),d.current=!0,v()},[F]),z=Yt.useCallback(function(){d.current=!1,j&&j(s.current),v()},[j]),V=Yt.useCallback(function(e){var t,n,r,i=m.current;i&&(t=("function"==typeof i?i():i)||document.body,(n="function"==typeof B?B(t):B)&&(r="object"==typeof n?n:void 0,m.current=null,e?Promise.resolve().then(function(){return t.focus(r)}):t.focus(r)))},[B]),G=Yt.useCallback(function(e){d.current&&vi.useMedium(e)},[]),q=fi.useMedium,W=Yt.useCallback(function(e){s.current!==e&&(s.current=e,c(e))},[]),K=oe(((n={})[li]=E&&"disabled",n[ui]=T,n),M),X=!0!==h,$=X&&"tail"!==h,Q=(r=[t,W],i=function(e){return r.forEach(function(t){return ae(t,e)})},(o=(0,Yt.useState)(function(){return{value:null,callback:i,facade:{get current(){return o.value},set current(e){var t=o.value;t!==e&&(o.value=e,o.callback(e,t))}}}})[0]).callback=i,a=o.facade,ci(function(){var e,t,n,i=si.get(a);i&&(e=new Set(i),t=new Set(r),n=a.current,e.forEach(function(e){t.has(e)||ae(e,null)}),t.forEach(function(t){e.has(t)||ae(t,n)})),si.set(a,r)},[r]),a),Z=Yt.useMemo(function(){return{observed:s,shards:I,enabled:!E,active:d.current}},[E,d.current,I,l]);return Yt.createElement(Yt.Fragment,null,X&&[Yt.createElement("div",{key:"guard-first","data-focus-guard":!0,tabIndex:E?-1:0,style:di}),S?Yt.createElement("div",{key:"guard-nearest","data-focus-guard":!0,tabIndex:E?-1:1,style:di}):null],!E&&Yt.createElement(P,{id:H,sideCar:Ei,observed:l,disabled:E,persistentFocus:b,crossFrame:C,autoFocus:A,whiteList:R,shards:I,onActivation:Y,onDeactivation:z,returnFocus:V,focusOptions:U,noFocusGuards:h}),Yt.createElement(w,oe({ref:Q},K,{className:O,onBlur:q,onFocus:G}),Yt.createElement(gi.Provider,{value:Z},f)),$&&Yt.createElement("div",{"data-focus-guard":!0,tabIndex:E?-1:0,style:di}))}),_i.propTypes={};const gu=_i;bi=function(e){for(var t=Array(e.length),n=0;n<e.length;++n)t[n]=e[n];return t},yi=function(e){return Array.isArray(e)?e:[e]},Ci=function(e){return Array.isArray(e)?e[0]:e},Ni=function(e){return e.parentNode&&e.parentNode.nodeType===Node.DOCUMENT_FRAGMENT_NODE?e.parentNode.host:e.parentNode},Ai=function(e){return e===document||e&&e.nodeType===Node.DOCUMENT_NODE},Ti=function(e,t){var n,r=e.get(t);return void 0!==r?r:(n=function(e,t){return!e||Ai(e)||!function(e){if(e.nodeType!==Node.ELEMENT_NODE)return!1;var t=window.getComputedStyle(e,null);return!(!t||!t.getPropertyValue||"none"!==t.getPropertyValue("display")&&"hidden"!==t.getPropertyValue("visibility"))}(e)&&!function(e){return e.hasAttribute("inert")}(e)&&t(Ni(e))}(t,Ti.bind(void 0,e)),e.set(t,n),n)},Oi=function(e,t){var n,r=e.get(t);return void 0!==r?r:(n=function(e,t){return!(e&&!Ai(e))||!!Ii(e)&&t(Ni(e))}(t,Oi.bind(void 0,e)),e.set(t,n),n)},Ri=function(e){return e.dataset},Si=function(e){return"INPUT"===e.tagName},xi=function(e){return Si(e)&&"radio"===e.type},Ii=function(e){var t=e.getAttribute("data-no-autofocus");return![!0,"true",""].includes(t)},Li=function(e){var t;return Boolean(e&&(null===(t=Ri(e))||void 0===t?void 0:t.focusGuard))},wi=function(e){return!Li(e)},Di=function(e){return Boolean(e)},Mi=function(e,t){var n=Math.max(0,e.tabIndex),r=Math.max(0,t.tabIndex),i=n-r,o=e.index-t.index;if(i){if(!n)return 1;if(!r)return-1}return i||o},Pi=function(e,t,n){return bi(e).map(function(e,t){var r=function(e){return e.tabIndex<0&&!e.hasAttribute("tabindex")?0:e.tabIndex}(e);return{node:e,index:t,tabIndex:n&&-1===r?(e.dataset||{}).focusGuard?0:-1:r}}).filter(function(e){return!t||e.tabIndex>=0}).sort(Mi)},ki=["button:enabled","select:enabled","textarea:enabled","input:enabled","a[href]","area[href]","summary","iframe","object","embed","audio[controls]","video[controls]","[tabindex]","[contenteditable]","[autofocus]"].join(","),Bi="".concat(ki,", [data-focus-guard]"),Ui=function(e,t){return bi((e.shadowRoot||e).children).reduce(function(e,n){return e.concat(n.matches(t?Bi:ki)?[n]:[],Ui(n))},[])},Fi=function(e,t){return e.reduce(function(e,n){var r,i=Ui(n,t),o=(r=[]).concat.apply(r,i.map(function(e){return function(e,t){var n;return e instanceof HTMLIFrameElement&&(null===(n=e.contentDocument)||void 0===n?void 0:n.body)?Fi([e.contentDocument.body],t):[e]}(e,t)}));return e.concat(o,n.parentNode?bi(n.parentNode.querySelectorAll(ki)).filter(function(e){return e===n}):[])},[])},ji=function(e,t){return bi(e).filter(function(e){return Ti(t,e)}).filter(function(e){return function(e){return!((Si(e)||function(e){return"BUTTON"===e.tagName}(e))&&("hidden"===e.type||e.disabled))}(e)})},Hi=function(e,t){return void 0===t&&(t=new Map),bi(e).filter(function(e){return Oi(t,e)})},Yi=function(e,t,n){return Pi(ji(Fi(e,n),t),!0,n)},zi=function(e,t){return Pi(ji(Fi(e),t),!1)},Vi=function(e,t){return e.shadowRoot?Vi(e.shadowRoot,t):!(void 0===Object.getPrototypeOf(e).contains||!Object.getPrototypeOf(e).contains.call(e,t))||bi(e.children).some(function(e){var n,r;return e instanceof HTMLIFrameElement?!!(r=null===(n=e.contentDocument)||void 0===n?void 0:n.body)&&Vi(r,t):Vi(e,t)})},Gi=function(e){if(void 0===e&&(e=document),e&&e.activeElement){var t=e.activeElement;return t.shadowRoot?Gi(t.shadowRoot):t instanceof HTMLIFrameElement&&function(){try{return t.contentWindow.document}catch(e){return}}()?Gi(t.contentWindow.document):t}},qi=function(e){return e.parentNode?qi(e.parentNode):e},Wi=function(e){return yi(e).filter(Boolean).reduce(function(e,t){var n=t.getAttribute(ui);return e.push.apply(e,n?function(e){var t,n,r,i,o;for(t=new Set,n=e.length,r=0;r<n;r+=1)for(i=r+1;i<n;i+=1)((o=e[r].compareDocumentPosition(e[i]))&Node.DOCUMENT_POSITION_CONTAINED_BY)>0&&t.add(i),(o&Node.DOCUMENT_POSITION_CONTAINS)>0&&t.add(r);return e.filter(function(e,n){return!t.has(n)})}(bi(qi(t).querySelectorAll("[".concat(ui,'="').concat(n,'"]:not([').concat(li,'="disabled"])')))):[t]),e},[])},Ki=function(e,t){return void 0===t&&(t=Gi(Ci(e).ownerDocument)),!(!t||t.dataset&&t.dataset.focusGuard)&&Wi(e).some(function(e){return Vi(e,t)||function(e,t){return Boolean(bi(e.querySelectorAll("iframe")).some(function(e){return function(e,t){return e===t}(e,t)}))}(e,t)})},Xi=function(e,t){e&&("focus"in e&&e.focus(t),"contentWindow"in e&&e.contentWindow&&e.contentWindow.focus())},$i=function(e,t){return xi(e)&&e.name?function(e,t){return t.filter(xi).filter(function(t){return t.name===e.name}).filter(function(e){return e.checked})[0]||e}(e,t):e},Qi=function(e){return e[0]&&e.length>1?$i(e[0],e):e[0]},Zi=function(e,t){return e.indexOf($i(t,e))},Ji="NEW_FOCUS",eo=function(e,t,n){var r,i=e.map(function(e){return e.node}),o=Hi(i.filter((r=n,function(e){var t,n=null===(t=Ri(e))||void 0===t?void 0:t.autofocus;return e.autofocus||void 0!==n&&"false"!==n||r.indexOf(e)>=0})));return o&&o.length?Qi(o):Qi(Hi(t))},to=function(e,t){return void 0===t&&(t=[]),t.push(e),e.parentNode&&to(e.parentNode.host||e.parentNode,t),t},no=function(e,t){var n,r,i,o;for(n=to(e),r=to(t),i=0;i<n.length;i+=1)if(o=n[i],r.indexOf(o)>=0)return o;return!1},ro=function(e,t,n){var r=yi(e),i=yi(t),o=r[0],a=!1;return i.filter(Boolean).forEach(function(e){a=no(a||e,e)||a,n.filter(Boolean).forEach(function(e){var t=no(o,e);t&&(a=!a||Vi(t,a)?t:no(t,a))})}),a},io=function(e,t){return e.reduce(function(e,n){return e.concat(function(e,t){return ji((n=e.querySelectorAll("[".concat("data-autofocus-inside","]")),bi(n).map(function(e){return Fi([e])}).reduce(function(e,t){return e.concat(t)},[])),t);var n}(n,t))},[])},oo=function(e,t){var n,r,i,o,a,u,l,c,s,d=Gi(yi(e).length>0?document:Ci(e).ownerDocument),m=Wi(e).filter(wi),v=ro(d||e,e,m),f=new Map,p=zi(m,f),E=p.filter(function(e){var t=e.node;return wi(t)});if(E[0])return o=zi([v],f).map(function(e){return e.node}),n=o,r=E,i=new Map,r.forEach(function(e){return i.set(e.node,e)}),a=n.map(function(e){return i.get(e)}).filter(Di),u=a.map(function(e){return e.node}),l=a.filter(function(e){return e.tabIndex>=0}).map(function(e){return e.node}),c=function(e,t,n,r,i){var o,a,u,l,c,s,d,m,v,f,p,E,g=e.length,h=e[0],_=e[g-1],b=Li(r);if(!(r&&e.indexOf(r)>=0))return o=void 0!==r?n.indexOf(r):-1,a=i?n.indexOf(i):o,u=i?e.indexOf(i):-1,-1===o?-1!==u?u:Ji:-1===u?Ji:(s=o-a,d=n.indexOf(h),m=n.indexOf(_),l=n,c=new Set,l.forEach(function(e){return c.add($i(e,l))}),v=l.filter(function(e){return c.has(e)}),f=(void 0!==r?v.indexOf(r):-1)-(i?v.indexOf(i):o),!s&&u>=0||0===t.length?u:(p=Zi(e,t[0]),E=Zi(e,t[t.length-1]),o<=d&&b&&Math.abs(s)>1?E:o>=m&&b&&Math.abs(s)>1?p:s&&Math.abs(f)>1?u:o<=d?E:o>m?p:s?Math.abs(s)>1?u:(g+u+s)%g:void 0))}(u,l,o,d,t),c===Ji?(s=eo(p,l,io(m,f))||eo(p,u,io(m,f)))?{node:s}:void 0:void 0===c?c:a[c]},ao=0,uo=!1,lo=function(e,t,n){void 0===n&&(n={});var r=oo(e,t);if(!uo&&r){if(ao>2)return uo=!0,void setTimeout(function(){uo=!1},1);ao++,Xi(r.node,n.focusOptions),ao--}},co=function(e){var t=function(e){if(!e)return null;for(var t=[],n=e;n&&n!==document.body;)t.push({current:me(n),parent:me(n.parentElement),left:me(n.previousElementSibling),right:me(n.nextElementSibling)}),n=n.parentElement;return{element:me(e),stack:t,ownerDocument:e.ownerDocument}}(e);return function(){return function(e){var t,n,r,i,o,a,u,l,c,s,d,m,v,f,p,E,g,h,_,b,y;if(e)for(a=e.stack,u=e.ownerDocument,l=new Map,c=0,s=a;c<s.length;c++)if((m=null===(t=(d=s[c]).parent)||void 0===t?void 0:t.call(d))&&u.contains(m)){for(v=null===(n=d.left)||void 0===n?void 0:n.call(d),f=d.current(),p=m.contains(f)?f:void 0,E=null===(r=d.right)||void 0===r?void 0:r.call(d),g=Yi([m],l),h=null!==(o=null!==(i=null!=p?p:null==v?void 0:v.nextElementSibling)&&void 0!==i?i:E)&&void 0!==o?o:v;h;){for(_=0,b=g;_<b.length;_++)if(y=b[_],null==h?void 0:h.contains(y.node))return y.node;h=h.nextElementSibling}if(g.length)return g[0].node}}(t)}},so=function(e,t,n){var r,i,o;void 0===t&&(t={}),r=function(e){return Object.assign({scope:document.body,cycle:!0,onlyTabbable:!0},e)}(t),i=function(e,t,n){var r,i,o;return e&&t?(r=yi(t)).every(function(t){return!Vi(t,e)})?{}:(o=(i=n?Yi(r,new Map):zi(r,new Map)).findIndex(function(t){return t.node===e}),-1!==o?{prev:i[o-1],next:i[o+1],first:i[0],last:i[i.length-1]}:void 0):{}}(e,r.scope,r.onlyTabbable),i&&(o=n(i,r.cycle))&&Xi(o.node,r.focusOptions)},mo=function(e,t,n){var r,i,o,a,u=(i=e,o=null===(r=t.onlyTabbable)||void 0===r||r,{first:(a=o?Yi(yi(i),new Map):zi(yi(i),new Map))[0],last:a[a.length-1]})[n];u&&Xi(u.node,t.focusOptions)},vo=function(e){return e&&"current"in e?e.current:e},fo=function(){return document&&document.activeElement===document.body},po=null,Eo=null,go=function(){return null},ho=null,_o=!1,bo=!1,yo=function(){return!0},Co=function e(t,n,r){return n&&(n.host===t&&(!n.activeElement||r.contains(n.activeElement))||n.parentNode&&e(t,n.parentNode,r))},No=function(e){return zi(e,new Map)},Ao=function(){var e,t,n,r,i,o,a,u,l,c,s,d,m,v,f,p,E,g,h,_,b,y,C=!1;return po&&(l=(u=po).observed,c=u.persistentFocus,s=u.autoFocus,d=u.shards,m=u.crossFrame,v=u.focusOptions,f=u.noFocusGuards,p=l||ho&&ho.portaledElement,!fo()||!Eo||document.body.contains(Eo)&&No([(a=Eo).parentNode]).some(function(e){return e.node===a})||(Eo=null,(E=go())&&E.focus()),g=document&&document.activeElement,p&&(h=[p].concat(d.map(vo).filter(Boolean)),g&&!function(e){return(po.whiteList||yo)(e)}(g)||(c||function(){if(!(m?Boolean(_o):"meanwhile"===_o)||!f||!Eo||bo)return!1;var e=No(h),t=e.findIndex(function(e){return e.node===Eo});return 0===t||t===e.length-1}()||!(fo()||function(e){void 0===e&&(e=document);var t=Gi(e);return!!t&&bi(e.querySelectorAll("[".concat("data-no-focus-lock","]"))).some(function(e){return Vi(e,t)})}())||!Eo&&s)&&(p&&!(Ki(h)||g&&function(e,t){return t.some(function(t){return Co(e,t,t)})}(g,h)||(o=g,ho&&ho.portaledElement===o))&&(document&&!Eo&&g&&!s?(g.blur&&g.blur(),document.body.focus()):(C=lo(h,Eo,{focusOptions:v}),ho={})),_o=!1,Eo=document&&document.activeElement,go=co(Eo)),document&&g!==document.activeElement&&document.querySelector("[data-focus-auto-guard]")&&(_=document&&document.activeElement,t=Wi(e=h).filter(wi),n=ro(e,e,t),r=Pi(Fi([n],!0),!0,!0),i=Fi(t,!1),b=r.map(function(e){var t=e.node;return{node:t,index:e.index,lockItem:i.indexOf(t)>=0,guard:Li(t)}}),y=b.map(function(e){return e.node}).indexOf(_),y>-1&&(b.filter(function(e){var t=e.guard,n=e.node;return t&&n.dataset.focusAutoGuard}).forEach(function(e){return e.node.removeAttribute("tabIndex")}),fe(y,b.length,1,b),fe(y,-1,-1,b))))),C},To=function(e){Ao()&&e&&(e.stopPropagation(),e.preventDefault())},Oo=function(){return ve(Ao)},Ro=function(){bo=!0},So=function(){bo=!1,_o="just",ve(function(){_o="meanwhile"})},xo={moveFocusInside:lo,focusInside:Ki,focusNextElement:function(e,t){void 0===t&&(t={}),so(e,t,function(e,t){var n=e.next,r=e.first;return n||t&&r})},focusPrevElement:function(e,t){void 0===t&&(t={}),so(e,t,function(e,t){var n=e.prev,r=e.last;return n||t&&r})},focusFirstElement:function(e,t){void 0===t&&(t={}),mo(e,t,"first")},focusLastElement:function(e,t){void 0===t&&(t={}),mo(e,t,"last")},captureFocusRestore:co},vi.assignSyncMedium(function(e){var t=e.target,n=e.currentTarget;n.contains(t)||(ho={observerNode:n,portaledElement:t})}),fi.assignMedium(Oo),pi.assignMedium(function(e){return e(xo)});const hu=(Io=function(e){return e.filter(function(e){return!e.disabled})},Lo=function(e){var t,n,r=e.slice(-1)[0];r&&!po&&(document.addEventListener("focusin",To),document.addEventListener("focusout",Oo),window.addEventListener("focus",Ro),window.addEventListener("blur",So)),n=(t=po)&&r&&r.id===t.id,po=r,t&&!n&&(t.onDeactivation(),e.filter(function(e){return e.id===t.id}).length||t.returnFocus(!r)),r?(Eo=null,n&&t.observed===r.observed||r.onActivation(xo),Ao(),ve(Ao)):(document.removeEventListener("focusin",To),document.removeEventListener("focusout",Oo),window.removeEventListener("focus",Ro),window.removeEventListener("blur",So),Eo=null)},function(e){function t(){n=Io(a.map(function(e){return e.props})),Lo(n)}var n,r,i,o,a=[],u=function(r){function i(){return r.apply(this,arguments)||this}var o,u,l=r;return(o=i).prototype=Object.create(l.prototype),o.prototype.constructor=o,se(o,l),i.peek=function(){return n},(u=i.prototype).componentDidMount=function(){a.push(this),t()},u.componentDidUpdate=function(){t()},u.componentWillUnmount=function(){var e=a.indexOf(this);a.splice(e,1),t()},u.render=function(){return Vt().createElement(e,this.props)},i}(Yt.PureComponent);return r=u,i="displayName",o="SideEffect("+function(e){return e.displayName||e.name||"Component"}(e)+")",(i=function(e){var t=function(e){var t,n;if("object"!=de(e)||!e)return e;if(void 0!==(t=e[Symbol.toPrimitive])){if("object"!=de(n=t.call(e,"string")))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==de(t)?t:t+""}(i))in r?Object.defineProperty(r,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):r[i]=o,u})(function(){return null});wo=Yt.forwardRef(function(e,t){return Yt.createElement(gu,oe({sideCar:hu,ref:t},e))}),(Do=gu.propTypes||{}).sideCar,function(e,t){var n,r;if(null==e)return{};for(r in n={},e)if({}.hasOwnProperty.call(e,r)){if(t.includes(r))continue;n[r]=e[r]}}(Do,["sideCar"]),wo.propTypes={};const _u=wo,bu=[la.vi_suitcase_loader_mc,la.vi_popcorn_loader_mc,la.vi_tv_loader_mc,la.vi_mobile_loader_mc],yu=({"aria-label":e})=>{const[t,n]=(0,Yt.useState)(0);return(0,Yt.useEffect)(()=>{const e=setInterval(()=>{n(e=>(e+1)%bu.length)},500);return()=>{clearInterval(e)}},[]),Vt().createElement("div",{className:"vrui-animate-icons-flipper vrui-w-fit"},Vt().createElement(ma,{iconClass:bu[t].icon,iconName:bu[t].name,className:"vrui-text-[32px]","aria-label":e}))};Mo=n(514);const Cu=({container:e=document.body,children:t})=>(0,Mo.createPortal)(t,e),Nu=({text:e,loaderAriaLabel:t="Content is loading",focusLockOptions:n={returnFocus:!0,lockProps:{tabIndex:-1}},className:r})=>{const i=(0,Yt.useRef)(null);return oa(i),Vt().createElement(Cu,null,Vt().createElement("div",{className:"vrui-fixed vrui-z-[9999] vrui-top-0"},Vt().createElement(_u,{as:"div",...n||{}},Vt().createElement("div",{className:["vrui-bg-black-1 vrui-bg-opacity-50 vrui-transition-opacity","vrui-h-screen vrui-w-screen","vrui-flex vrui-justify-center vrui-items-center","vrui-fixed vrui-inset-0 vrui-m-auto vrui-z-[9999]"].join(" ").trim(),role:"alert","aria-busy":!0},Vt().createElement("div",{className:["vrui-min-w-fit vrui-min-h-fit vrui-w-fit vrui-self-center vrui-shadow-xl","vrui-py-[16px] vrui-px-[24px] vrui-mx-16","vrui-bg-white vrui-rounded-full sm:vrui-rounded-[74px] vrui-select-none","vrui-flex vrui-flex-row vrui-justify-center vrui-items-center vrui-gap-[20px]",r].join(" ").trim(),ref:i},Vt().createElement(yu,{"aria-label":t}),Vt().createElement(Ba,{className:"vrui-block vrui-text-12 vrui-font-poppins-Regular vrui-font-normal"},Ua(e)))))))},Au={solidRed:"vrui-font-poppins-Semibold vrui-rounded-4 vrui-bg-red vrui-text-white vrui-border-red vrui-border-2 vrui-border-solid hover:vrui-bg-red-1 hover:vrui-border-red-1 focus:vrui-bg-red-1 focus:vrui-border-red-1 focus:vrui-outline-blue focus:vrui-outline focus:vrui-outline-2 focus:vrui-outline-offset-3 vrui-inline-block",outlinedBlack:"vrui-font-poppins-Semibold vrui-rounded-4 vrui-bg-transparent vrui-text-darkblue  vrui-border-darkblue vrui-border-2 vrui-border-solid hover:vrui-bg-transparent-1 focus:vrui-outline-blue focus:vrui-outline focus:vrui-outline-2 focus:vrui-outline-offset-3 vrui-inline-block",solidWhite:"vrui-font-poppins-Semibold vrui-rounded-4 vrui-bg-white vrui-text-darkblue vrui-border-white vrui-border-2 vrui-border-solid hover:vrui-bg-pink hover:vrui-border-pink focus:vrui-bg-pink focus:vrui-border-pink focus:vrui-outline-white focus:vrui-outline focus:vrui-outline-2 focus:vrui-outline-offset-3 vrui-inline-block",outlinedWhite:"vrui-font-poppins-Semibold vrui-rounded-4 vrui-bg-transparent vrui-text-white  vrui-border-white vrui-border-2 vrui-border-solid hover:vrui-bg-transparent-1 focus:vrui-bg-transparent-1 focus:vrui-outline-white focus:vrui-outline focus:vrui-outline-2 focus:vrui-outline-offset-3 vrui-inline-block",textRed:"vrui-font-poppins-Semibold vrui-rounded-4 vrui-bg-transparent vrui-text-red vrui-underline vrui-underline-offset-4 hover:vrui-text-red-1 hover:vrui-no-underline focus:vrui-text-red-1 focus:vrui-no-underline focus:vrui-outline-blue focus:vrui-outline focus:vrui-outline-2 focus:vrui-outline-offset-3 !vrui-p-0",textBlue:"vrui-font-poppins-Semibold vrui-rounded-4 vrui-bg-transparent vrui-text-blue vrui-underline vrui-underline-offset-4 hover:vrui-text-blue-1 hover:vrui-no-underline focus:vrui-text-blue-1 focus:vrui-no-underline focus:vrui-outline-blue focus:vrui-outline focus:vrui-outline-2 focus:vrui-outline-offset-3 !vrui-p-0",textWhite:"vrui-font-poppins-Semibold vrui-rounded-4 vrui-bg-transparent vrui-text-white vrui-underline vrui-underline-offset-4 hover:vrui-text-pink hover:vrui-no-underline focus:vrui-text-pink focus:vrui-no-underline focus:vrui-outline-white focus:vrui-outline focus:vrui-outline-2 focus:vrui-outline-offset-3 !vrui-p-0",default:""},Tu={regular:"vrui-text-16 vrui-leading-18 vrui-py-11 vrui-px-28",small:"vrui-text-14 vrui-leading-18 vrui-py-5 vrui-px-14",default:""},Ou=(0,Yt.forwardRef)(function({variant:e="textBlue",size:t="regular",className:n,href:r,...i},o){const a={linkStyle:[Tu[t],Au[e],n].join(" ").trim()};return Vt().createElement("a",{className:a.linkStyle,href:r,ref:o,...i})}),Ru=Ou,Su=({icon:e,text:t,className:n,position:r,href:i,...o})=>{const a=(0,Yt.useMemo)(()=>"right"===r?Vt().createElement(Vt().Fragment,null,Vt().createElement(Ba,{elementType:"span",className:"group-hover:vrui-underline vrui-mr-8"},t),e):Vt().createElement(Vt().Fragment,null,e,Vt().createElement(Ba,{elementType:"span",className:"group-hover:vrui-underline vrui-ml-8"},t)),[r,e,t]);return Vt().createElement(Ru,{className:["vrui-group vrui-no-underline",n,"right"===r?"vrui-inline-block":"vrui-inline-flex vrui-items-center"].join(" ").trim(),href:i,...o},a)},xu=(0,Yt.forwardRef)(function({className:e,children:t,...n},r){return Vt().createElement("button",{className:["vrui-px-16 vrui-py-8 vrui-inline-flex vrui-items-center vrui-rounded-[100px] vrui-bg-blue hover:vrui-bg-blue-1 vrui-text-white focus:vrui-outline-blue focus:vrui-outline focus:vrui-outline-2 focus:vrui-outline-offset-3",e].join(" ").trim(),ref:r,...n},t)}),Iu=xu,Lu={ordinaryPrice:{divClassName:null,spanClassName:null,forwardSlashClassName:null},defaultPrice:{divClassName:"vrui-font-bold vrui-text-18 vrui-text-blue",spanClassName:null,forwardSlashClassName:null},bigPrice:{divClassName:"vrui-font-poppins-Semibold vrui-text-28 vrui-leading-25 sm:vrui-text-40 sm:vrui-leading-40 -vrui-tracking-1 vrui-whitespace-nowrap",spanClassName:"vrui-text-16 vrui-leading-16 sm:vrui-text-20 sm:vrui-leading-30 -vrui-tracking-0.45 vrui-relative vrui-top-0 sm:-vrui-top-3 vrui-align-top",forwardSlashClassName:"vrui-text-[11px] vrui-leading-16 sm:vrui-text-[13px] sm:vrui-leading-20 -vrui-tracking-0.45 vrui-relative vrui-top-0 sm:vrui-top-3 vrui-align-top"},smallPrice:{divClassName:"vrui-font-poppins-Semibold vrui-text-28 vrui-leading-25 -vrui-tracking-1 vrui-whitespace-nowrap",spanClassName:"vrui-text-16 vrui-leading-16 -vrui-tracking-0.45 vrui-relative vrui-align-top",forwardSlashClassName:"vrui-text-[11px] vrui-leading-16 -vrui-tracking-0.45 vrui-relative vrui-align-top"}},wu={strikeText:{en:"previous price",fr:"précédent Coût"},decimalPointText:{en:".",fr:","},perMonth:{visibleText:{en:"MO.",fr:"MOIS"},screenReaderText:{en:" per month",fr:" par mois"}},perDay:{visibleText:{en:"DAY",fr:"JOUR"},screenReaderText:{en:" per day",fr:" par jour"}}},Du={CR:{en:"Credit",fr:"Crédit"},"-":{en:"Negative",fr:"Négatif"}},Mu=({price:e,variant:t="defaultPrice",strike:n,reverse:r,language:i="en",suffixText:o,className:a,negativeIndicator:u="CR",showZeroDecimalPart:l="ifNoSuffixOrZero",srText:c})=>{if(!e&&0!==e)return null;const s=e<0,d=Math.abs(e),m=d.toFixed(2).toString().split("."),v=m[0];let f=m[1]||"";"ifNoSuffixOrZero"!==l||void 0===o&&0!==e?!0===l?f=parseFloat(v)===d?"00":f:!1===l&&(f=""):(f=parseFloat(v)===d?"00":f,o&&"00"===f&&(f=""));const p=(0,Yt.useMemo)(()=>{let e="",a="",l="";const s="fr"===i,d=n?"vrui-line-through":"";let m,p,E;return c?l=`${c} `:null!==u&&(l=`${Du[u][i]} `),"bigPrice"!==t&&"smallPrice"!==t||(e=s?"":"vrui-mr-px",(f||o)&&(a=s?"":"vrui-ml-[2px]")),m=r?"vrui-text-white":n?"vrui-text-gray":"ordinaryPrice"===t?"":"vrui-text-darkblue",s?(p=Vt().createElement(Vt().Fragment,null,Vt().createElement("span",{className:d},v),f?Vt().createElement("span",{className:[Lu[t].spanClassName,a,d].join(" ").trim()},wu.decimalPointText.fr+f):null,Vt().createElement("span",{className:[Lu[t].spanClassName,e].join(" ").trim()}," $")),E=`${v}${f?wu.decimalPointText.fr+f:""} $`):(p=Vt().createElement(Vt().Fragment,null,Vt().createElement("span",{className:[Lu[t].spanClassName,e].join(" ").trim()},"$"),Vt().createElement("span",{className:d},v),f?Vt().createElement("span",{className:[Lu[t].spanClassName,a,d].join(" ").trim()},wu.decimalPointText.en+f):null),E=`$${v}${f?wu.decimalPointText.en+f:""}`),{dollarMargin:e,leftMargin:a,strikeClass:d,priceTextColor:m,priceMarkup:p,priceScreenReaderText:E,negativeText:l}},[v,t,f,o,r,n,i,c]);return Vt().createElement("div",null,Vt().createElement("div",{className:[Lu[t].divClassName,p.priceTextColor,a].join(" ").trim(),"aria-hidden":"true"},s&&`${u?.toString()} `,p.priceMarkup,o?Vt().createElement(Vt().Fragment,null,Vt().createElement("span",{className:[Lu[t].forwardSlashClassName,p.leftMargin].join(" ").trim()},"/"),Vt().createElement("span",{className:[Lu[t].spanClassName,p.leftMargin].join(" ").trim()},wu[o].visibleText[i])):null),n&&Vt().createElement("span",{className:"vrui-sr-only"},wu.strikeText[i]," "),Vt().createElement("span",{className:"vrui-sr-only"},s&&p.negativeText,p.priceScreenReaderText,o?wu[o].screenReaderText[i]:null))},Pu={topRight:"vrui-right-24 vrui-top-24",topLeft:"vrui-top-16 vrui-left-16 sm:vrui-top-32 sm:vrui-left-32",leftCenter:"vrui-left-12 vrui-transform -vrui-translate-y-1/2 vrui-top-1/2",hidden:"vrui-hidden"},ku=(0,Yt.forwardRef)(function({radioPlacement:e="topRight",radioPlacementMobile:t,radioPlacementClassName:n,borderRadiusClassName:r,cursorPointer:i=!1,...o},a){const{width:u}=ia(),l=(0,Yt.useMemo)(()=>t&&u<768?t:e,[u]);return Vt().createElement("div",{className:"vrui-group/inputradio vrui-absolute vrui-right-0 vrui-top-0 vrui-leading-0 vrui-w-full vrui-h-full"},Vt().createElement("div",{className:["vrui-absolute vrui-w-full vrui-h-full group-has-[:checked]/inputradio:vrui-border-2 group-has-[:checked]/inputradio:vrui-border-blue group-has-[:focus-visible]/inputradio:vrui-outline-blue group-has-[:focus-visible]/inputradio:vrui-outline group-has-[:focus-visible]/inputradio:vrui-outline-2 group-has-[:focus-visible]/inputradio:vrui-outline-offset-3 group-has-[:focus-within]/inputradio:vrui-outline-blue group-has-[:focus-within]/inputradio:vrui-outline group-has-[:focus-within]/inputradio:vrui-outline-2 group-has-[:focus-within]/inputradio:vrui-outline-offset-3 transition-all",r||"vrui-rounded-16"].join(" ").trim()}),Vt().createElement("input",{type:"radio",className:"vrui-absolute vrui-left-0 vrui-top-0 vrui-w-full vrui-h-full vrui-z-10 vrui-opacity-0 "+(i?"vrui-cursor-pointer":""),ref:a,...o}),Vt().createElement("div",{className:["vrui-absolute",n||Pu[l]].join(" ")},Vt().createElement("div",{className:"vrui-w-24 vrui-h-24 vrui-rounded-12 vrui-border vrui-border-gray-2"}),Vt().createElement("div",{className:"vrui-scale-0 group-has-[:checked]/inputradio:vrui-scale-100 vrui-absolute vrui-w-12 vrui-h-12 vrui-bg-blue vrui-rounded-6 vrui-top-1/2 vrui-left-1/2 vrui-transform -vrui-translate-x-1/2 -vrui-translate-y-1/2 vrui-transition-transform"})))}),Bu={topRight:"vrui-py-32 vrui-px-24",topLeft:"vrui-p-16 sm:vrui-p-32",leftCenter:"vrui-p-12 vrui-pl-48",hidden:"vrui-p-8"},Uu=(0,Yt.forwardRef)(function({children:e,className:t,"aria-labelledby":n,"aria-describedby":r,name:i,radioPlacement:o="topRight",radioPlacementMobile:a,radioPlacementClassName:u,borderRadiusClassName:l,defaultPadding:c=!0,disabled:s,...d},m){return Vt().createElement("div",{className:["vrui-group vrui-border vrui-relative",c&&Bu[o],l||"vrui-rounded-16",t,s&&"vrui-bg-gray-5 vrui-border-gray-3 vrui-opacity-60"].join(" ").trim()},Vt().createElement(ku,{"aria-labelledby":n,"aria-describedby":r,name:i,ref:m,radioPlacement:o,radioPlacementMobile:a,radioPlacementClassName:u,borderRadiusClassName:l,disabled:s,...d}),e)}),Fu=Uu,ju=({text:e,variant:t,className:n,children:r,...i})=>{const o=(0,Yt.useMemo)(()=>({tagStyle:["vrui-font-semibold vrui-text-12 vrui-leading-14 vrui-rounded-6 vrui-py-4 vrui-px-8",{solidRedTag:"vrui-bg-red vrui-text-white",solidBlueTag:"vrui-bg-blue vrui-text-white",solidWhiteTag:"vrui-bg-white vrui-text-red",solidGreyTag:"vrui-bg-gray-4 vrui-text-white",solidBlackTag:"vrui-bg-darkblue vrui-text-white",solidGradientDarkBlueTag:"vrui-bg-gradient-to-l vrui-from-darkblue vrui-from-0% vrui-to-blue vrui-to-100% vrui-text-white",outlinedBlackTag:"vrui-text-black vrui-bg-transparent vrui-border-1 vrui-border-solid vrui-border-black",outlinedWhiteTag:"vrui-text-white vrui-bg-transparent vrui-border-1 vrui-border-solid vrui-border-white",outlinedRedTag:"vrui-text-red vrui-bg-transparent vrui-border-1 vrui-border-solid vrui-border-red"}[t],n].join(" ").trim()}),[t]);return Vt().createElement("span",{className:o.tagStyle,...i},e," ",r)},Hu={default:"group-has-[:focus-visible]/inputradiobutton:vrui-outline-blue group-has-[:focus-visible]/inputradiobutton:vrui-outline group-has-[:focus-visible]/inputradiobutton:vrui-outline-2 group-has-[:focus-visible]/inputradiobutton:vrui-outline-offset-3",boxedInMobile:"group-has-[:focus-visible]/inputradiobutton:sm:vrui-outline-blue group-has-[:focus-visible]/inputradiobutton:sm:vrui-outline group-has-[:focus-visible]/inputradiobutton:sm:vrui-outline-2 group-has-[:focus-visible]/inputradiobutton:sm:vrui-outline-offset-3"},Yu=(0,Yt.forwardRef)(function({id:e,name:t,value:n,hasError:r=!1,variant:i,...o},a){return Vt().createElement("div",{className:"vrui-relative vrui-group/inputradiobutton"},Vt().createElement("input",{type:"radio",id:e,name:t,value:n,className:"vrui-absolute vrui-size-full vrui-opacity-0 enabled:vrui-cursor-pointer disabled:vrui-cursor-default vrui-z-10",...o,ref:a}),Vt().createElement("div",{className:"vrui-relative"},Vt().createElement("div",{className:["vrui-size-24 vrui-rounded-12 vrui-border group-has-[:disabled]/inputradiobutton:vrui-opacity-40",r?"vrui-border-red":"vrui-border-gray-2",Hu[i]].join(" ").trim()}),Vt().createElement("div",{className:["vrui-scale-0 group-has-[:checked]/inputradiobutton:vrui-scale-100 vrui-absolute vrui-w-12 vrui-h-12 vrui-bg-blue vrui-rounded-6 vrui-top-1/2 vrui-left-1/2 vrui-transform -vrui-translate-x-1/2 -vrui-translate-y-1/2 vrui-transition-transform group-has-[:disabled]/inputradiobutton:vrui-opacity-40",r?"vrui-bg-red":"vrui-bg-blue"].join(" ").trim()})))}),zu=(0,Yt.forwardRef)(function({id:e,name:t,value:n,children:r,variant:i,hasError:o=!1,...a},u){const l={default:{wrapper:"vrui-group/label vrui-relative vrui-inline-flex vrui-w-full vrui-border-0",label:"vrui-pl-12 enabled:vrui-text-14 enabled:vrui-leading-19 vrui-cursor-pointer vrui-self-center group-has-[:disabled]/label:vrui-text-gray-7 group-has-[:disabled]/label:vrui-cursor-default group-has-[:checked]/label:vrui-font-bold group-has-[:checked:disabled]/label:vrui-font-normal"},boxedInMobile:{wrapper:"vrui-group/label vrui-relative vrui-inline-flex vrui-w-full vrui-rounded-6 vrui-border-1 sm:vrui-border-0 has-[:checked]:sm:vrui-border-0 has-[:checked]:vrui-border-2 vrui-p-15 sm:vrui-p-0 vrui-border-gray-3 has-[input:focus-visible]:vrui-outline has-[input:focus-visible]:vrui-outline-2 has-[input:focus-visible]:vrui-outline-offset-3 has-[input:focus-visible]:vrui-outline-blue has-[input:focus-visible]:sm:vrui-outline-0 has-[:checked:disabled]:vrui-border-[#BABEC2]"+(o?" vrui-border-red has-[:checked]:vrui-border-red":" vrui-border-gray-3 has-[:checked]:vrui-border-blue"),label:"vrui-pl-12 vrui-text-14 vrui-leading-19 enabled:vrui-cursor-pointer group-has-[:checked]/label:vrui-font-bold group-has-[:checked:disabled]/label:vrui-font-normal vrui-self-center group-has-[:disabled]/label:vrui-text-gray-7 group-has-[:disabled]/label:vrui-cursor-default group-has-[:checked:disabled]/label:vrui-font-normal"}};return Vt().createElement(Vt().Fragment,null,Vt().createElement("div",{className:[l[i].wrapper].join(" ").trim()},Vt().createElement(Yu,{id:e,name:t,value:n,ref:u,hasError:o,variant:i,...a}),Vt().createElement("label",{htmlFor:e,className:[l[i].label].join(" ").trim()},r)))}),Vu=zu,Gu=({children:e,className:t,...n})=>Vt().createElement("div",{className:["vrui-mb-24",t].join(" ").trim(),...n},e),qu=({children:e,className:t,...n})=>Vt().createElement("div",{className:["vrui-mt-auto",t].join(" ").trim(),...n},e),Wu=(0,Yt.createContext)({selectedValue:0,setSelectedValue:()=>{},rangeSliderOptions:[],initializeRangeSliderOptions:()=>{},showRangeSliderOptions:!1,setShowRangeSliderOptions:()=>{}}),Ku=({className:e,children:t})=>{const[n,r]=(0,Yt.useState)(0),[i,o]=(0,Yt.useState)([]),[a,u]=(0,Yt.useState)(!1);return Vt().createElement(Wu.Provider,{value:{selectedValue:n,setSelectedValue:r,rangeSliderOptions:i,initializeRangeSliderOptions:e=>{o(t=>[...t,e])},showRangeSliderOptions:a,setShowRangeSliderOptions:u}},Vt().createElement("div",{className:e},t))},Xu=(0,Yt.forwardRef)(function({id:e,name:t,min:n,max:r,step:i,value:o,className:a,onChange:u,onMouseUp:l,onTouchEnd:c,...s},d){const{selectedValue:m,setSelectedValue:v,rangeSliderOptions:f}=(0,Yt.useContext)(Wu);return(0,Yt.useEffect)(()=>{v(o)},[]),Vt().createElement("input",{type:"range",id:e,name:t,min:n,max:r,step:i,value:m,ref:d,className:["vrui-range-slider-input vrui-leading-1 vrui-relative vrui-w-full vrui-h-10 vrui-rounded-[5px] vrui-appearance-none vrui-outline-none vrui-cursor-pointer vrui-my-5",a].join(" ").trim(),style:{WebkitAppearance:"none",border:"1px solid transparent",background:(()=>{const e=m/r*100;return`linear-gradient(to right, #4E4AE4 ${e}%, #ffffff ${e}%) padding-box, linear-gradient(to right, #4E4AE4 ${e}%, #858a98 ${e}%) border-box`})()},"aria-valuetext":((e,t)=>{const n=t?.find(t=>t.value===e);return n?.label})(m,f),onChange:e=>{v(parseFloat(e.target.value)),u&&u(e)},onMouseUp:e=>{l&&l(e)},onTouchEnd:e=>{c&&c(e)},...s})}),$u=()=>{const{selectedValue:e,rangeSliderOptions:t}=(0,Yt.useContext)(Wu);return Vt().createElement(Vt().Fragment,null,t&&t.map((t,n)=>Vt().createElement("div",{key:n,className:["vrui-relative vrui-justify-center vrui-w-1 first:vrui-bg-inherit last:vrui-bg-inherit vrui-h-10 vrui-flex",t?.value<=e?"vrui-bg-white":"vrui-bg-gray-7"].join(" ").trim()})))},Qu=({children:e,className:t,showOptions:n})=>{const{setShowRangeSliderOptions:r}=(0,Yt.useContext)(Wu);return(0,Yt.useEffect)(()=>{r(n??!1)},[n]),Vt().createElement(Vt().Fragment,null,Vt().createElement("div",{className:"vrui-range-slider-markers vrui-flex vrui-justify-between -vrui-mt-[15px] vrui-mb-0 vrui-pl-[2%] vrui-w-[98%] md:vrui-pl-[1%] md:vrui-w-[99%]"},Vt().createElement($u,null)),Vt().createElement("div",{className:["vrui-flex vrui-justify-between",t].join(" ").trim()},e))},Zu=({label:e,value:t})=>{const{rangeSliderOptions:n,initializeRangeSliderOptions:r,showRangeSliderOptions:i}=(0,Yt.useContext)(Wu);return(0,Yt.useEffect)(()=>{n.findIndex(e=>e.value===t)<0&&r({label:e,value:t})},[]),i?Vt().createElement("div",{className:"vrui-text-center"},e):null},Ju=({"aria-labelledby":e,"aria-describedby":t,children:n,...r})=>Vt().createElement(Fu,{"aria-labelledby":e,"aria-describedby":t,...r},n),el=(0,Yt.createContext)(null),tl=()=>{const e=(0,Yt.useContext)(el);if(null===e)throw new Error("Select components must be wrapped in <Select />");return e},nl=el;Po=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"].join(","),ko="undefined"==typeof Element,Bo=ko?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,Uo=!ko&&Element.prototype.getRootNode?function(e){var t;return null==e||null===(t=e.getRootNode)||void 0===t?void 0:t.call(e)}:function(e){return null==e?void 0:e.ownerDocument},Fo=function e(t,n){var r,i;return void 0===n&&(n=!0),""===(i=null==t||null===(r=t.getAttribute)||void 0===r?void 0:r.call(t,"inert"))||"true"===i||n&&t&&e(t.parentNode)},jo=function e(t,n,r){var i,o,a,u,l,c,s,d;for(i=[],o=Array.from(t);o.length;)a=o.shift(),Fo(a,!1)||("SLOT"===a.tagName?(l=e((u=a.assignedElements()).length?u:a.children,!0,r),r.flatten?i.push.apply(i,l):i.push({scopeParent:a,candidates:l})):(Bo.call(a,Po)&&r.filter(a)&&(n||!t.includes(a))&&i.push(a),c=a.shadowRoot||"function"==typeof r.getShadowRoot&&r.getShadowRoot(a),s=!Fo(c,!1)&&(!r.shadowRootFilter||r.shadowRootFilter(a)),c&&s?(d=e(!0===c?a.children:c.children,!0,r),r.flatten?i.push.apply(i,d):i.push({scopeParent:a,candidates:d})):o.unshift.apply(o,a.children)));return i},Ho=function(e){return!isNaN(parseInt(e.getAttribute("tabindex"),10))},Yo=function(e){if(!e)throw new Error("No node provided");return e.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||function(e){var t,n=null==e||null===(t=e.getAttribute)||void 0===t?void 0:t.call(e,"contenteditable");return""===n||"true"===n}(e))&&!Ho(e)?0:e.tabIndex},zo=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},Vo=function(e){return"INPUT"===e.tagName},Go=function(e){var t=e.getBoundingClientRect(),n=t.width,r=t.height;return 0===n&&0===r},qo=function(e,t){return!(t.disabled||Fo(t)||function(e){return Vo(e)&&"hidden"===e.type}(t)||function(e,t){var n,r,i,o,a=t.displayCheck,u=t.getShadowRoot;if("hidden"===getComputedStyle(e).visibility)return!0;if(n=Bo.call(e,"details>summary:first-of-type")?e.parentElement:e,Bo.call(n,"details:not([open]) *"))return!0;if(a&&"full"!==a&&"legacy-full"!==a){if("non-zero-area"===a)return Go(e)}else{if("function"==typeof u){for(r=e;e;){if(i=e.parentElement,o=Uo(e),i&&!i.shadowRoot&&!0===u(i))return Go(e);e=e.assignedSlot?e.assignedSlot:i||o===e.ownerDocument?i:o.host}e=r}if(function(e){var t,n,r,i,o,a,u,l=e&&Uo(e),c=null===(t=l)||void 0===t?void 0:t.host,s=!1;if(l&&l!==e)for(s=!!(null!==(n=c)&&void 0!==n&&null!==(r=n.ownerDocument)&&void 0!==r&&r.contains(c)||null!=e&&null!==(i=e.ownerDocument)&&void 0!==i&&i.contains(e));!s&&c;)s=!(null===(a=c=null===(o=l=Uo(c))||void 0===o?void 0:o.host)||void 0===a||null===(u=a.ownerDocument)||void 0===u||!u.contains(c));return s}(e))return!e.getClientRects().length;if("legacy-full"!==a)return!0}return!1}(t,e)||function(e){return"DETAILS"===e.tagName&&Array.prototype.slice.apply(e.children).some(function(e){return"SUMMARY"===e.tagName})}(t)||function(e){var t,n,r;if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(t=e.parentElement;t;){if("FIELDSET"===t.tagName&&t.disabled){for(n=0;n<t.children.length;n++)if("LEGEND"===(r=t.children.item(n)).tagName)return!!Bo.call(t,"fieldset[disabled] *")||!r.contains(e);return!0}t=t.parentElement}return!1}(t))},Wo=function(e,t){return!(function(e){return function(e){return Vo(e)&&"radio"===e.type}(e)&&!function(e){var t,n,r,i;if(!e.name)return!0;if(n=e.form||Uo(e),r=function(e){return n.querySelectorAll('input[type="radio"][name="'+e+'"]')},"undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.escape)t=r(window.CSS.escape(e.name));else try{t=r(e.name)}catch(e){return!1}return i=function(e,t){for(var n=0;n<e.length;n++)if(e[n].checked&&e[n].form===t)return e[n]}(t,e.form),!i||i===e}(e)}(t)||Yo(t)<0||!qo(e,t))},Ko=function(e){var t=parseInt(e.getAttribute("tabindex"),10);return!!(isNaN(t)||t>=0)},Xo=function e(t){var n=[],r=[];return t.forEach(function(t,i){var o=!!t.scopeParent,a=o?t.scopeParent:t,u=function(e,t){var n=Yo(e);return n<0&&t&&!Ho(e)?0:n}(a,o),l=o?e(t.candidates):a;0===u?o?n.push.apply(n,l):n.push(a):r.push({documentOrder:i,tabIndex:u,item:t,isScope:o,content:l})}),r.sort(zo).reduce(function(e,t){return t.isScope?e.push.apply(e,t.content):e.push(t.content),e},[]).concat(n)},$o=function(e,t){var n;return n=(t=t||{}).getShadowRoot?jo([e],t.includeContainer,{filter:Wo.bind(null,t),flatten:!1,getShadowRoot:t.getShadowRoot,shadowRootFilter:Ko}):function(e,t,n){if(Fo(e))return[];var r=Array.prototype.slice.apply(e.querySelectorAll(Po));return t&&Bo.call(e,Po)&&r.unshift(e),r.filter(n)}(e,t.includeContainer,Wo.bind(null,t)),Xo(n)};const rl=Math.min,il=Math.max,ol=Math.round,al=Math.floor,ul=e=>({x:e,y:e}),ll={left:"right",right:"left",bottom:"top",top:"bottom"},cl={start:"end",end:"start"},sl=ul(0),dl={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e;const o="fixed"===i,a=he(r),u=!!t&&Te(t.floating);if(r===a||u&&o)return n;let l={scrollLeft:0,scrollTop:0},c=ul(1);const s=ul(0),d=ye(r);if((d||!d&&!o)&&(("body"!==Ee(r)||Ne(a))&&(l=Ie(r)),ye(r))){const e=ct(r);c=ut(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-l.scrollLeft*c.x+s.x,y:n.y*c.y-l.scrollTop*c.y+s.y}},getDocumentElement:he,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e;const o=[..."clippingAncestors"===n?Te(t)?[]:function(e,t){const n=t.get(e);if(n)return n;let r=De(e,[],!1).filter(e=>be(e)&&"body"!==Ee(e)),i=null;const o="fixed"===xe(e).position;let a=o?Le(e):e;for(;be(a)&&!Se(a);){const t=xe(a),n=Oe(a);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&i&&["absolute","fixed"].includes(i.position)||Ne(a)&&!n&&mt(e,a))?r=r.filter(e=>e!==a):i=t,a=Le(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],a=o[0],u=o.reduce((e,n)=>{const r=dt(t,n,i);return e.top=il(r.top,e.top),e.right=rl(r.right,e.right),e.bottom=rl(r.bottom,e.bottom),e.left=il(r.left,e.left),e},dt(t,a,i));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:Et,getElementRects:async function(e){const t=this.getOffsetParent||Et,n=this.getDimensions,r=await n(e.floating);return{reference:vt(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){const{width:t,height:n}=ot(e);return{width:t,height:n}},getScale:ut,isElement:be,isRTL:function(e){return"rtl"===xe(e).direction}},ml=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:i,y:o,placement:a,middlewareData:u}=t,l=await async function(e,t){const{placement:n,platform:r,elements:i}=e,o=await(null==r.isRTL?void 0:r.isRTL(i.floating)),a=We(n),u=Ke(n),l="y"===Qe(n),c=["left","top"].includes(a)?-1:1,s=o&&l?-1:1,d=qe(t,e);let{mainAxis:m,crossAxis:v,alignmentAxis:f}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return u&&"number"==typeof f&&(v="end"===u?-1*f:f),l?{x:v*s,y:m*c}:{x:m*c,y:v*s}}(t,e);return a===(null==(n=u.offset)?void 0:n.placement)&&null!=(r=u.arrow)&&r.alignmentOffset?{}:{x:i+l.x,y:o+l.y,data:{...l,placement:a}}}}},vl=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:i}=t,{mainAxis:o=!0,crossAxis:a=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...l}=qe(e,t),c={x:n,y:r},s=await it(t,l),d=Qe(We(i)),m=Xe(d);let v=c[m],f=c[d];if(o){const e="y"===m?"bottom":"right";v=Ge(v+s["y"===m?"top":"left"],v,v-s[e])}if(a){const e="y"===d?"bottom":"right";f=Ge(f+s["y"===d?"top":"left"],f,f-s[e])}const p=u.fn({...t,[m]:v,[d]:f});return{...p,data:{x:p.x-n,y:p.y-r,enabled:{[m]:o,[d]:a}}}}}},fl=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,i,o,a;const{placement:u,middlewareData:l,rects:c,initialPlacement:s,platform:d,elements:m}=t,{mainAxis:v=!0,crossAxis:f=!0,fallbackPlacements:p,fallbackStrategy:E="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:h=!0,..._}=qe(e,t);if(null!=(n=l.arrow)&&n.alignmentOffset)return{};const b=We(u),y=Qe(s),C=We(s)===s,N=await(null==d.isRTL?void 0:d.isRTL(m.floating)),A=p||(C||!h?[et(s)]:function(e){const t=et(e);return[Je(e),t,Je(t)]}(s)),T="none"!==g;!p&&T&&A.push(...function(e,t,n,r){const i=Ke(e);let o=function(e,t,n){const r=["left","right"],i=["right","left"],o=["top","bottom"],a=["bottom","top"];switch(e){case"top":case"bottom":return n?t?i:r:t?r:i;case"left":case"right":return t?o:a;default:return[]}}(We(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(Je)))),o}(s,h,g,N));const O=[s,...A],R=await it(t,_),S=[];let x=(null==(r=l.flip)?void 0:r.overflows)||[];if(v&&S.push(R[b]),f){const e=function(e,t,n){void 0===n&&(n=!1);const r=Ke(e),i=Ze(e),o=$e(i);let a="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(a=et(a)),[a,et(a)]}(u,c,N);S.push(R[e[0]],R[e[1]])}if(x=[...x,{placement:u,overflows:S}],!S.every(e=>e<=0)){const e=((null==(i=l.flip)?void 0:i.index)||0)+1,t=O[e];if(t)return{data:{index:e,overflows:x},reset:{placement:t}};let n=null==(o=x.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(E){case"bestFit":{const e=null==(a=x.filter(e=>{if(T){const t=Qe(e.placement);return t===y||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=s}if(u!==n)return{reset:{placement:n}}}return{}}}},pl=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:i,rects:o,platform:a,elements:u,middlewareData:l}=t,{element:c,padding:s=0}=qe(e,t)||{};if(null==c)return{};const d=tt(s),m={x:n,y:r},v=Ze(i),f=$e(v),p=await a.getDimensions(c),E="y"===v,g=E?"top":"left",h=E?"bottom":"right",_=E?"clientHeight":"clientWidth",b=o.reference[f]+o.reference[v]-m[v]-o.floating[f],y=m[v]-o.reference[v],C=await(null==a.getOffsetParent?void 0:a.getOffsetParent(c));let N=C?C[_]:0;N&&await(null==a.isElement?void 0:a.isElement(C))||(N=u.floating[_]||o.floating[f]);const A=b/2-y/2,T=N/2-p[f]/2-1,O=rl(d[g],T),R=rl(d[h],T),S=O,x=N-p[f]-R,I=N/2-p[f]/2+A,L=Ge(S,I,x),w=!l.arrow&&null!=Ke(i)&&I!==L&&o.reference[f]/2-(I<S?O:R)-p[f]/2<0,D=w?I<S?I-S:I-x:0;return{[v]:m[v]+D,data:{[v]:L,centerOffset:I-L-D,...w&&{alignmentOffset:D}},reset:w}}});Qo="undefined"!=typeof document?Yt.useLayoutEffect:Yt.useEffect;const El=e=>({name:"arrow",options:e,fn(t){const{element:n,padding:r}="function"==typeof e?e(t):e;return n&&(i=n,{}.hasOwnProperty.call(i,"current"))?null!=n.current?pl({element:n.current,padding:r}).fn(t):{}:n?pl({element:n,padding:r}).fn(t):{};var i}}),gl=(e,t)=>({...vl(e),options:[e,t]}),hl=(e,t)=>({...fl(e),options:[e,t]}),_l=(e,t)=>({...El(e),options:[e,t]}),bl={...zt},yl=bl.useInsertionEffect||(e=>e());let Cl=0;Zo="undefined"!=typeof document?Yt.useLayoutEffect:Yt.useEffect;let Nl=!1,Al=0;const Tl=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+Al++,Ol=bl.useId||function(){const[e,t]=Yt.useState(()=>Nl?Tl():void 0);return Zo(()=>{null==e&&t(Tl())},[]),Yt.useEffect(()=>{Nl=!0},[]),e},Rl=Yt.forwardRef(function(e,t){const{context:{placement:n,elements:{floating:r},middlewareData:{arrow:i,shift:o}},width:a=14,height:u=7,tipRadius:l=0,strokeWidth:c=0,staticOffset:s,stroke:d,d:m,style:{transform:v,...f}={},...p}=e,E=Ol(),[g,h]=Yt.useState(!1);if(Zo(()=>{r&&"rtl"===xe(r).direction&&h(!0)},[r]),!r)return null;const[_,b]=n.split("-"),y="top"===_||"bottom"===_;let C=s;(y&&null!=o&&o.x||!y&&null!=o&&o.y)&&(C=null);const N=2*c,A=N/2,T=a/2*(l/-8+1),O=u/2*l/4,R=!!m,S=C&&"end"===b?"bottom":"top";let x=C&&"end"===b?"right":"left";C&&g&&(x="end"===b?"left":"right");const I=null!=(null==i?void 0:i.x)?C||i.x:"",L=null!=(null==i?void 0:i.y)?C||i.y:"",w=m||"M0,0 H"+a+" L"+(a-T)+","+(u-O)+" Q"+a/2+","+u+" "+T+","+(u-O)+" Z",D={top:R?"rotate(180deg)":"",left:R?"rotate(90deg)":"rotate(-90deg)",bottom:R?"":"rotate(180deg)",right:R?"rotate(-90deg)":"rotate(90deg)"}[_];return Yt.createElement("svg",Tt({},p,{"aria-hidden":!0,ref:t,width:R?a:a+N,height:a,viewBox:"0 0 "+a+" "+(u>a?u:a),style:{position:"absolute",pointerEvents:"none",[x]:I,[S]:L,[_]:y||R?"100%":"calc(100% - "+N/2+"px)",transform:[D,v].filter(e=>!!e).join(" "),...f}}),N>0&&Yt.createElement("path",{clipPath:"url(#"+E+")",fill:"none",stroke:d,strokeWidth:N+(m?0:1),d:w}),Yt.createElement("path",{stroke:N&&!m?p.fill:"none",d:w}),Yt.createElement("clipPath",{id:E},Yt.createElement("rect",{x:-A,y:A*(R?-1:1),width:a+N,height:a})))}),Sl=Yt.createContext(null),xl=Yt.createContext(null),Il=()=>{var e;return(null==(e=Yt.useContext(Sl))?void 0:e.id)||null},Ll=()=>Yt.useContext(xl),wl=Ot("safe-polygon");let Dl=new WeakMap,Ml=new WeakSet,Pl={},kl=0;const Bl=e=>e&&(e.host||Bl(e.parentNode)),Ul=()=>({getShadowRoot:!0,displayCheck:"function"==typeof ResizeObserver&&ResizeObserver.toString().includes("[native code]")?"full":"none"}),Fl={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"fixed",whiteSpace:"nowrap",width:"1px",top:0,left:0},jl=Yt.forwardRef(function(e,t){const[n,r]=Yt.useState();Zo(()=>(/apple/i.test(navigator.vendor)&&r("button"),document.addEventListener("keydown",Dt),()=>{document.removeEventListener("keydown",Dt)}),[]);const i={ref:t,tabIndex:0,role:n,"aria-hidden":!n||void 0,[Ot("focus-guard")]:"",style:Fl};return Yt.createElement("span",Tt({},e,i))}),Hl=Yt.createContext(null),Yl="data-floating-ui-focusable";let zl=[];const Vl=Yt.forwardRef(function(e,t){return Yt.createElement("button",Tt({},e,{type:"button",ref:t,tabIndex:-1,style:Fl}))}),Gl={pointerdown:"onPointerDown",mousedown:"onMouseDown",click:"onClick"},ql={pointerdown:"onPointerDownCapture",mousedown:"onMouseDownCapture",click:"onClickCapture"},Wl=e=>{var t,n;return{escapeKey:"boolean"==typeof e?e:null!=(t=null==e?void 0:e.escapeKey)&&t,outsidePress:"boolean"==typeof e?e:null==(n=null==e?void 0:e.outsidePress)||n}},Kl="active",Xl="selected",$l=new Map([["select","listbox"],["combobox","listbox"],["label",!1]]),Ql=(0,Yt.forwardRef)(function({options:e,name:t,onChange:n,id:r,...i},o){const{selectedOptionValue:a,selectRef:u,buttonRef:l}=tl(),c=Ct([o,u]);return Vt().createElement("select",{id:`hidden-${r}`,ref:c,name:t,value:a,className:"vrui-sr-only","aria-hidden":"true",tabIndex:-1,onChange:n,...i,onFocus:()=>{l.current?.focus()}},Vt().createElement("option",{value:""}),e.map(e=>Vt().createElement("option",{key:e.id,value:e.value})))}),Zl="vrui-text-left vrui-font-normal vrui-text-darkblue focus:vrui-text-darkblue vrui-text-14 vrui-leading-18 disabled:hover:vrui-bg-gray-5 hover:vrui-bg-gray-6 vrui-rounded-t-4  disabled:vrui-border-gray-3 vrui-h-44 vrui-w-full vrui-py-13 vrui-px-10 vrui-bg-gray-5 vrui-border-b focus:vrui-border-b-2 vrui-flex vrui-items-center vrui-justify-between focus-visible:vrui-outline-1 focus-visible:vrui-outline-blue focus-visible:vrui-outline-offset-2",Jl=({"aria-controls":e,id:t,"aria-labelledby":n,"aria-describedby":r,className:i,hasError:o,"aria-required":a,onBlur:u})=>{const{selectedOptionValue:l,buttonRef:c,options:s,isOpen:d,selectedOptionDisplayName:m,navigatorIndex:v,setIsNavigating:f,setSelectedOptionValue:p,setSelectedOptionDisplayName:E,setIsOpen:g,setNavigatorIndex:h,selectRef:_}=tl(),b=e=>{const t=document.getElementById(`${e}`);t&&t.scrollIntoView({behavior:"auto",block:"nearest"})},[y,C]=(0,Yt.useState)(""),N=(0,Yt.useRef)(null);return(0,Yt.useEffect)(()=>{if(y){const e=s.findIndex(e=>e.displayName.toString().toLowerCase().startsWith(y.toLowerCase()));e>-1&&(h(e),f(!0),b(s[e].id))}},[y]),Vt().createElement("button",{id:t,ref:c,type:"button",onClick:()=>{g(!d)},onKeyDown:e=>{let t;if("ArrowDown"===e.key||"ArrowUp"===e.key)if(e.preventDefault(),g(!0),f(!0),"ArrowDown"===e.key){t=null===v?0:v+1;const e=s.length-1,n=t>e;h(t>e?e:t),b(s[n?e:t].id)}else"ArrowUp"===e.key&&(t=null===v?0:v-1,h(t<0?0:t),b(s[t<0?0:t].id));else if("Enter"===e.key&&d&&null!==v)l!==s[v].value&&(p(s[v].value),E(s[v].displayName),setTimeout(()=>{if(_.current){const e=new Event("change",{bubbles:!0});_.current.dispatchEvent(e)}},0)),f(!1);else if("Escape"===e.key){g(!1),f(!1);const e=s.findIndex(e=>e.value===l);h(e)}},onBlur:e=>{g(!1),u&&u(e)},"aria-controls":e,"aria-haspopup":"listbox","aria-expanded":d,"aria-labelledby":n,"aria-describedby":r,"aria-activedescendant":d&&s[v]?s[v].id:void 0,role:"combobox",className:[Zl,i,o?"vrui-border-red focus:vrui-border-red":"vrui-border-darkblue focus:vrui-border-blue"].join(" ").trim(),"aria-required":a,onKeyUp:e=>{N.current&&clearTimeout(N.current),1===e.key.length&&C(t=>t+e.key),N.current=setTimeout(()=>{C("")},1e3)}},m,Vt().createElement(ma,{className:["vrui-text-12 vrui-ml-auto",o?"vrui-text-red":"vrui-text-blue"].join(" ").trim(),iconClass:"vi_vrui",iconName:d?"vi_arrow_dropdown_up_f":"vi_arrow_dropdown_f"}))},ec="vrui-bg-white vrui-px-10 vrui-py-8 vrui-text-14 vrui-leading-18 vrui-rounded-b-4 vrui-shadow-2sm vrui-w-full vrui-z-[1001] vrui-overflow-y-auto vrui-scrollbar-2",tc=({id:e,children:t,dropDownHeight:n})=>{const{isOpen:r}=tl(),{width:i}=ia();return Vt().createElement("div",{id:e,className:"vrui-absolute vrui-w-full vrui-top-[44px] vrui-z-[1001]"},Vt().createElement("ul",{role:"listbox",className:[ec,r?"vrui-absolute":"vrui-hidden"].join(" ").trim(),style:n?ua(i,n):void 0},t))},nc=(0,Yt.forwardRef)(function({defaultValue:e,placeHolder:t,children:n,"aria-labelledby":r,"aria-describedby":i,name:o,id:a,onChange:u,className:l,hasError:c,errorMessage:s,"aria-required":d,dropDownHeight:m,required:v,onBlur:f,...p},E){const{formGroupHasError:g,formGroupErrorMessage:h,inputErrorId:_}=(0,Yt.useContext)(tu)||{},b=void 0!==h?h:s,[y,C]=(0,Yt.useState)([]),[N,A]=(0,Yt.useState)(null),[T,O]=(0,Yt.useState)(e),[R,S]=(0,Yt.useState)(t),[x,I]=(0,Yt.useState)(!1),[L,w]=(0,Yt.useState)(!1),D=(0,Yt.useRef)(null),M=(0,Yt.useRef)(null),P=`dropdown-${(0,Yt.useId)()}`;(0,Yt.useEffect)(()=>{const t=y.findIndex(t=>t.value.toString()===e?.toString());e&&y.length>0&&t>-1&&(O(y[t].value),A(t),S(y[t].displayName))},[y,e]);const k=(0,Yt.useMemo)(()=>_?[i||"",_].join(" ").trim():[i||"",`${a}-error`].join(" ").trim(),[i,_,a]);return Vt().createElement(nl.Provider,{value:{selectedOptionValue:T,selectedOptionDisplayName:R,isOpen:x,buttonRef:D,selectRef:M,isNavigating:L,options:y,navigatorIndex:N,setSelectedOptionValue:O,setSelectedOptionDisplayName:S,setIsOpen:I,setIsNavigating:w,setNavigatorIndex:A,initializeOptions:e=>{C(t=>[...t,e])},buttonElementID:a}},Vt().createElement("div",{className:"vrui-relative"},Vt().createElement(Jl,{"aria-controls":P,id:a,"aria-labelledby":r,"aria-describedby":k||"",className:l,hasError:g||c,"aria-required":!(!v&&!d),onBlur:f}),Vt().createElement(tc,{id:P,dropDownHeight:m||void 0},n),Vt().createElement(Ql,{options:y,id:a,name:o,onChange:u,ref:E,...p}),void 0===g&&c&&b&&Vt().createElement(Fa,{id:_||`${a}-error`,iconClass:"vi_warning_c",iconName:"vi_vrui",errorMessage:b||""})))}),rc=nc,ic=({displayName:e,value:t,id:n,...r})=>{const{selectedOptionValue:i,buttonRef:o,selectRef:a,isNavigating:u,options:l,navigatorIndex:c,setSelectedOptionValue:s,setSelectedOptionDisplayName:d,setIsOpen:m,setNavigatorIndex:v,initializeOptions:f,buttonElementID:p}=tl();(0,Yt.useEffect)(()=>{l.findIndex(e=>e.value===t)<0&&f({value:t,id:`${p}-${n}`,displayName:e})},[]);const E=["vrui-px-8 vrui-py-6 hover:vrui-bg-gray-5 hover:vrui-border-l-2 hover:vrui-border-blue hover:vrui-pl-6 ",i===t&&!u||u&&l[null!==c&&c>=0?c:0].id===`${p}-${n}`?"vrui-bg-gray-5 vrui-border-l-2 vrui-border-blue vrui-pl-[6px]":""].join(" ").trim();return Vt().createElement("li",{value:t,id:`${p}-${n}`,onMouseDown:()=>{((e,t)=>{if(i!==e){s(e),d(t);const n=l.findIndex(t=>t.value===e);v(n),setTimeout(()=>{if(a.current){const e=new Event("change",{bubbles:!0});a.current.dispatchEvent(e)}},0)}m(!1),setTimeout(()=>{o.current?.focus()},1)})(t,e)},role:"option",className:E,...r},e)},oc=(0,Yt.createContext)({activeTabId:"",updateActiveTabID:()=>{},mode:"manual",isFocusableTabPanel:!1}),ac=({mode:e,children:t,isFocusableTabPanel:n,...r})=>{const[i,o]=(0,Yt.useState)("");return Vt().createElement(oc.Provider,{value:{activeTabId:i,updateActiveTabID:e=>{o(e)},mode:e,isFocusableTabPanel:n},...r},t)},uc=({className:e,"aria-labelledby":t,children:n,arrowButtonsLabel:r={previous:"Previous",next:"Next"}})=>{const[i,o]=(0,Yt.useState)(0),[a,u]=(0,Yt.useState)(!1),{width:l}=ia(),[c,s]=(0,Yt.useState)(l<768),d=(0,Yt.useRef)(null),m=(0,Yt.useCallback)(()=>{if(d.current){const e=d.current,t=e.scrollWidth>e.clientWidth,n=e.scrollLeft;o(n),u(t&&Math.ceil(n+e.clientWidth)<e.scrollWidth&&n!==e.scrollWidth-e.clientWidth)}},[d]);(0,Yt.useEffect)(()=>{s(l<768)},[l]),(0,Yt.useEffect)(()=>{const e=d.current;return e&&c&&(m(),e.addEventListener("scroll",m)),window.addEventListener("resize",m),()=>{e?.removeEventListener("scroll",m),window.removeEventListener("resize",m)}},[c,m]);const v=(0,Yt.useCallback)(e=>{if(d.current){const t=d.current,n=Math.ceil(t.scrollWidth),r=t.clientWidth,i=t.scrollLeft,o="left"===e?i:n-r-i,a=o<r?o:105;t.scrollBy({left:"left"===e?-a:a})}},[d]);return Vt().createElement("div",{className:"vrui-relative -vrui-mx-5"},c&&i>0?Vt().createElement("div",{className:"vrui-absolute vrui-top-0 -vrui-left-5 sm:vrui-left-0 vrui-z-10 vrui-bg-white vrui-px-5 sm:vrui-px-0"},Vt().createElement("button",{className:"focus-visible:vrui-outline-blue focus-visible:vrui-outline focus-visible:vrui-outline-2 focus-visible:vrui-outline-offset-3 focus-visible:vrui-rounded-full vrui-leading-none",onClick:()=>v("left"),"aria-label":r.previous,role:"presentation"},Vt().createElement(ma,{className:"vrui-text-32 vrui-text-blue",iconClass:"vi_vrui",iconName:"vi_arrow_left_c"}))):null,c&&a?Vt().createElement("div",{className:"vrui-absolute vrui-top-0 -vrui-right-5 sm:vrui-right-0 vrui-z-10 vrui-bg-white vrui-px-5 sm:vrui-px-0"},Vt().createElement("button",{className:"focus-visible:vrui-outline-blue focus-visible:vrui-outline focus-visible:vrui-outline-2 focus-visible:vrui-outline-offset-3 focus-visible:vrui-rounded-full vrui-leading-none",onClick:()=>v("right"),"aria-label":r.next,role:"presentation"},Vt().createElement(ma,{className:"vrui-text-32 vrui-text-blue",iconClass:"vi_vrui",iconName:"vi_arrow_right_c"}))):null,Vt().createElement("div",{ref:d,className:"vrui-tablist-overflow vrui-relative vrui-w-full vrui-overflow-x-auto vrui-scroll-smooth vrui-scrollbar-width-none vrui-whitespace-nowrap"},Vt().createElement("div",{className:e,role:"tablist","aria-labelledby":t},n)))},lc="vrui-inline-flex vrui-w-full hover:after:vrui-content-[''] hover:after:vrui-w-full hover:after:vrui-h-full hover:after:vrui-absolute hover:after:-vrui-bottom-0 hover:after:vrui-border-solid hover:after:vrui-border-b-4 hover:after:vrui-border-red focus-visible:vrui-outline-blue focus-visible:vrui-outline focus-visible:vrui-outline-2 focus-visible:vrui-outline-offset-3 focus-visible:vrui-rounded-6",cc=({id:e,active:t,activeByDefault:n,useDefaultFontStyle:r=!0,className:i,onClick:o,children:a,...u})=>{const{mode:l,activeTabId:c,updateActiveTabID:s}=(0,Yt.useContext)(oc);(0,Yt.useEffect)(()=>{n&&!c&&s(e)},[n,c,s,e]),(0,Yt.useEffect)(()=>{t&&s(e)},[t]);const d=c===e;return Vt().createElement("div",{className:"vrui-inline-flex vrui-relative vrui-mr-24 last:vrui-mr-5 vrui-pt-5 vrui-pb-8 vrui-shrink-0"},Vt().createElement("button",{id:e,className:[i,lc,d&&"active focus:after:vrui-content-[''] focus:after:vrui-w-full focus:after:vrui-h-full focus:after:vrui-absolute focus:after:-vrui-bottom-0 focus:after:vrui-border-solid focus:after:vrui-border-b-4 focus:after:vrui-border-red after:vrui-w-full after:vrui-h-full after:vrui-absolute after:-vrui-bottom-0 after:vrui-border-solid after:vrui-border-b-4 after:vrui-border-red",r,r&&"vrui-font-semibold vrui-text-16 vrui-line-height-22"].join(" "),onClick:t=>{s(e),o&&o(t)},onKeyDown:e=>{if("ArrowRight"===e.key||"ArrowLeft"===e.key){e.preventDefault();const t=e.currentTarget.parentElement;if(!t)return;const n="ArrowRight"===e.key?t.nextElementSibling:t.previousElementSibling;if(!n)return;const r=Array.from(n.children).filter(e=>"BUTTON"===e.tagName),i=r.find(e=>-1===e.tabIndex);i&&(i.focus(),"automatic"===l&&i.click())}},role:"tab","aria-selected":d?"true":"false",tabIndex:d?0:-1,...u},a))},sc=({id:e,tabId:t,children:n,"aria-labelledby":r,activeByDefault:i})=>{const{activeTabId:o,updateActiveTabID:a,isFocusableTabPanel:u}=(0,Yt.useContext)(oc);(0,Yt.useEffect)(()=>{i&&!o&&a(t)},[i,o,a,t]);const l=o===t,c=u?0:void 0;return Vt().createElement("div",{id:e,role:"tabpanel",className:"focus-visible:vrui-outline-blue focus-visible:vrui-outline focus-visible:vrui-outline-2 focus-visible:vrui-outline-offset-3 focus-visible:vrui-rounded-6 "+(l?"vrui-block":"vrui-hidden"),"aria-labelledby":r,"data-tabid":t,tabIndex:c},n)},dc=({title:e,subtitle:t,status:n,hideSubtitle:r,children:i,className:o,editButton:a,variant:u="default",disableSrOnlyText:l,autoScrollActiveStep:c=!0,...s})=>{const d="active"===n?{containerStyles:{default:"vrui-flex vrui-flex-col vrui-pt-32 vrui-pb-48",leftAlign:"vrui-flex vrui-flex-col vrui-py-32",leftAlignNoStep:"vrui-flex vrui-flex-col sm:vrui-pt-44 vrui-pt-30"},headingTitle:{default:["vrui-text-center vrui-text-darkblue",o].join(" ").trim(),leftAlign:"",leftAlignNoStep:""},headingSubtitle:{default:"vrui-text-14 vrui-text-red",leftAlign:"vrui-text-14 vrui-text-red",leftAlignNoStep:"vrui-text-14 vrui-text-red"},headingContainer:{default:"vrui-flex vrui-justify-center",leftAlign:"vrui-flex vrui-justify-start",leftAlignNoStep:"vrui-flex vrui-justify-start"}}:"inactive"===n?{containerStyles:{default:"vrui-flex vrui-flex-col vrui-pt-32 vrui-pb-48",leftAlign:"vrui-flex vrui-flex-col vrui-py-32",leftAlignNoStep:"vrui-flex vrui-flex-col vrui-py-40",leftAlignHeading:"vrui-flex vrui-flex-col"},headingTitle:{default:["vrui-text-center vrui-text-darkblue",o].join(" ").trim(),leftAlign:"vrui-text-darkblue !vrui-text-22 !vrui-leading-24 sm:!vrui-text-24 sm:!vrui-leading-26",leftAlignNoStep:"vrui-text-gray-10 sm:vrui-text-32 sm:vrui-text-26",leftAlignHeading:"vrui-text-gray-10 sm:vrui-text-24 sm:vrui-text-24 md:vrui-text-24 lg:vrui-text-24"},headingSubtitle:{default:"vrui-text-14 vrui-text-darkblue",leftAlign:"vrui-text-14 vrui-text-darkblue",leftAlignNoStep:"vrui-text-14 vrui-text-darkblue",leftAlignHeading:"vrui-text-14 vrui-text-darkblue"},headingContainer:{default:"vrui-flex vrui-justify-center",leftAlign:"",leftAlignNoStep:""}}:"complete"===n?{containerStyles:{default:"vrui-flex vrui-flex-col vrui-pt-32 vrui-pb-48",leftAlign:"vrui-flex vrui-flex-col vrui-py-32",leftAlignNoStep:"vrui-flex vrui-flex-col vrui-pt-44"},headingTitle:{default:["vrui-text-darkblue !vrui-text-20 !vrui-leading-22 md:!vrui-text-22 md:!vrui-leading-28",o].join(" ").trim(),leftAlign:"vrui-text-darkblue !vrui-text-22 !vrui-leading-24 sm:!vrui-text-24 sm:!vrui-leading-26",leftAlignNoStep:["vrui-text-gray-10 sm:!vrui-text-24 !vrui-text-26 !vrui-leading-28 sm:!vrui-leading-26 !vrui-tracking-[-0.4px]",o].join(" ").trim()},headingSubtitle:{default:"vrui-text-14 vrui-text-darkblue",leftAlign:"vrui-text-14 vrui-text-darkblue",leftAlignNoStep:"vrui-text-14 vrui-text-darkblue"},headingContainer:{default:"vrui-flex vrui-justify-between vrui-gap-x-10 md:vrui-mx-80",leftAlign:"vrui-flex vrui-justify-between vrui-gap-x-10",leftAlignNoStep:"vrui-flex vrui-justify-between vrui-gap-x-10"}}:{containerStyles:{default:"",leftAlign:"",leftAlignNoStep:"",leftAlignHeading:""},headingTitle:{default:"",leftAlign:"",leftAlignNoStep:"",leftAlignHeading:""},headingSubtitle:{default:"",leftAlign:"",leftAlignNoStep:"",leftAlignHeading:""},headingContainer:{default:"",leftAlign:"",leftAlignNoStep:"",leftAlignHeading:""}},m=(0,Yt.useRef)(null);return(0,Yt.useEffect)(()=>{"active"===n&&c&&m.current?.scrollIntoView({behavior:"smooth"})},[n]),Vt().createElement("div",{ref:m,className:d.containerStyles[u]},Vt().createElement("div",{className:d.headingContainer[u]},Vt().createElement(cu,{level:"h2",variant:"lg",className:d.headingTitle[u],...s},Vt().createElement("span",{className:"vrui-flex vrui-flex-col"},r?null:Vt().createElement(Ba,{elementType:"span",className:d.headingSubtitle[u]},t," "),e,l?null:Vt().createElement(ha,null," ",{active:"(Current Step)",inactive:"(Disabled: Please click the next step button above to proceed)",complete:"(Complete Step)"}[n]))),"complete"===n?a:null),"active"===n||"complete"===n?i:null)},mc={topRight:"vrui-right-24 vrui-top-24",topLeft:"vrui-top-16 vrui-left-16 sm:vrui-top-32 sm:vrui-left-32",default:"vrui-right-24 vrui-top-24"},vc=(0,Yt.forwardRef)(function({checkboxPlacement:e="default",checkboxPlacementClassName:t,borderRadiusClassName:n,disabled:r,checked:i,defaultChecked:o,...a},u){return Vt().createElement("div",{className:"vrui-group/inputcheckbox vrui-absolute vrui-right-0 vrui-top-0 rui-leading-0 vrui-w-full vrui-h-full"},Vt().createElement("div",{className:["vrui-absolute vrui-w-full vrui-h-full group-has-[:checked]/inputcheckbox:vrui-border-2 group-has-[:focus-visible]/inputcheckbox:vrui-outline-blue group-has-[:focus-visible]/inputcheckbox:vrui-outline group-has-[:focus-visible]/inputcheckbox:vrui-outline-2 group-has-[:focus-visible]/inputcheckbox:vrui-outline-offset-3 transition-all",n||"vrui-rounded-16",r&&(i||o)?"group-has-[:checked]/inputcheckbox:vrui-border-gray-10":"group-has-[:checked]/inputcheckbox:vrui-border-blue"].join(" ").trim()}),Vt().createElement("input",{type:"checkbox",ref:u,disabled:r,checked:i,defaultChecked:o,...a,className:"vrui-absolute vrui-left-0 vrui-top-0 vrui-w-full vrui-h-full vrui-z-10 vrui-opacity-0"}),Vt().createElement("div",{className:["vrui-absolute",t||mc[e]].join(" ")},Vt().createElement("div",{className:["vrui-w-24 vrui-h-24 vrui-rounded-4 vrui-border  ",r&&"vrui-opacity-60",r&&(i||o)?"vrui-border-gray-10":"vrui-border-gray-2 group-has-[:checked]/inputcheckbox:vrui-bg-blue"].join(" ").trim()}),Vt().createElement(ma,{className:["group-has-[:checked]/inputcheckbox:vrui-block vrui-absolute vrui-hidden vrui-text-12 vrui-transform vrui-top-1/2 vrui-left-1/2 -vrui-translate-x-1/2 -vrui-translate-y-1/2",r&&"vrui-opacity-60",r&&(i||o)?"vrui-text-gray-9":"vrui-text-white"].join(" ").trim(),iconClass:"vi_vrui",iconName:"vi_checked"})))}),fc={topRight:"vrui-py-32 vrui-px-24",topLeft:"vrui-p-16 sm:vrui-p-32",default:"vrui-py-32 vrui-px-24"},pc=(0,Yt.forwardRef)(function({children:e,className:t,"aria-labelledby":n,"aria-describedby":r,name:i,checkboxPlacement:o="topRight",checkboxPlacementClassName:a,borderRadiusClassName:u,defaultPadding:l=!0,disabled:c,checked:s,defaultChecked:d,...m},v){return Vt().createElement("div",{className:["vrui-group/checkboxcard vrui-border vrui-rounded-16 vrui-relative",l?fc[o]:"",u||"vrui-rounded-16",,t,c&&(s||d)?"vrui-bg-gray-5":"",c&&"vrui-bg-gray-5 vrui-border-gray-3"].join(" ").trim()},Vt().createElement(vc,{"aria-labelledby":n,"aria-describedby":r,name:i,ref:v,checkboxPlacement:o,checkboxPlacementClassName:a,borderRadiusClassName:u,disabled:c,checked:s,defaultChecked:d,...m}),e)}),Ec=pc,gc=(0,Yt.forwardRef)(function({"aria-labelledby":e,"aria-describedby":t,children:n,defaultPadding:r,...i},o){return Vt().createElement(Ec,{"aria-labelledby":e,"aria-describedby":t,ref:o,defaultPadding:r,...i},n)}),hc=gc,_c=({id:e,children:t,"aria-labelledby":n,"aria-label":r,arrowButtonsLabel:i,dotsLabel:o,slideDescription:a="slide",slideLabel:u="%s of %s",rootDescription:l="carousel",firstFocus:c="pagination"})=>{const s=(0,Yt.useRef)(null),[d,m]=(0,Yt.useState)({left:"16px",right:"32px"}),{width:v}=ia(100),[f,p]=(0,Yt.useState)(!1),E=(0,Yt.useRef)(null),g=(0,Yt.useCallback)(e=>{const t=(t,n)=>{if(!t?.classList.contains("is-visible")){e.preventDefault(),s.current?.go(n);const r=t?.querySelector("input");setTimeout(()=>{r?.click(),r?.focus()},50)}};"ArrowLeft"===e.key||"ArrowUp"===e.key?t(e.currentTarget.previousElementSibling,"<"):"ArrowDown"!==e.key&&"ArrowRight"!==e.key||t(e.currentTarget.nextElementSibling,">")},[s]),h=()=>{const t=document.querySelectorAll(`#${e}-parent .splide__pagination__page`);o&&E&&Ta(o,t)};(0,Yt.useLayoutEffect)(()=>{const{splide:e}=s.current||{};return e&&f&&(e.on("pagination:mounted",h),h()),()=>{e&&e.off("pagination:mounted")}},[f]);const _=()=>{const e=s.current?.splide?.index,t=s.current?.splide?.length||0;window.innerWidth<768?m(0===e?{left:"16px",right:"32px"}:e===t-1?{left:"32px",right:"16px"}:{left:"24px",right:"24px"}):window.innerWidth>=768&&window.innerWidth<992&&m(0===e?{left:"32px",right:"42px"}:e===t-2?{left:"42px",right:"32px"}:{left:"32px",right:"32px"})};(0,Yt.useEffect)(()=>{const{splide:e}=s.current||{};e&&(e.on("move",_),_(),p(e?.Components.Pagination.items.length>1))},[v]);const b=(0,Yt.useMemo)(()=>v<768?{marginRight:"-16px",marginLeft:"-16px"}:v>=768&&v<992?{marginRight:"-32px",marginLeft:"-32px"}:void 0,[v]),y=(0,Yt.useMemo)(()=>{const e=Vt().Children.map(t,(e,t)=>Vt().isValidElement(e)?Vt().createElement(oi,{key:t,"data-id":t,onKeyDown:g},Vt().cloneElement(e)):e);return e},[t]);return Vt().createElement(ii,{options:{mediaQuery:"min",destroy:!1,gap:16,pagination:!0,drag:!0,live:!1,focusableNodes:"input",padding:d,classes:{arrows:"splide__arrows vrui-hidden",arrow:"splide__arrow vrui-hidden",prev:"splide__arrow--prev",next:"splide__arrow--next",pagination:"splide__pagination",page:"splide__pagination__page !vrui-bg-white"},breakpoints:{768:{perPage:2,perMove:2,pagination:!0},992:{destroy:!0}},i18n:{prev:i&&i.previous?i.previous:"Previous slide",next:i&&i.next?i.next:"Next slide",slide:a,slideLabel:u,carousel:l}},ref:s,hasTrack:!1,"aria-label":r,"aria-labelledby":n,id:`${e}-parent`,onPaginationMounted:()=>{h()}},"pagination"===c&&Vt().createElement("ul",{id:`${e}-pagination`,ref:E,className:"splide__pagination !-vrui-bottom-16 -vrui-translate-y-16"}),Vt().createElement(ri,{className:"md:*:!vrui-grid md:*:vrui-grid-cols-3 md:*:vrui-gap-24 -vrui-mt-10 vrui-pt-10 vrui-px-5 "+(f?"vrui-pb-28":"vrui-pb-4 -vrui-mb-4"),style:{...b}},y),"slide"===c&&Vt().createElement("ul",{id:`${e}-pagination`,ref:E,className:"splide__pagination !-vrui-bottom-16 -vrui-translate-y-16"}))},bc=({id:e,children:t,"aria-labelledby":n,"aria-label":r,arrowButtonsLabel:i,slideDescription:o="slide",slideLabel:a="%s of %s",rootDescription:u="carousel",dotsLabel:l,listRole:c,focusActive:s,goToSlide:d})=>{const m=(0,Yt.useRef)(null),[v,f]=(0,Yt.useState)({left:"16px",right:"32px"}),[p,E]=(0,Yt.useState)(!1),{width:g}=ia(100),h=(0,Yt.useRef)(null),_=(0,Yt.useCallback)(e=>{const t=(t,n)=>{if(!t?.classList.contains("is-visible")){e.preventDefault(),m.current?.go(n);const r=t?.querySelector("input");setTimeout(()=>{r?.click(),r?.focus()},50)}};"ArrowLeft"===e.key||"ArrowUp"===e.key?t(e.currentTarget.previousElementSibling,"<"):"ArrowDown"!==e.key&&"ArrowRight"!==e.key||t(e.currentTarget.nextElementSibling,">")},[m]),b=()=>{const e=m.current?.splide?.index,t=m.current?.splide?.length||0;window.innerWidth<768?f(0===e?{left:"26px",right:"18px"}:e===t-1?{left:"18px",right:"26px"}:{left:"22px",right:"22px"}):window.innerWidth>=768&&window.innerWidth<992?f({left:"24px",right:"45px"}):window.innerWidth>=992&&f({left:"24px",right:"16px"})};(0,Yt.useEffect)(()=>{const{splide:e}=m.current||{};e&&(e.on("move",b),b())},[g]),(0,Yt.useEffect)(()=>{const{splide:t}=m.current||{};t&&e&&(t.on("drag move",function(){E(!0);const t=document.querySelectorAll(`#${e} .splide__slide:not(.is-visible)`);for(let e=0;e<t.length;e++)t.item(e).classList.add("!vrui-visible")}),t.on("dragged moved",function(){E(!1);const t=document.querySelectorAll(`#${e} .splide__slide:not(.is-visible)`);for(let e=0;e<t.length;e++)t.item(e).classList.remove("!vrui-visible")}))},[e]);const y=(0,Yt.useMemo)(()=>g<768?{marginRight:"-16px",marginLeft:"-16px"}:g>=768&&g<992?{marginRight:"-24px",marginLeft:"-24px"}:g>=992?{marginRight:"-16px",marginLeft:"-16px"}:void 0,[g]);(0,Yt.useEffect)(()=>{m.current&&d&&setTimeout(()=>{m.current?.go(d)},100)},[d]);const C=(0,Yt.useMemo)(()=>{const e=Vt().Children.map(t,(e,t)=>Vt().isValidElement(e)?Vt().createElement("div",{key:t,"data-id":t,onKeyDown:_,className:"splide__slide"},Vt().cloneElement(e)):e);return e},[t]);return Vt().createElement(ii,{options:{mediaQuery:"min",destroy:!1,pagination:!0,drag:!0,live:!1,focusableNodes:"button, input",padding:v,paginationKeyboard:!1,omitEnd:!0,classes:{arrows:"splide__arrows",arrow:"splide__arrow",prev:"splide__arrow--prev !vrui-h-40 !vrui-w-40 !vrui-bg-white !vrui-border-solid !vrui-border-2 !vrui-border-blue !vrui-text-blue !vrui-border-rounded-60 hover:!vrui-border-blue-1 !-vrui-left-10 focus:!vrui-outline-blue focus:!vrui-outline focus:!vrui-outline-2 focus:!vrui-outline-offset-3 !vrui-opacity-100",next:"splide__arrow--next !vrui-h-40 !vrui-w-40 !vrui-bg-white !vrui-border-solid !vrui-border-2 !vrui-border-blue !vrui-text-blue !vrui-border-rounded-60 hover:!vrui-border-blue-1 !-vrui-right-10 focus:!vrui-outline-blue focus:!vrui-outline focus:!vrui-outline-2 focus:!vrui-outline-offset-3 !vrui-opacity-100",pagination:"splide__pagination",page:"splide__pagination__page !vrui-bg-white !vrui-block"},breakpoints:{320:{focus:s,perPage:1,perMove:1,arrows:!1,gap:12},768:{focus:s,gap:24},992:{focus:s,perPage:2,perMove:1,arrows:!0,gap:24}},i18n:{prev:i&&i.previous?i.previous:"Previous slide",next:i&&i.next?i.next:"Next slide",slide:o,slideLabel:a,carousel:u}},ref:m,hasTrack:!1,"aria-label":r,"aria-labelledby":n,id:`${e}-parent`,onPaginationMounted:()=>{(()=>{const t=document.querySelectorAll(`#${e}-parent .splide__pagination__page`);l&&Ta(l,t),t&&t.forEach(e=>{e.removeAttribute("role"),e.removeAttribute("tabindex"),e.removeAttribute("aria-controls"),e.removeAttribute("aria-selected"),e.classList.contains("is-active")&&e.setAttribute("aria-current","true")}),h&&h.current&&(h.current.removeAttribute("aria-label"),h.current.removeAttribute("role"),h.current.setAttribute("role","group"))})()},onMove:()=>{(()=>{const e=h.current?.querySelectorAll(".splide__pagination__page");setTimeout(()=>{e&&e.forEach(e=>{e.removeAttribute("tabindex"),e.removeAttribute("aria-selected"),e.removeAttribute("aria-current"),e.classList.contains("is-active")&&e.setAttribute("aria-current","true")},1)})})()},onMounted:e=>{(e=>{const t=e.root.querySelectorAll(".splide__slide");t&&t.forEach(e=>{void 0!==c&&""!==c&&e.setAttribute("role",c)})})(e)},className:"vrui-h-full"},Vt().createElement("div",{id:e,className:`-vrui-mt-10 vrui-pt-10 vrui-pb-[18px] partial-carousel vrui-h-full splide__track ${p&&"is-moving"}`,style:{...y}},Vt().createElement("div",{className:"splide__list"},C)),Vt().createElement("div",{className:"splide__arrows"}),Vt().createElement("ul",{id:`${e}-pagination`,ref:h,className:"splide__pagination !-vrui-bottom-[22px]"}))},yc=({children:e,className:t,...n})=>Vt().createElement("div",{className:["vrui-mb-24",t].join(" ").trim(),...n},e),Cc=({children:e,className:t,...n})=>Vt().createElement("div",{className:["vrui-mt-auto",t].join(" ").trim(),...n},e),Nc={defaultLabel:"disabled:vrui-text-gray-2 vrui-font-semibold vrui-text-sm vrui-leading-19"},Ac=({children:e,required:t,className:n,isError:r,htmlFor:i,overrideClassNames:o,...a})=>{const u=()=>r?"vrui-text-red-2":"vrui-text-darkblue",l=Vt().createElement("span",{className:["vrui-mr-8",u()].join(" ").trim(),"aria-hidden":"true"},"*");return Vt().createElement(Vt().Fragment,null,Vt().createElement("label",{htmlFor:i,className:o||[Nc.defaultLabel,u(),n].join(" ").trim(),...a},t&&l,e))},Tc={default:"vrui-flex-col"},Oc=({variant:e="default",className:t,children:n,...r})=>Vt().createElement("div",{className:[Tc[e],"vrui-flex vrui-box-border vrui-w-full",t].join(" ").trim(),...r},n),Rc={textBlue:"vrui-font-poppins-Semibold vrui-inline-block vrui-rounded-4 vrui-bg-transparent vrui-text-blue vrui-underline vrui-underline-offset-4 enabled:hover:vrui-text-blue-1 enabled:hover:vrui-no-underline focus:vrui-text-blue-1 focus:vrui-no-underline focus:vrui-outline-blue focus:vrui-outline focus:vrui-outline-2 focus:vrui-outline-offset-3 disabled:vrui-opacity-40",default:""},Sc={regular:"vrui-text-16 vrui-leading-18",small:"vrui-text-14 vrui-leading-18",default:""},xc=({icon:e,text:t,className:n,position:r,variant:i="textBlue",size:o,...a})=>{const u=(0,Yt.useMemo)(()=>"right"===r?Vt().createElement(Vt().Fragment,null,Vt().createElement(Ba,{elementType:"span",className:"group-hover:vrui-underline vrui-mr-8"},t),e):Vt().createElement(Vt().Fragment,null,e,Vt().createElement(Ba,{elementType:"span",className:"group-hover:vrui-underline vrui-ml-8"},t)),[r,e,t]);return Vt().createElement("button",{className:[Sc[o],Rc[i],"vrui-group vrui-no-underline",n,"right"===r?"vrui-inline-block":"vrui-inline-flex vrui-items-center"].join(" ").trim(),...a},u)},Ic={default:"vrui-bg-gray-5"},Lc={defaultInput:"vrui-font-poppins-Regular vrui-font-normal vrui-text-darkblue vrui-placeholder-text-gray-2 focus:vrui-text-darkblue vrui-text-16 sm:vrui-text-14 vrui-leading-22 sm-:vrui-leading-19 disabled:hover:vrui-bg-gray-5 hover:vrui-bg-gray-6 vrui-rounded-t vrui-border-solid vrui-box-border disabled:vrui-border-gray-3 disabled:vrui-text-gray-2 focus:vrui-border-blue focus:vrui-outline-1 focus:vrui-outline-blue focus:vrui-outline-offset-2 vrui-h-44 vrui-w-full vrui-py-5 vrui-px-10"},wc=(0,Yt.forwardRef)(function({id:e,variant:t="default",errorMessage:n,className:r,"aria-describedby":i,iconClass:o="vi_warning_c",iconName:a="vi_vrui",isError:u,errorMessageClassName:l="",...c},s){const d=n&&u?`error-${e}`:"",m=(0,Yt.useMemo)(()=>i?[i||"",d].join(" ").trim():d,[i,d]);return Vt().createElement("div",{className:"vrui-flex vrui-flex-col"},Vt().createElement("div",{className:"vrui-flex vrui-items-center"},Vt().createElement("input",{className:[Lc.defaultInput,Ic[t],u?"invalid:vrui-border-red-2 vrui-border-red-2 vrui-border-b-2":"vrui-border-b vrui-border-darkblue focus:vrui-border-b-2",r].join(" ").trim(),type:"text","aria-describedby":m||"",ref:s,id:e,...c})),u&&n&&Vt().createElement(Fa,{id:d,iconClass:o,iconName:a,errorMessage:n||"",errorMessageClassName:l}))});!function(e){e.ENTER="Enter",e.ESCAPE="Escape",e.SPACE="Space",e.TAB="Tab"}(Jo||(Jo={}));const Dc=(0,Yt.createContext)({variant:"default",ariaLabelledby:""}),Mc=({isNestedModal:e=!1,variant:t="default","aria-labelledby":n,"aria-describedby":r,onOverlayClick:i,onEscapeKeyPressed:o,children:a,className:u,focusLockOptions:l={returnFocus:!0,lockProps:{tabIndex:-1}},hasBackdrop:c=!0,modalWidth:s={mobile:"100%",tablet:"645px",desktop:"645px"},...d})=>{const m=n||((e=5)=>{let t="";for(let n=0;n<e;n++){const e=Math.floor(62*Math.random());t+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".charAt(e)}return t})(),{width:v}=ia(100),f=(0,Yt.useRef)(null);oa(f,e),((e,t="keydown")=>{(0,Yt.useEffect)(()=>(document.addEventListener(t,e),()=>{document.removeEventListener(t,e)}),[])})(e=>{e.stopPropagation(),e.key===Jo.ESCAPE&&o?.(e)});const p=(0,Yt.useMemo)(()=>v<768?s.mobile:v>=768&&v<992?s.tablet:s.desktop,[v,s]);return Vt().createElement(Dc.Provider,{value:{variant:t,ariaLabelledby:m,ariaDescribedBy:r}},Vt().createElement(Cu,null,Vt().createElement(_u,{as:"div",...l||{}},Vt().createElement("div",{className:["vrui-bg-black-1 vrui-transition-opacity vrui-overflow-y-auto","vrui-h-dvh vrui-w-screen","vrui-flex vrui-items-start","vrui-fixed vrui-inset-0 vrui-m-auto vrui-z-[9998]",c?"vrui-bg-opacity-50":"vrui-bg-opacity-0",u||"vrui-justify-center"].join(" ").trim(),onClick:i,role:"dialog","aria-modal":!0,"aria-labelledby":m,tabIndex:-1,...d},Vt().createElement("div",{className:["vrui-relative vrui-z-10","vrui-w-full","vrui-flex"].join(" ").trim(),ref:f,style:{maxWidth:p}},a)))))},Pc={default:"vrui-modal-body vrui-flex vrui-flex-col vrui-items-start vrui-bg-white vrui-gap-16"},kc=({isDefaultPadding:e=!0,className:t,children:n,...r})=>{const{variant:i}=(0,Yt.useContext)(Dc),o=(0,Yt.useMemo)(()=>({modalBodyStyle:[Pc[i],e&&"vrui-p-24 sm:vrui-p-32",t].join(" ").trim()}),[i]);return Vt().createElement("div",{className:o.modalBodyStyle,...r},n)},Bc={default:["vrui-min-w-full vrui-inline-block vrui-bg-white vrui-text-left vrui-overflow-hidden vrui-shadow-xl vrui-transform vrui-transition-all","vrui-flex vrui-flex-col","vrui-relative vrui-opacity-0 sm:vrui-align-middle"].join(" ").trim()},Uc={top:"vrui-self-start",bottom:"vrui-self-end",center:"vrui-self-center",left:"",right:""},Fc={top:"vrui-rounded-b-24 -vrui-translate-y-[50px]",bottom:"vrui-rounded-t-24 vrui-translate-y-[50px]",center:"vrui-rounded-24 -vrui-translate-y-[50px]",left:"vrui-rounded-r-16 vrui-h-full",right:"vrui-rounded-l-16 vrui-h-full"},jc=({className:e,children:t,verticalAlignment:n={mobile:"bottom",tablet:"center",desktop:"center"},useDefaultRadius:r=!0})=>{const{variant:i}=(0,Yt.useContext)(Dc),{width:o}=ia(100),{verticalAlignmentBreakpoint:a,radiusBreakpoint:u}=((e,t,n,r)=>{const i=(0,Yt.useMemo)(()=>{let i,o;return e<768?(i=t[r.mobile],o=n[r.mobile]):e>=768&&e<992?(i=t[r.tablet],o=n[r.tablet]):(i=t[r.desktop],o=n[r.desktop]),{verticalAlignmentBreakpoint:i,radiusBreakpoint:o}},[e,t,n,r]);return i})(o,Uc,Fc,n),[l,c]=(0,Yt.useState)(!1),s=(0,Yt.useMemo)(()=>({modalContentStyle:[Bc[i],l?"vrui-transition-all vrui-duration-300 vrui-ease-out vrui-delay-50 vrui-opacity-100 vrui-transform-none":"",a,r?u:"",e].join(" ").trim()}),[i,e,l]);return(0,Yt.useEffect)(()=>{c(!0)},[]),Vt().createElement("div",{className:"vrui-flex vrui-justify-center vrui-w-full "+("center"===n.mobile?"vrui-min-h-dvh vrui-px-16":"vrui-min-h-dvh")},Vt().createElement("div",{onClick:e=>e.stopPropagation(),className:s.modalContentStyle},t))},Hc={variant:{gray:"vrui-bg-gray-5",transparent:""}},Yc=({variant:e="gray",children:t,className:n,isDefaultPadding:r=!0,...i})=>Vt().createElement("div",{className:["vrui-flex sm:vrui-gap-32","vrui-justify-center sm:vrui-justify-start vrui-flex-col sm:vrui-flex-row",r&&"vrui-ps-24 vrui-pe-16 vrui-py-16 vrui-gap-16 sm:vrui-px-32 sm:vrui-py-24",Hc.variant[e],n].join(" ").trim(),...i},t),zc={variant:{grayBar:"vrui-bg-gray-5",transparent:"",none:""}},Vc=({variant:e="transparent",headerIcon:t,title:n,rightButtonIcon:r,onRightButtonClicked:i,rightButtonLabel:o="Close Modal",className:a,children:u,isDefaultPadding:l=!0})=>{const{ariaLabelledby:c}=(0,Yt.useContext)(Dc);return Vt().createElement("div",{className:["vrui-modal-header vrui-flex vrui-flex-col",zc.variant[e],l&&"vrui-py-16 vrui-pl-24 vrui-pr-16 sm:vrui-py-24 sm:vrui-px-32",a].join(" ").trim()},Vt().createElement("div",{className:"vrui-flex vrui-justify-between vrui-gap-16"},Vt().createElement("div",{className:["vrui-flex vrui-items-start  sm:vrui-items-center vrui-justify-center sm:vrui-justify-start vrui-gap-20 vrui-flex-col sm:vrui-flex-row"].join(" ").trim()},t||null,Vt().createElement(cu,{className:"vrui-text-20 vrui-leading-22 sm:vrui-text-22 sm:vrui-leading-28 vrui-text-darkblue",id:c||"modal=dialog-title",level:"h2",variant:"default"},n)),r?Vt().createElement("div",{className:"vrui-pl-14"},Vt().createElement("button",{type:"button","aria-label":o,onClick:()=>i?.(),className:"vrui-flex vrui-rounded-2 focus:vrui-outline-blue focus:vrui-outline focus:vrui-outline-2 focus:vrui-outline-offset-3",id:`${c}-close-button`||"modal-dialog-close-button"},"default"===r?Vt().createElement(ma,{className:"vrui-text-24 vrui-text-darkblue",iconClass:la.vi_close.icon,iconName:la.vi_close.name}):null,Vt().createElement(ha,null,o))):null),u)},Gc=(0,Yt.createContext)(null),qc=({placement:e="bottom",children:t})=>{const n=function(e){const[t,n]=(0,Yt.useState)(!1),[r,i]=(0,Yt.useState)(!1),o=(0,Yt.useRef)(null),{width:a}=ia(),[u,l]=(0,Yt.useState)(a<768),c=function(e){void 0===e&&(e={});const{nodeId:t}=e,n=function(e){const{open:t=!1,onOpenChange:n,elements:r}=e,i=Ol(),o=Yt.useRef({}),[a]=Yt.useState(()=>function(){const e=new Map;return{emit(t,n){var r;null==(r=e.get(t))||r.forEach(e=>e(n))},on(t,n){e.set(t,[...e.get(t)||[],n])},off(t,n){var r;e.set(t,(null==(r=e.get(t))?void 0:r.filter(e=>e!==n))||[])}}}()),u=null!=Il(),[l,c]=Yt.useState(r.reference),s=Nt((e,t,r)=>{o.current.openEvent=e?t:void 0,a.emit("openchange",{open:e,event:t,reason:r,nested:u}),null==n||n(e,t,r)}),d=Yt.useMemo(()=>({setPositionReference:c}),[]),m=Yt.useMemo(()=>({reference:l||r.reference||null,floating:r.floating||null,domReference:r.reference}),[l,r.reference,r.floating]);return Yt.useMemo(()=>({dataRef:o,open:t,onOpenChange:s,elements:m,events:a,floatingId:i,refs:d}),[t,s,m,a,i,d])}({...e,elements:{reference:null,floating:null,...e.elements}}),r=e.rootContext||n,i=r.elements,[o,a]=Yt.useState(null),[u,l]=Yt.useState(null),c=(null==i?void 0:i.reference)||o,s=Yt.useRef(null),d=Ll();Zo(()=>{c&&(s.current=c)},[c]);const m=function(e){void 0===e&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:i,elements:{reference:o,floating:a}={},transform:u=!0,whileElementsMounted:l,open:c}=e,[s,d]=Yt.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[m,v]=Yt.useState(r);ht(m,r)||v(r);const[f,p]=Yt.useState(null),[E,g]=Yt.useState(null),h=Yt.useCallback(e=>{e!==C.current&&(C.current=e,p(e))},[]),_=Yt.useCallback(e=>{e!==N.current&&(N.current=e,g(e))},[]),b=o||f,y=a||E,C=Yt.useRef(null),N=Yt.useRef(null),A=Yt.useRef(s),T=null!=l,O=yt(l),R=yt(i),S=yt(c),x=Yt.useCallback(()=>{if(!C.current||!N.current)return;const e={placement:t,strategy:n,middleware:m};R.current&&(e.platform=R.current),((e,t,n)=>{const r=new Map,i={platform:dl,...n},o={...i.platform,_c:r};return(async(e,t,n)=>{const{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:a}=n,u=o.filter(Boolean),l=await(null==a.isRTL?void 0:a.isRTL(t));let c=await a.getElementRects({reference:e,floating:t,strategy:i}),{x:s,y:d}=rt(c,r,l),m=r,v={},f=0;for(let p=0;p<u.length;p++){const{name:n,fn:o}=u[p],{x:E,y:g,data:h,reset:_}=await o({x:s,y:d,initialPlacement:r,placement:m,strategy:i,middlewareData:v,rects:c,platform:a,elements:{reference:e,floating:t}});s=null!=E?E:s,d=null!=g?g:d,v={...v,[n]:{...v[n],...h}},_&&f<=50&&(f++,"object"==typeof _&&(_.placement&&(m=_.placement),_.rects&&(c=!0===_.rects?await a.getElementRects({reference:e,floating:t,strategy:i}):_.rects),({x:s,y:d}=rt(c,m,l))),p=-1)}return{x:s,y:d,placement:m,strategy:i,middlewareData:v}})(e,t,{...i,platform:o})})(C.current,N.current,e).then(e=>{const t={...e,isPositioned:!1!==S.current};I.current&&!ht(A.current,t)&&(A.current=t,Mo.flushSync(()=>{d(t)}))})},[m,t,n,R,S]);Qo(()=>{!1===c&&A.current.isPositioned&&(A.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[c]);const I=Yt.useRef(!1);Qo(()=>(I.current=!0,()=>{I.current=!1}),[]),Qo(()=>{if(b&&(C.current=b),y&&(N.current=y),b&&y){if(O.current)return O.current(b,y,x);x()}},[b,y,x,O,T]);const L=Yt.useMemo(()=>({reference:C,floating:N,setReference:h,setFloating:_}),[h,_]),w=Yt.useMemo(()=>({reference:b,floating:y}),[b,y]),D=Yt.useMemo(()=>{const e={position:n,left:0,top:0};if(!w.floating)return e;const t=bt(w.floating,s.x),r=bt(w.floating,s.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",..._t(w.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,w.floating,s.x,s.y]);return Yt.useMemo(()=>({...s,update:x,refs:L,elements:w,floatingStyles:D}),[s,x,L,w,D])}({...e,elements:{...i,...u&&{reference:u}}}),v=Yt.useCallback(e=>{const t=be(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),contextElement:e}:e;l(t),m.refs.setReference(t)},[m.refs]),f=Yt.useCallback(e=>{(be(e)||null===e)&&(s.current=e,a(e)),(be(m.refs.reference.current)||null===m.refs.reference.current||null!==e&&!be(e))&&m.refs.setReference(e)},[m.refs]),p=Yt.useMemo(()=>({...m.refs,setReference:f,setPositionReference:v,domReference:s}),[m.refs,f,v]),E=Yt.useMemo(()=>({...m.elements,domReference:c}),[m.elements,c]),g=Yt.useMemo(()=>({...m,...r,refs:p,elements:E,nodeId:t}),[m,p,E,t,r]);return Zo(()=>{r.dataRef.current.floatingContext=g;const e=null==d?void 0:d.nodesRef.current.find(e=>e.id===t);e&&(e.context=g)}),Yt.useMemo(()=>({...m,context:g,refs:p,elements:E}),[m,p,E,g])}({placement:e,open:t,onOpenChange:n,whileElementsMounted:gt,middleware:[{...ml(24),options:[24,void 0]},hl({crossAxis:e.includes("-"),fallbackAxisSideDirection:"end",padding:5}),gl({padding:5}),_l({element:o,padding:8})]}),s=c.context,d=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,dataRef:i,elements:{domReference:o}}=e,{enabled:a=!0,event:u="click",toggle:l=!0,ignoreMouse:c=!1,keyboardHandlers:s=!0}=t,d=Yt.useRef(),m=Yt.useRef(!1),v=Yt.useMemo(()=>({onPointerDown(e){d.current=e.pointerType},onMouseDown(e){const t=d.current;0===e.button&&"click"!==u&&(Fe(t,!0)&&c||(!n||!l||i.current.openEvent&&"mousedown"!==i.current.openEvent.type?(e.preventDefault(),r(!0,e.nativeEvent,"click")):r(!1,e.nativeEvent,"click")))},onClick(e){const t=d.current;"mousedown"===u&&d.current?d.current=void 0:Fe(t,!0)&&c||(!n||!l||i.current.openEvent&&"click"!==i.current.openEvent.type?r(!0,e.nativeEvent,"click"):r(!1,e.nativeEvent,"click"))},onKeyDown(e){d.current=void 0,e.defaultPrevented||!s||Bt(e)||(" "!==e.key||Ut(o)||(e.preventDefault(),m.current=!0),"Enter"===e.key&&r(!n||!l,e.nativeEvent,"click"))},onKeyUp(e){e.defaultPrevented||!s||Bt(e)||Ut(o)||" "===e.key&&m.current&&(m.current=!1,r(!n||!l,e.nativeEvent,"click"))}}),[i,o,u,c,s,r,n,l]);return Yt.useMemo(()=>a?{reference:v}:{},[a,v])}(s),m=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,dataRef:i,events:o,elements:a}=e,{enabled:u=!0,delay:l=0,handleClose:c=null,mouseOnly:s=!1,restMs:d=0,move:m=!0}=t,v=Ll(),f=Il(),p=Rt(c),E=Rt(l),g=Rt(n),h=Yt.useRef(),_=Yt.useRef(-1),b=Yt.useRef(),y=Yt.useRef(-1),C=Yt.useRef(!0),N=Yt.useRef(!1),A=Yt.useRef(()=>{}),T=Yt.useCallback(()=>{var e;const t=null==(e=i.current.openEvent)?void 0:e.type;return(null==t?void 0:t.includes("mouse"))&&"mousedown"!==t},[i]);Yt.useEffect(()=>{function e(e){let{open:t}=e;t||(clearTimeout(_.current),clearTimeout(y.current),C.current=!0)}if(u)return o.on("openchange",e),()=>{o.off("openchange",e)}},[u,o]),Yt.useEffect(()=>{function e(e){T()&&r(!1,e,"hover")}if(!u)return;if(!p.current)return;if(!n)return;const t=je(a.floating).documentElement;return t.addEventListener("mouseleave",e),()=>{t.removeEventListener("mouseleave",e)}},[a.floating,n,r,u,p,T]);const O=Yt.useCallback(function(e,t,n){void 0===t&&(t=!0),void 0===n&&(n="hover");const i=St(E.current,"close",h.current);i&&!b.current?(clearTimeout(_.current),_.current=window.setTimeout(()=>r(!1,e,n),i)):t&&(clearTimeout(_.current),r(!1,e,n))},[E,r]),R=Nt(()=>{A.current(),b.current=void 0}),S=Nt(()=>{if(N.current){const e=je(a.floating).body;e.style.pointerEvents="",e.removeAttribute(wl),N.current=!1}});Yt.useEffect(()=>{function e(){return!!i.current.openEvent&&["click","mousedown"].includes(i.current.openEvent.type)}function t(e){if(clearTimeout(_.current),C.current=!1,s&&!Fe(h.current)||d>0&&!St(E.current,"open"))return;const t=St(E.current,"open",h.current);t?_.current=window.setTimeout(()=>{g.current||r(!0,e,"hover")},t):r(!0,e,"hover")}function o(t){if(e())return;A.current();const r=je(a.floating);if(clearTimeout(y.current),p.current&&i.current.floatingContext){n||clearTimeout(_.current),b.current=p.current({...i.current.floatingContext,tree:v,x:t.clientX,y:t.clientY,onClose(){S(),R(),O(t,!0,"safe-polygon")}});const e=b.current;return r.addEventListener("mousemove",e),void(A.current=()=>{r.removeEventListener("mousemove",e)})}("touch"!==h.current||!ke(a.floating,t.relatedTarget))&&O(t)}function l(t){e()||i.current.floatingContext&&(null==p.current||p.current({...i.current.floatingContext,tree:v,x:t.clientX,y:t.clientY,onClose(){S(),R(),O(t)}})(t))}if(u&&be(a.domReference)){var c;const e=a.domReference;return n&&e.addEventListener("mouseleave",l),null==(c=a.floating)||c.addEventListener("mouseleave",l),m&&e.addEventListener("mousemove",t,{once:!0}),e.addEventListener("mouseenter",t),e.addEventListener("mouseleave",o),()=>{var r;n&&e.removeEventListener("mouseleave",l),null==(r=a.floating)||r.removeEventListener("mouseleave",l),m&&e.removeEventListener("mousemove",t),e.removeEventListener("mouseenter",t),e.removeEventListener("mouseleave",o)}}},[a,u,e,s,d,m,O,R,S,r,n,g,v,E,p,i]),Zo(()=>{var e,t;if(u&&n&&null!=(e=p.current)&&e.__options.blockPointerEvents&&T()){N.current=!0;const e=a.floating;if(be(a.domReference)&&e){const n=je(a.floating).body;n.setAttribute(wl,"");const r=a.domReference,i=null==v||null==(t=v.nodesRef.current.find(e=>e.id===f))||null==(t=t.context)?void 0:t.elements.floating;return i&&(i.style.pointerEvents=""),n.style.pointerEvents="none",r.style.pointerEvents="auto",e.style.pointerEvents="auto",()=>{n.style.pointerEvents="",r.style.pointerEvents="",e.style.pointerEvents=""}}}},[u,n,f,a,v,p,T]),Zo(()=>{n||(h.current=void 0,R(),S())},[n,R,S]),Yt.useEffect(()=>()=>{R(),clearTimeout(_.current),clearTimeout(y.current),S()},[u,a.domReference,R,S]);const x=Yt.useMemo(()=>{function e(e){h.current=e.pointerType}return{onPointerDown:e,onPointerEnter:e,onMouseMove(e){function t(){C.current||g.current||r(!0,i,"hover")}const{nativeEvent:i}=e;s&&!Fe(h.current)||n||0===d||(clearTimeout(y.current),"touch"===h.current?t():y.current=window.setTimeout(t,d))}}},[s,r,n,g,d]),I=Yt.useMemo(()=>({onMouseEnter(){clearTimeout(_.current)},onMouseLeave(e){O(e.nativeEvent,!1)}}),[O]);return Yt.useMemo(()=>u?{reference:x,floating:I}:{},[u,x,I])}(s,{handleClose:Ht({buffer:5})}),v=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,elements:i,dataRef:o}=e,{enabled:a=!0,escapeKey:u=!0,outsidePress:l=!0,outsidePressEvent:c="pointerdown",referencePress:s=!1,referencePressEvent:d="pointerdown",ancestorScroll:m=!1,bubbles:v,capture:f}=t,p=Ll(),E=Nt("function"==typeof l?l:()=>!1),g="function"==typeof l?E:l,h=Yt.useRef(!1),_=Yt.useRef(!1),{escapeKey:b,outsidePress:y}=Wl(v),{escapeKey:C,outsidePress:N}=Wl(f),A=Nt(e=>{var t;if(!n||!a||!u||"Escape"!==e.key)return;const i=null==(t=o.current.floatingContext)?void 0:t.nodeId,l=p?xt(p.nodesRef.current,i):[];if(!b&&(e.stopPropagation(),l.length>0)){let e=!0;if(l.forEach(t=>{var n;null==(n=t.context)||!n.open||t.context.dataRef.current.__escapeKeyBubbles||(e=!1)}),!e)return}r(!1,function(e){return"nativeEvent"in e}(e)?e.nativeEvent:e,"escape-key")}),T=Nt(e=>{var t;const n=()=>{var t;A(e),null==(t=Ye(e))||t.removeEventListener("keydown",n)};null==(t=Ye(e))||t.addEventListener("keydown",n)}),O=Nt(e=>{var t;const n=h.current;h.current=!1;const a=_.current;if(_.current=!1,"click"===c&&a)return;if(n)return;if("function"==typeof g&&!g(e))return;const u=Ye(e),l="["+Ot("inert")+"]",s=je(i.floating).querySelectorAll(l);let d=be(u)?u:null;for(;d&&!Se(d);){const e=Le(d);if(Se(e)||!be(e))break;d=e}if(s.length&&be(u)&&!u.matches("html,body")&&!ke(u,i.floating)&&Array.from(s).every(e=>!ke(d,e)))return;if(ye(u)&&x){const t=u.clientWidth>0&&u.scrollWidth>u.clientWidth,n=u.clientHeight>0&&u.scrollHeight>u.clientHeight;let r=n&&e.offsetX>u.clientWidth;if(n&&"rtl"===xe(u).direction&&(r=e.offsetX<=u.offsetWidth-u.clientWidth),r||t&&e.offsetY>u.clientHeight)return}const m=null==(t=o.current.floatingContext)?void 0:t.nodeId,v=p&&xt(p.nodesRef.current,m).some(t=>{var n;return He(e,null==(n=t.context)?void 0:n.elements.floating)});if(He(e,i.floating)||He(e,i.domReference)||v)return;const f=p?xt(p.nodesRef.current,m):[];if(f.length>0){let e=!0;if(f.forEach(t=>{var n;null==(n=t.context)||!n.open||t.context.dataRef.current.__outsidePressBubbles||(e=!1)}),!e)return}r(!1,e,"outside-press")}),R=Nt(e=>{var t;const n=()=>{var t;O(e),null==(t=Ye(e))||t.removeEventListener(c,n)};null==(t=Ye(e))||t.addEventListener(c,n)});Yt.useEffect(()=>{function e(e){r(!1,e,"ancestor-scroll")}if(!n||!a)return;o.current.__escapeKeyBubbles=b,o.current.__outsidePressBubbles=y;const t=je(i.floating);u&&t.addEventListener("keydown",C?T:A,C),g&&t.addEventListener(c,N?R:O,N);let l=[];return m&&(be(i.domReference)&&(l=De(i.domReference)),be(i.floating)&&(l=l.concat(De(i.floating))),!be(i.reference)&&i.reference&&i.reference.contextElement&&(l=l.concat(De(i.reference.contextElement)))),l=l.filter(e=>{var n;return e!==(null==(n=t.defaultView)?void 0:n.visualViewport)}),l.forEach(t=>{t.addEventListener("scroll",e,{passive:!0})}),()=>{u&&t.removeEventListener("keydown",C?T:A,C),g&&t.removeEventListener(c,N?R:O,N),l.forEach(t=>{t.removeEventListener("scroll",e)})}},[o,i,u,g,c,n,r,m,a,b,y,A,C,T,O,N,R]),Yt.useEffect(()=>{h.current=!1},[g,c]);const S=Yt.useMemo(()=>({onKeyDown:A,[Gl[d]]:e=>{s&&r(!1,e.nativeEvent,"reference-press")}}),[A,r,s,d]),x=Yt.useMemo(()=>({onKeyDown:A,onMouseDown(){_.current=!0},onMouseUp(){_.current=!0},[ql[c]]:()=>{h.current=!0}}),[A,c]);return Yt.useMemo(()=>a?{reference:S,floating:x}:{},[a,S,x])}(s),f=function(e,t){var n;void 0===t&&(t={});const{open:r,floatingId:i}=e,{enabled:o=!0,role:a="dialog"}=t,u=null!=(n=$l.get(a))?n:a,l=Ol(),c=null!=Il(),s=Yt.useMemo(()=>"tooltip"===u||"label"===a?{["aria-"+("label"===a?"labelledby":"describedby")]:r?i:void 0}:{"aria-expanded":r?"true":"false","aria-haspopup":"alertdialog"===u?"dialog":u,"aria-controls":r?i:void 0,..."listbox"===u&&{role:"combobox"},..."menu"===u&&{id:l},..."menu"===u&&c&&{role:"menuitem"},..."select"===a&&{"aria-autocomplete":"none"},..."combobox"===a&&{"aria-autocomplete":"list"}},[u,i,c,r,l,a]),d=Yt.useMemo(()=>{const e={id:i,...u&&{role:u}};return"tooltip"===u||"label"===a?e:{...e,..."menu"===u&&{"aria-labelledby":l}}},[u,i,l,a]),m=Yt.useCallback(e=>{let{active:t,selected:n}=e;const r={role:"option",...t&&{id:i+"-option"}};switch(a){case"select":return{...r,"aria-selected":t&&n};case"combobox":return{...r,...t&&{"aria-selected":!0}}}return{}},[i,a]);return Yt.useMemo(()=>o?{reference:s,floating:d,item:m}:{},[o,s,d,m])}(s),p=function(e){void 0===e&&(e=[]);const t=e.map(e=>null==e?void 0:e.reference),n=e.map(e=>null==e?void 0:e.floating),r=e.map(e=>null==e?void 0:e.item),i=Yt.useCallback(t=>Ft(t,e,"reference"),t),o=Yt.useCallback(t=>Ft(t,e,"floating"),n),a=Yt.useCallback(t=>Ft(t,e,"item"),r);return Yt.useMemo(()=>({getReferenceProps:i,getFloatingProps:o,getItemProps:a}),[i,o,a])}([d,v,f,m]),E=e=>{i(e)};return(0,Yt.useEffect)(()=>{const e=a<768;l(e),n((r||t)&&!e),E((r||t)&&e)},[a]),{popoverIsOpen:t,setPopoverIsOpen:n,arrowRef:o,modalIsOpen:r,toggleModal:E,isMobile:u,...p,...c}}(e);return Vt().createElement(Gc.Provider,{value:n},t)},Wc=()=>{const e=(0,Yt.useContext)(Gc);if(null==e)throw new Error("Popover components must be wrapped in <Popover />");return e},Kc=(0,Yt.forwardRef)(function({children:e,ariaLabel:t="More info",className:n,disabled:r,...i},o){const a=Wc(),u=Ct([a.refs.setReference,o]),l=(0,Yt.useMemo)(()=>({popover:{ref:u,"data-state":a.popoverIsOpen?"open":"closed",...a.getReferenceProps(),"aria-haspopup":!1},modal:{ref:o,onClick:()=>a.toggleModal(!0)}}),[a,u,o]);return Vt().createElement(Vt().Fragment,null,Vt().createElement(Aa,{type:"button",disabled:r,"aria-disabled":r,...i,variant:"icon",size:"default","aria-label":t,className:["disabled:vrui-pointer-events-none",n].join(" ").trim(),...a.isMobile?l.modal:l.popover},e))}),Xc=Kc,$c=(0,Yt.forwardRef)(function({children:e,className:t,ariaLabelledby:n="MoreInfo"},r){const{context:i,...o}=Wc(),a=Ct([o.refs.setFloating,r]),u=(0,Yt.useMemo)(()=>"right"===i.placement?"-vrui-mr-1":"left"===i.placement?"-vrui-ml-1":"",[i]);return Vt().createElement(Vt().Fragment,null,i.open&&Vt().createElement(kt,{context:i,modal:!1,disabled:!0},Vt().createElement("div",{ref:a,style:o.floatingStyles,...o.getFloatingProps(),role:"none",className:["vrui-border vrui-border-gray-3 vrui-p-16 vrui-rounded-8 vrui-w-[290px] vrui-hidden sm:vrui-block vrui-shadow-2sm vrui-bg-white vrui-z-[1000]",t].join(" ").trim()},Vt().createElement(Rl,{ref:o.arrowRef,context:i,width:30,height:15,fill:"white",stroke:"#CDCFD5",strokeWidth:1,tipRadius:2,className:u}),e)),o.modalIsOpen&&Vt().createElement(Mc,{"aria-labelledby":n,onEscapeKeyPressed:()=>o.toggleModal(!1),onOverlayClick:()=>o.toggleModal(!1)},Vt().createElement(jc,{verticalAlignment:{mobile:"center",tablet:"center",desktop:"center"},useDefaultRadius:!1,className:"vrui-rounded-16"},Vt().createElement(kc,null,Vt().createElement(Aa,{variant:"icon",size:"default",className:"vrui-absolute vrui-top-12 vrui-right-12 vrui-leading-0",onClick:()=>o.toggleModal(!1)},Vt().createElement(ma,{className:"vrui-text-16 vrui-text-blue-2",iconClass:"vi_vrui",iconName:"vi_close"}),Vt().createElement(ha,null,"Close modal")),Vt().createElement("div",{className:"vrui-w-full vrui-pt-12"},e)))))}),Qc=$c,Zc=(0,Yt.createContext)({steps:[],initializeSteps:()=>{},updateStepsStatus:()=>{},screenReaderText:{}}),Jc=()=>{const e=(0,Yt.useContext)(Zc);if(null==e)throw new Error("ProgressStep components must be wrapped in <Popover />");return e},es=Zc,ts=({stepWidth:e})=>{const{steps:t,screenReaderText:n}=Jc(),r={disabled:Vt().createElement("div",{className:"vrui-h-[12px] vrui-w-[12px] vrui-border-[2px] vrui-bg-red vrui-rounded-full"}),current:Vt().createElement("div",{className:"vrui-h-[24px] vrui-w-[24px] vrui-border-[5px] vrui-bg-blue vrui-rounded-full"}),completed:Vt().createElement("div",{className:"vrui-h-[24px] vrui-w-[24px] vrui-flex vrui-justify-center vrui-items-center"},Vt().createElement(ma,{className:"vrui-text-blue vrui-text-[7px]",iconName:"vi_check_tk",iconClass:"vi_vrui"}))},{width:i}=ia();return Vt().createElement(Vt().Fragment,null,Vt().createElement("div",{className:"vrui-flex vrui-justify-between vrui-absolute vrui-top-[11px] vrui-ml-[12px]"},t.map((n,r)=>Vt().createElement("div",{key:n.index,className:"vrui-bg-pink "+("completed"===n.status?"vrui-h-[4px] vrui-top-[-1px] vrui-relative":"vrui-h-[2px]"),style:{width:`${r+1!=t.length?e:0}px`}}))),Vt().createElement("div",{className:"vrui-flex vrui-justify-between vrui-relative"},t.map((e,o)=>{const a=o+1,u=t.length,l=e.status;let c;n||(c=i<768?`${e.index+1}/${t.length}`:`${e.index+1}.`);let s,d="";return"completed"===l&&n?.completed?d=n.completed.replace("{step}",`${a}`).replace("{total}",`${u}`):"current"===l&&n?.current?d=n.current.replace("{step}",`${a}`).replace("{total}",`${u}`):"disabled"===l&&n?.disabled&&(d=n.disabled.replace("{step}",`${a}`).replace("{total}",`${u}`)),s=n?`${e.title} ${d&&d}`:`${c} ${e.title} ${e.status} step ${d&&d}`,Vt().createElement("div",{key:e.index,className:`vrui-h-[24px] vrui-w-[24px] ${"disabled"!==e.status?"vrui-bg-white":"vrui-bg-transparent"} vrui-rounded-full vrui-flex vrui-items-center vrui-justify-center`},Vt().createElement(Ru,{className:"vrui-rounded-full focus-visible:vrui-outline-white focus-visible:vrui-outline focus-visible:vrui-outline-2 focus-visible:vrui-outline-offset-3",key:e.index,href:"javascript:void(0)",variant:"default",size:"default",onClick:"completed"===e.status?e.onClick:e=>{e.preventDefault},"aria-disabled":"disabled"===e.status?"true":"false",id:`step-status-indicator-${e.index+1}`,...n?{}:{title:i<768?s:""}},r[e.status],Vt().createElement(ha,null,s)))})))},ns=({className:e,children:t,activeStep:n=0,screenReaderText:r,...i})=>{const[o,a]=(0,Yt.useState)([]),u=(0,Yt.useRef)(null),l=(0,Yt.useRef)(null),{width:c}=ia(200),[s,d]=(0,Yt.useState)(0),m=e=>{a(t=>t.map(t=>t.index===e?{...t,status:"current"}:t.index<e?{...t,status:"completed"}:{...t,status:"disabled"}))};return(0,Yt.useEffect)(()=>{m(n)},[n]),(0,Yt.useEffect)(()=>{if(o.length&&u.current&&l.current)if(c<768){const e=(c-24)/3,t=3*e+24-(o?.length<4?(4-o?.length)*e:0);u.current.style.width=`${t}px`,d(e);const r=(c-24)/2-12,i=n*e;u.current.style.transform=o?.length<3?`translateX(${(c-24)/2-t/2}px)`:`translateX(${r-i}px)`}else{u.current.style.transform="translateX(0px)";const e=l.current.offsetWidth,t=e/o.length,n=e-t+24;u.current.style.width=`${n}px`,d(t)}},[o,c]),Vt().createElement(es.Provider,{value:{steps:o,updateStepsStatus:m,initializeSteps:e=>{a(t=>t.findIndex(t=>t.index===e.index)>=0?t:[...t,e]),m(n)},screenReaderText:r}},Vt().createElement("div",{className:"-vrui-mt-5 vrui-relative vrui-text-white"},Vt().createElement("div",{ref:l,className:["vrui-overflow-hidden vrui-pt-5 before:sm:vrui-content-none after:sm:vrui-content-none after:vrui-content[''] after:vrui-h-full after:vrui-absolute after:vrui-w-[25px] after:vrui-top-0 after:vrui-right-0 after:vrui-z-[1] after:vrui-bg-gradient-to-l after:vrui-from-red after:vrui-to-transparent before:vrui-content[''] before:vrui-h-full before:vrui-absolute before:vrui-w-[25px] before:vrui-top-0 before:vrui-left-0 before:vrui-z-[1] before:vrui-bg-gradient-to-r before:vrui-from-red before:vrui-to-transparent",e].join(" ").trim(),...i},Vt().createElement("div",{ref:u,className:"sm:vrui-mx-auto vrui-relative vrui-transition-transform vrui-mb-10"},Vt().createElement(ts,{stepWidth:s})),c<768?Vt().createElement("div",null,t):Vt().createElement("div",{className:"vrui-hidden sm:vrui-flex"},t))))},rs=({children:e,step:t,...n})=>{const{steps:r}=Jc();return Vt().createElement("div",{className:("current"!==t?.status?"vrui-hidden":"vrui-font-semibold")+" sm:vrui-block vrui-relative",...n},Vt().createElement("span",{className:"sm:vrui-hidden"},t?`${t.index+1}/${r.length} - `:""),Vt().createElement("span",{className:"vrui-hidden sm:vrui-inline"},t?`${t.index+1}. `:""),e)},is=({index:e,title:t,onClick:n})=>{const{steps:r,initializeSteps:i}=Jc(),o=r.find(t=>t.index===e),{width:a}=ia(200);return(0,Yt.useEffect)(()=>{i({index:e,status:"disabled",title:t,onClick:n})},[]),Vt().createElement("div",{className:"vrui-flex-1 vrui-text-14 vrui-leading-19 vrui-text-center"},"completed"===o?.status&&a>768?Vt().createElement(Ru,{className:"vrui-underline vrui-underline-offset-3 hover:vrui-no-underline vrui-font-normal vrui-text-14 vrui-leading-19 vrui-inline-block",variant:"default",size:"default",href:"javascript:void(0)",onClick:n,"aria-hidden":"true",tabIndex:-1,id:`progressive-step-${e+1}`},Vt().createElement(rs,{step:o},t)):Vt().createElement(rs,{"aria-hidden":"true",step:o},t))},os=({timerMinutes:e,timerSeconds:t,onTimeout:n,timerText:r,className:i,...o})=>{const a=((e,t)=>{const[n,r]=(0,Yt.useState)({minutes:e,seconds:t});return(0,Yt.useEffect)(()=>{r({minutes:e,seconds:t})},[e,t]),(0,Yt.useEffect)(()=>{const e=setInterval(()=>{r(t=>{let n=0,r=t.minutes;if(0===t.seconds){if(!(t.minutes>0))return clearInterval(e),t;n=59,r=t.minutes-1}else n=t.seconds-1;return{minutes:r,seconds:n}})},1e3);return()=>clearInterval(e)},[]),n})(e,t);return(0,Yt.useEffect)(()=>{0===a.minutes&&0===a.seconds&&n?.()},[a]),Vt().createElement("div",{className:["vrui-bg-gray-6 vrui-rounded-xl vrui-w-full vrui-px-30 vrui-py-15 vrui-flex vrui-justify-center vrui-flex-col",i].join(" ").trim(),"aria-live":"polite",...o},Vt().createElement("span",{className:"vrui-text-14 vrui-text-gray vrui-self-center vrui-mb-10"},r),Vt().createElement(Ba,{className:"vrui-text-30 sm:vrui-text-40 vrui-font-semibold vrui-self-center vrui-leading-[32px] sm:vrui-leading-[46px]"},String(a.minutes).padStart(2,"0"),":",String(a.seconds).padStart(2,"0")))},as=({"aria-labelledby":e="session-timer-modal",timer:t,headerTitle:n,preText:r,postText:i,footerButtonText:o,timerText:a="Time Left",onFooterButtonClick:u,onTimeout:l,...c})=>Vt().createElement(Mc,{"aria-labelledby":e,...c},Vt().createElement(jc,{className:"vrui-py-16"},Vt().createElement(Vc,{variant:"transparent",title:n,headerIcon:Vt().createElement(ma,{iconClass:la.vi_warning_c.icon,iconName:la.vi_warning_c.name,className:"vrui-text-[32px] vrui-text-yellow-2"}),className:"vrui-pb-0 sm:vrui-pb-0"}),Vt().createElement(kc,{className:"sm:vrui-ml-50 vrui-py-8 sm:vrui-py-8"},Vt().createElement("span",{className:"vrui-text-14 vrui-text-black vrui-mt-10"},r),Vt().createElement(os,{timerMinutes:t.minutes||10,timerSeconds:t.seconds||0,onTimeout:l,timerText:a}),Vt().createElement("span",{className:"vrui-text-14 vrui-text-black vrui-mb-20"},i)),Vt().createElement(Yc,{variant:"transparent",className:"sm:vrui-ml-50 vrui-pt-0 sm:vrui-pt-0"},Vt().createElement(Aa,{id:"session-timer-button",size:"regular",className:"vrui-w-fit",onClick:u},o)))),us=(0,Yt.createContext)({partition:{mobile:{image:100,copy:100},tablet:{image:50,copy:50},desktop:{image:50,copy:50}}}),ls=()=>{const e=(0,Yt.useContext)(us);if(null==e)throw new Error("Banner components must be wrapped in <Banner />");return e},cs=us,ss=({partition:e,children:t,className:n,...r})=>Vt().createElement(cs.Provider,{value:{partition:e}},Vt().createElement("div",{className:["sm:vrui-flex sm:vrui-flex-row vrui-justify-between",n].join(" ").trim(),...r},t)),ds=({className:e,children:t})=>{const{partition:n}=ls(),{width:r}=ia(100),i=(0,Yt.useMemo)(()=>r<768?n.mobile.copy:r>=768&&r<992?n.tablet.copy:n.desktop.copy,[r,n]);return Vt().createElement("div",{className:e,style:{width:`${i}%`}},t)},ms={topLeft:"",topCenter:"vrui-mx-auto",topRight:"vrui-ml-auto",bottomLeft:"vrui-self-end",bottomCenter:"vrui-mx-auto vrui-self-end",bottomRight:"vrui-ml-auto vrui-self-end",middleLeft:"vrui-self-center",middleCenter:"vrui-mx-auto vrui-self-center",middleRight:"vrui-ml-auto vrui-self-center",default:""},vs=({placement:e,children:t,className:n})=>{const{partition:r}=ls(),{width:i}=ia(100),o=(0,Yt.useMemo)(()=>i<768?r.mobile.image:i>=768&&i<992?r.tablet.image:r.desktop.image,[i,r]);return Vt().createElement("div",{className:"vrui-flex",style:{width:`${o}%`}},Vt().createElement("div",{className:[ms[e],n].join(" ").trim()},t))},fs=(0,Yt.createContext)({setIsModalOpen:()=>{},isModalOpen:!1}),ps=()=>{const e=(0,Yt.useContext)(fs);if(null==e)throw new Error("ProgressStep components must be wrapped in <Popover />");return e},Es=fs,gs=({isOpen:e,children:t,...n})=>{const[r,i]=(0,Yt.useState)(e);return Vt().createElement(Es.Provider,{value:{isModalOpen:r,setIsModalOpen:i}},Vt().createElement("sup",{...n},t))},hs=({children:e,...t})=>{const{isModalOpen:n,setIsModalOpen:r}=ps();return Vt().createElement(Aa,{variant:"default",size:"default",...t,onClick:()=>r(!n)},Vt().createElement("span",{className:"vrui-sr-only"},"footnote "),e)},_s=({children:e})=>{const{isModalOpen:t,setIsModalOpen:n}=ps();return Vt().createElement(Vt().Fragment,null,t&&Vt().createElement(Mc,{modalWidth:{mobile:"100%",tablet:"100%",desktop:"100%"},hasBackdrop:!1,"aria-labelledby":"modal-footnote",onEscapeKeyPressed:()=>n(!1),onOverlayClick:()=>n(!1)},Vt().createElement(jc,{verticalAlignment:{mobile:"bottom",tablet:"bottom",desktop:"bottom"},className:"vrui-border-solid vrui-border-1 vrui-border-gray-3"},Vt().createElement(Vc,{variant:"none",rightButtonIcon:"default",title:"Additional details",onRightButtonClicked:()=>n(!1),isDefaultPadding:!1,className:"vrui-ps-24 vrui-pe-16 vrui-py-16 sm:vrui-ps-32 sm:vrui-pe-24 sm:vrui-py-24"}),Vt().createElement(kc,{isDefaultPadding:!1,className:"vrui-px-24 vrui-pb-24 sm:vrui-px-32 sm:vrui-pb-32"},Vt().createElement("div",{className:"vrui-w-full vrui-text-12 vrui-leading-14 vrui-text-darkblue"},e)))))},bs=(0,Yt.createContext)({visibility:!1}),ys=({children:e})=>{const[t,n]=(0,Yt.useState)(!1),r=(0,Yt.useRef)(null),i=sa(0),o=()=>{if(r.current){const e=r.current.getBoundingClientRect();n(e.top>=0&&e.bottom<=window.innerHeight)}};return(0,Yt.useEffect)(()=>{o()},[i]),(0,Yt.useEffect)(()=>(window.addEventListener("scroll",o),()=>window.removeEventListener("scroll",o)),[]),Vt().createElement(bs.Provider,{value:{visibility:!t}},Vt().createElement("div",{ref:r},e))},Cs=({children:e,className:t,...n})=>{const{visibility:r}=(0,Yt.useContext)(bs);return Vt().createElement("div",{className:["vrui-fixed vrui-left-0 vrui-right-0 vrui-bottom-0 vrui-z-50 "+(r?"":"vrui-hidden"),t].join(" ").trim(),...n},e)},Ns=({children:e,className:t,...n})=>Vt().createElement("div",{className:t,...n},e),As=(0,Yt.createContext)({sameHeightGroups:[],updateSameHeightGroups:()=>{},deleteItem:()=>{}}),Ts=({children:e})=>{const[t,n]=(0,Yt.useState)([]),r=(0,Yt.useCallback)((e,t)=>{n(n=>n.some(t=>t.groupIndex===e)?n.map(n=>n.groupIndex===e?n.items.some(e=>e.index===t.index)?{...n,items:n.items.map(e=>e.index===t.index?t:e)}:{...n,items:[...n.items,t]}:n):[...n,{groupIndex:e,items:[t]}])},[]);return Vt().createElement(As.Provider,{value:{sameHeightGroups:t,updateSameHeightGroups:r,deleteItem:(e,t)=>{n(n=>n.map(n=>n.groupIndex===e?{...n,items:n.items.filter(e=>e.index!==t)}:n))}}},e)},Os=({children:e,index:t,groupIndex:n})=>{const r=(0,Yt.useRef)(null),[i,o]=(0,Yt.useState)(null),{width:a}=ia(100),u=sa(),l=(0,Yt.useContext)(As);if(!l)return null;const{sameHeightGroups:c,updateSameHeightGroups:s,deleteItem:d}=l;return(0,Yt.useEffect)(()=>{const e=c.find(e=>e.groupIndex===n),t=e?.items.map(e=>e.height);o(t?Math.max(...t):null)},[a,u,c]),(0,Yt.useEffect)(()=>{if(r.current){const e=r.current.offsetHeight;c&&s(n,{index:t,height:e})}return()=>{d(n,t)}},[u]),Vt().createElement("div",{ref:r,style:{minHeight:i?`${i}px`:"auto"},"data-id":t},e)},Rs=(0,Yt.forwardRef)(function({borderRadiusClassName:e,cursorPointer:t=!1,...n},r){const{unavailable:i}=Is();return Vt().createElement("div",{className:"vrui-group/inputradio vrui-absolute vrui-right-0 vrui-top-0 vrui-leading-0 vrui-size-full"},Vt().createElement("div",{className:["vrui-absolute vrui-size-full group-has-[:focus-visible]/inputradio:vrui-outline-2 group-has-[:focus-visible]/inputradio:vrui-outline-blue group-has-[:focus-visible]/inputradio:vrui-outline vrui-rounded-[3px]",i?"group-has-[:focus-visible]/inputradio:vrui-outline-offset-1":"group-has-[:focus-visible]/inputradio:vrui-outline-offset-2"].join(" ").trim()}),Vt().createElement("div",{className:["vrui-absolute vrui-size-full group-has-[:checked]/inputradio:vrui-outline-2 group-has-[:checked]/inputradio:vrui-outline-blue group-has-[:checked]/inputradio:vrui-outline vrui-rounded-[3px]",i?"group-has-[:checked]/inputradio:-vrui-outline-offset-2":"group-has-[:checked]/inputradio:-vrui-outline-offset-1 vrui-border-2 vrui-border-white"].join(" ").trim()}),Vt().createElement("input",{type:"radio",className:"vrui-absolute vrui-left-0 vrui-top-0 vrui-size-full vrui-z-10 vrui-opacity-0 "+(t?"vrui-cursor-pointer":""),ref:r,disabled:i,"aria-disabled":i,...n}))}),Ss=({color:e})=>Vt().createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32",viewBox:"0 0 32 32",fill:"none"},Vt().createElement("g",{filter:"url(#filter0_i_12049_89585)"},Vt().createElement("path",{d:"M0 4C0 1.79086 1.79086 0 4 0H28C30.2091 0 32 1.79086 32 4V28C32 30.2091 30.2091 32 28 32H4C1.79086 32 0 30.2091 0 28V4Z",fill:e})),Vt().createElement("path",{d:"M4 0.5H28C29.933 0.5 31.5 2.067 31.5 4V28C31.5 29.933 29.933 31.5 28 31.5H4C2.067 31.5 0.5 29.933 0.5 28V4C0.5 2.067 2.067 0.5 4 0.5Z",stroke:"#858A99"}),Vt().createElement("rect",{width:"5",height:"36.9218",transform:"translate(27.1074 1.40723) rotate(45)",fill:"white"}),Vt().createElement("rect",{width:"1",height:"41",transform:"translate(29.9634 1.37988) rotate(45)",fill:"#858A99"}),Vt().createElement("defs",null,Vt().createElement("filter",{id:"filter0_i_12049_89585",x:"0",y:"0",width:"32",height:"32",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB"},Vt().createElement("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),Vt().createElement("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),Vt().createElement("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),Vt().createElement("feMorphology",{radius:"2.75",operator:"erode",in:"SourceAlpha",result:"effect1_innerShadow"}),Vt().createElement("feOffset",null),Vt().createElement("feComposite",{in2:"hardAlpha",operator:"arithmetic",k2:"-1",k3:"1"}),Vt().createElement("feColorMatrix",{type:"matrix",values:"0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"}),Vt().createElement("feBlend",{mode:"normal",in2:"shape",result:"effect1_innerShadow_12049_89585"})))),xs=(0,Yt.createContext)({unavailable:!1,setUnavailable:()=>{}}),Is=()=>{const e=(0,Yt.useContext)(xs);if(!e)throw new Error("useRadioColorContext must be used within a RadioColorProvider");return e},Ls=(0,Yt.forwardRef)(function({children:e,className:t,"aria-labelledby":n,name:r,disabled:i,hexCode:o,unavailable:a=!1,...u},l){const[c,s]=(0,Yt.useState)(a);return Vt().createElement(xs.Provider,{value:{unavailable:c,setUnavailable:s}},Vt().createElement("div",{className:["vrui-group vrui-relative vrui-z-10 vrui-rounded-4",c?"":"vrui-border-1 vrui-border-gray-9",t].join(" ").trim(),style:c?{}:{backgroundColor:o}},c&&Vt().createElement("div",{className:"vrui-size-full vrui-flex vrui-items-center vrui-justify-center"},Vt().createElement(Ss,{color:o})),Vt().createElement(Rs,{name:r,ref:l,"aria-labelledby":n,disabled:i,"aria-disabled":c,...u})))}),ws=Ls,Ds={Accordion:ta,AccordionContent:da,AccordionIcon:va,AccordionItem:ra,AccordionTrigger:fa,AccordionToggleTitle:pa,Alert:ba,Button:Aa,Banner:ss,BannerCopy:ds,BannerImageContainer:vs,Card:ga,CardCarousel:Oa,Carousel:Ia,CarouselArrows:La,CarouselContent:wa,CarouselItem:Da,CarouselPagination:Ma,Checkbox:Ha,CheckboxInput:ka,ComboBox:za,ComboBoxDropDown:qa,ComboBoxInput:Ka,ComboBoxOption:Xa,Divider:$a,DynamicRadioContent:Qa,FooterLegal:eu,Footnote:gs,FootnoteContent:_s,FootnoteTrigger:hs,FormGroup:nu,SimpleFooter:iu,SimpleHeader:ou,GenericStep:uu,GenericStepHeading:su,HardStop:fu,HardStopTitle:pu,HardStopMessage:Eu,Heading:cu,Icon:ma,IconButton:xc,ListItem:Za,IconLink:Su,Link:Ru,Loader:Nu,PillFilter:Iu,Price:Mu,RadioCard:Fu,SessionTimerModal:as,SrOnly:ha,Tag:ju,Text:Ba,Container:ru,RadioCardBody:Gu,RadioCardPrice:qu,RadioButton:Vu,RadioButtonInput:Yu,RangeSlider:Ku,RangeSliderInput:Xu,RangeSliderMarkers:Qu,RangeSliderOption:Zu,RatePlanCard:Ju,Select:rc,SelectCustom:Jl,SelectNativeHidden:Ql,SelectOption:ic,SelectDropdown:tc,Tabs:ac,TabList:uc,Tab:cc,TabPanel:sc,HeadingStep:dc,RadioCardInput:ku,AddOnCard:hc,RadioCardCarousel:_c,RadioCardPartialCarousel:bc,CheckboxCard:Ec,CheckboxCardInput:vc,CheckboxCardBody:yc,CheckboxCardPrice:Cc,Label:Ac,FormControl:Oc,InputText:wc,InputError:Fa,Modal:Mc,ModalContent:jc,ModalHeader:Vc,ModalBody:kc,ModalFooter:Yc,Popover:qc,PopoverTrigger:Xc,PopoverContent:Qc,ProgressStep:ns,SameHeightGroup:Ts,SameHeightItem:Os,Step:is,TimerBox:os,iconChecker:ca,ICONS:la,useHeightResizeObserver:aa,useWindowResize:ia,DockBar:ys,Fixed:Cs,Static:Ns,useBodyHeightObserver:sa,RadioColor:Ls,RadioColorInput:Rs}})(),o})(),e.exports=r(n(1),n(11))},function(e){"use strict";e.exports=d}],m={};return e.n=function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(n,{a:n}),n},e.d=function(t,n){for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},e.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},e.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t={},function(){"use strict";function n(e,t){function n(){this.constructor=e}if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");l(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}function r(e,t,n,r){var i,o,a=arguments.length,u=a<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)u=Reflect.decorate(e,t,n,r);else for(o=e.length-1;o>=0;o--)(i=e[o])&&(u=(a<3?i(u):a>3?i(t,n,u):i(t,n))||u);return a>3&&u&&Object.defineProperty(t,n,u),u}function i(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function o(e,t){var n,r,i,o,a="function"==typeof Symbol&&e[Symbol.iterator];if(!a)return e;n=a.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=n.next()).done;)i.push(r.value)}catch(c){o={error:c}}finally{try{r&&!r.done&&(a=n.return)&&a.call(n)}finally{if(o)throw o.error}}return i}function a(e,t,n){if(n||2===arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))}var u,l,c,s,d,m,v,f,p,E,g,h,_,b,y,C,N,A,T,O,R,S,x,I,L,w,D,M,P,k,B,U,F,j,H,Y,z,V,G,q,W,K,X,$,Q,Z,J,ee,te,ne,re,ie,oe,ae,ue,le,ce,se,de,me,ve,fe,pe,Ee,ge,he,_e,be,ye,Ce,Ne,Ae,Te,Oe,Re,Se,xe,Ie,Le,we,De,Me,Pe,ke,Be,Ue,Fe,je,He,Ye,ze,Ve,Ge,qe,We,Ke,Xe,$e,Qe,Ze,Je,et,tt,nt,rt,it,ot,at,ut,lt,ct,st,dt,mt,vt,ft,pt,Et,gt,ht,_t,bt,yt,Ct,Nt,At,Tt,Ot,Rt,St,xt,It,Lt,wt,Dt,Mt,Pt,kt,Bt,Ut,Ft,jt,Ht,Yt,zt,Vt,Gt,qt,Wt,Kt,Xt,$t,Qt,Zt,Jt,en,tn,nn,rn,on,an,un,ln,cn,sn,dn,mn,vn,fn,pn,En,gn,hn,_n,bn,yn,Cn,Nn,An,Tn,On,Rn,Sn,xn,In,Ln,wn,Dn,Mn,Pn,kn,Bn,Un,Fn,jn,Hn,Yn,zn,Vn,Gn;e.r(t),e.d(t,{default:function(){return Gn}}),u={},e.r(u),e.d(u,{OmnitureOnApiFailure:function(){return ze},OmnitureOnCancelationCompleted:function(){return Ye},OmnitureOnCancelationFailed:function(){return je},OmnitureOnCancelationPartiallyCompleted:function(){return Fe},OmnitureOnCancelationPartiallyFailed:function(){return He},OmnitureOnConfirmationFailure:function(){return Ue},OmnitureOnCurrentBalance:function(){return De},OmnitureOnInteracFailure:function(){return Ve},OmnitureOnLoad:function(){return Le},OmnitureOnPaymentSelect:function(){return we},OmnitureOnReview:function(){return Me},cancelPreauthAction:function(){return Pe},cancelPreauthFailureAction:function(){return Be},cancelPreauthSuccessAction:function(){return ke},cardTokenizationError:function(){return ye},cardTokenizationSuccess:function(){return Ce},clearCardNumber:function(){return be},createMultiPaymentAction:function(){return ue},createMultiPaymentCompleted:function(){return le},createMultiPaymentFailed:function(){return ce},createPaymentAction:function(){return Z},createPaymentCompleted:function(){return J},createPaymentFailed:function(){return ee},fetchPaymentItems:function(){return H},fetchPaymentItemsFailed:function(){return z},getConfig:function(){return G},getInteracBankInfo:function(){return Oe},getPassKey:function(){return ge},getRedirectUrl:function(){return Ne},interacBankInfoFailure:function(){return Se},interacBankInfoSuccess:function(){return Re},onCardHolderNameChange:function(){return W},onCreditCardExpiryDateChange:function(){return X},onCreditCardNumberChange:function(){return q},onSecurityCodeChange:function(){return K},redirectUrlFailure:function(){return Te},redirectUrlSuccess:function(){return Ae},resetValidationErrors:function(){return Q},setConfig:function(){return V},setCreditCardInfo:function(){return _e},setInteractBankInfoFailure:function(){return Ie},setIsLoading:function(){return xe},setPassKey:function(){return he},setPaymentItems:function(){return Y},setValidationErrors:function(){return $},submitMultiOrderPaymentAction:function(){return ve},submitMultiOrderPaymentActionCompleted:function(){return fe},submitMultiOrderPaymentActionFailed:function(){return pe},submitOrderPaymentAction:function(){return ie},submitOrderPaymentActionCompleted:function(){return oe},submitOrderPaymentActionFailed:function(){return ae},tokenizeAndPropagateFormValues:function(){return Ee},validateMultiOrderPaymentAction:function(){return se},validateMultiOrderPaymentActionCompleted:function(){return de},validateMultiOrderPaymentActionFailed:function(){return me},validateOrderPaymentAction:function(){return te},validateOrderPaymentActionCompleted:function(){return ne},validateOrderPaymentActionFailed:function(){return re}}),l=function(e,t){return l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},l(e,t)},c=function(){return c=Object.assign||function(e){var t,n,r,i;for(n=1,r=arguments.length;n<r;n++)for(i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},c.apply(this,arguments)},Object.create,Object.create,s=function(e){return s=Object.getOwnPropertyNames||function(e){var t,n=[];for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[n.length]=t);return n},s(e)},"function"==typeof SuppressedError&&SuppressedError,d=e(1),m=e.n(d),v=e(2),f=e(3),p=e(4),E=e(5),g=f.CommonFeatures.BaseLocalization,h=function(t){function i(){return null!==t&&t.apply(this,arguments)||this}return n(i,t),Object.defineProperty(i.prototype,"defaultMessages",{get:function(){return e(6)},enumerable:!1,configurable:!0}),r([f.Injectable],i)}(g),_=f.CommonFeatures.BaseConfig,b=f.CommonFeatures.configProperty,y=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n(t,e),r([b("en"),i("design:type",String)],t.prototype,"language",void 0),r([b(f.LoggerSeverityLevel.All),i("design:type",Number)],t.prototype,"logLevel",void 0),r([b("/UXP.Services/eCare/Ordering/Mobility/Onebill/OrderForm/PreAuthorizePayment"),i("design:type",String)],t.prototype,"createPaymentURL",void 0),r([b("/UXP.Services/eCare/Ordering/Mobility/Onebill/OrderMultiForm/PreAuthorizePayment"),i("design:type",String)],t.prototype,"createMultiPaymentURL",void 0),r([b("B"),i("design:type",String)],t.prototype,"brand",void 0),r([b("BELLCAEXT"),i("design:type",String)],t.prototype,"channel",void 0),r([b("ON"),i("design:type",String)],t.prototype,"province",void 0),r([b("b14bc3rcGo"),i("design:type",String)],t.prototype,"userID",void 0),r([b(""),i("design:type",String)],t.prototype,"CSRFToken",void 0),r([b(""),i("design:type",Array)],t.prototype,"getPaymentItem",void 0),r([b(""),i("design:type",String)],t.prototype,"pagetitle",void 0),r([b(""),i("design:type",Object)],t.prototype,"DTSTokenization",void 0),r([b(""),i("design:type",String)],t.prototype,"paymentApiUrl",void 0),r([b(""),i("design:type",Array)],t.prototype,"getBankList",void 0),r([b(""),i("design:type",Array)],t.prototype,"transactionIdArray",void 0),r([b(""),i("design:type",String)],t.prototype,"RedirectUrl",void 0),r([b(""),i("design:type",String)],t.prototype,"BankInfoUrl",void 0),r([b(""),i("design:type",String)],t.prototype,"currentUrl",void 0),r([b(""),i("design:type",String)],t.prototype,"selectedUpdatePaymentMethod",void 0),r([b(""),i("design:type",String)],t.prototype,"isCheckedBan",void 0),r([b(""),i("design:type",Array)],t.prototype,"creditCardAutopayOffers",void 0),r([b(""),i("design:type",Array)],t.prototype,"debitCardAutopayOffers",void 0),r([b(""),i("design:type",Array)],t.prototype,"removedSubscriberOffers",void 0),r([b(""),i("design:type",String)],t.prototype,"CancelApiUrl",void 0),r([b(""),i("design:type",String)],t.prototype,"IsInteracEnabled",void 0),r([b(""),i("design:type",String)],t.prototype,"IsAutopayCreditEnabled",void 0),r([b(""),i("design:type",String)],t.prototype,"userProfileProvince",void 0),r([f.Injectable],t)}(_),C=y,function(e){e[e.Creditcard=0]="Creditcard",e[e.Debit=1]="Debit",e[e.ExistingCreditcard=2]="ExistingCreditcard"}(N||(N={})),function(e){e[e.ChangeCreditCardInfo=0]="ChangeCreditCardInfo",e[e.SwitchToBankAccount=1]="SwitchToBankAccount",e[e.UnEnroll=2]="UnEnroll"}(A||(A={})),function(e){e[e.ChangeBankAccountInfo=0]="ChangeBankAccountInfo",e[e.SwitchToCreditCard=1]="SwitchToCreditCard",e[e.UnEnroll=2]="UnEnroll"}(T||(T={})),function(e){e[e.Regular=0]="Regular",e[e.CreditCard=1]="CreditCard",e[e.PreAuthBank=2]="PreAuthBank",e[e.PreAuthCreditCard=3]="PreAuthCreditCard",e[e.Invoice=4]="Invoice",e[e.ECoupon=5]="ECoupon",e[e.Certificate=6]="Certificate"}(O||(O={})),function(e){e[e.Unknown=0]="Unknown",e[e.Success=1]="Success",e[e.Failed=2]="Failed",e[e.NetworkError=3]="NetworkError"}(R||(R={})),function(e){e[e.Unknown=0]="Unknown",e[e.Success=1]="Success",e[e.Failed=2]="Failed",e[e.NetworkError=3]="NetworkError"}(S||(S={})),function(e){e[e.Unknown=0]="Unknown",e[e.Success=1]="Success",e[e.Failed=2]="Failed",e[e.NetworkError=3]="NetworkError"}(x||(x={})),function(e){e[e.Unknown=0]="Unknown",e[e.Success=1]="Success",e[e.Failed=2]="Failed",e[e.NetworkError=3]="NetworkError"}(I||(I={})),function(e){e[e.Unknown=0]="Unknown",e[e.Active=1]="Active",e[e.Suspended=2]="Suspended",e[e.Cancelled=3]="Cancelled"}(L||(L={})),function(e){e[e.OneBill=0]="OneBill",e[e.Mobility=1]="Mobility",e[e.TV=2]="TV",e[e.Internet=3]="Internet",e[e.HomePhone=4]="HomePhone",e[e.MobilityAndOneBill=5]="MobilityAndOneBill",e[e.SingleBan=6]="SingleBan"}(w||(w={})),function(e){e[e.DC=0]="DC",e[e.VI=1]="VI",e[e.MC=2]="MC",e[e.AX=3]="AX"}(D||(D={})),function(e){e[e.OneBill=0]="OneBill",e[e.Mobility=1]="Mobility",e[e.TV=2]="TV"}(M||(M={})),function(e){e[e.Unknown=0]="Unknown",e[e.Success=1]="Success",e[e.Failed=2]="Failed",e[e.NetworkError=3]="NetworkError"}(P||(P={})),function(e){e.CardNumber="CREDIT_CARD_NUMBER",e.CardHolderName="CREDIT_CARD_HOLDER_NAME",e.ExpirationDate="CREDIT_CARD_EXPIRE_DATE",e.SecurityCode="CREDIT_CARD_SECURITY_CODE",e.BankName="BANK_NAME",e.BankAccountHolderName="BANK_ACCOUNT_HOLDER_NAME",e.BankTransitCode="BANK_TRANSIT_CODE",e.BankAccountNumber="BANK_ACCOUNT_NUM",e.ServerValidation="SERVER_VALIDATION"}(k||(k={})),function(e){e[e.Default=0]="Default",e[e.SelectBills=1]="SelectBills",e[e.PaymentMethod=2]="PaymentMethod",e[e.CurrentBalance=3]="CurrentBalance",e[e.TermsAndCondition=4]="TermsAndCondition",e[e.Confirmation=5]="Confirmation"}(B||(B={})),function(e){e[e.Default=0]="Default",e.SelectBills="SelectBills",e.PaymentMethod="PaymentMethod",e.CurrentBalance="CurrentBalance",e.TermsAndCondition="TermsAndCondition",e.Confirmation="Confirmation"}(U||(U={})),D.VI,F={CreditCardNumber:"",CreditCardNumberMasked:"",CardholderName:"",ExpireYear:"",ExpireMonth:"",SecurityCode:""},function(e){e.ONCHANGE_CREDITCARD_NUMBER="ONCHANGE_CREDITCARD_NUMBER",e.ONCHANGE_CARDHOLDER_NAME="ONCHANGE_CARDHOLDER_NAME",e.ONCHANGE_EXPIRY_MONTH="ONCHANGE_EXPIRY_MONTH",e.ONCHANGE_EXPIRY_YEAR="ONCHANGE_EXPIRY_YEAR",e.ONCHANGE_EXPIRY_DATE="ONCHANGE_EXPIRY_DATE",e.ONCHANGE_SECURITY_CODE="ONCHANGE_SECURITY_CODE",e.SET_CREDIT_CARD_DEFAULT="SET_CREDIT_CARD_DEFAULT",e.SET_CREDIT_CARD_VALIDATION="SET_CREDIT_CARD_VALIDATION",e.RESET_CREDIT_CARD_VALIDATION="RESET_CREDIT_CARD_VALIDATION"}(j||(j={})),N.Debit,H=(0,E.createAction)("FETCH_PREAUTHORIZED_PAYMENT"),Y=(0,E.createAction)("SET_PREAUTHORIZED_PAYMENT"),z=(0,E.createAction)("FETCH_PREAUTHORIZED_PAYMENT_FAILED"),V=(0,E.createAction)("SET_CONFIG"),G=(0,E.createAction)("GET_CONFIG"),q=(0,E.createAction)(j.ONCHANGE_CREDITCARD_NUMBER),W=(0,E.createAction)(j.ONCHANGE_CARDHOLDER_NAME),K=(0,E.createAction)(j.ONCHANGE_SECURITY_CODE),X=(0,E.createAction)(j.ONCHANGE_EXPIRY_DATE),$=(0,E.createAction)(j.SET_CREDIT_CARD_VALIDATION),Q=(0,E.createAction)(j.RESET_CREDIT_CARD_VALIDATION),Z=(0,E.createAction)("CREATE_PAYMENT"),J=(0,E.createAction)("CREATE_PAYMENT_COMPLETED"),ee=(0,E.createAction)("CREATE_PAYMENT_FAILED"),te=(0,E.createAction)("VALIDATE_ORDER_PAYMENT"),ne=(0,E.createAction)("VALIDATE_ORDER_PAYMENT_COMPLETED"),re=(0,E.createAction)("VALIDATE_ORDER_PAYMENT_FAILED"),ie=(0,E.createAction)("SUBMIT_ORDER_PAYMENT"),oe=(0,E.createAction)("SUBMIT_ORDER_PAYMENT_COMPLETED"),ae=(0,E.createAction)("SUBMIT_ORDER_PAYMENT_FAILED"),ue=(0,E.createAction)("CREATE_MULTI_PAYMENT"),le=(0,E.createAction)("CREATE_MULTI_PAYMENT_COMPLETED"),ce=(0,E.createAction)("CREATE_MULTI_PAYMENT_FAILED"),se=(0,E.createAction)("VALIDATE_MULTI_ORDER_PAYMENT"),de=(0,E.createAction)("VALIDATE_MULTI_ORDER_PAYMENT_COMPLETED"),me=(0,E.createAction)("VALIDATE_MULTI_ORDER_PAYMENT_FAILED"),ve=(0,E.createAction)("SUBMIT_MULTI_ORDER_PAYMENT"),fe=(0,E.createAction)("SUBMIT_MULTI_ORDER_PAYMENT_COMPLETED"),pe=(0,E.createAction)("SUBMIT_MULTI_ORDER_PAYMENT_FAILED"),Ee=(0,E.createAction)("TOKENIZE_AND_PROPAGE_FORM_VALUES"),ge=(0,E.createAction)("GET_PASSKEY"),he=(0,E.createAction)("SET_PASSKEY"),_e=(0,E.createAction)("SET_STATE",function(e){return e.cardNumberToken&&e.cardNumberToken.length>0?e.cardNumberToken.length>16?e.maskdCardNumber=e.creditCardNumber?e.creditCardNumber.replace(/\d(?=\d{4})/g,"*"):"":e.maskdCardNumber=e.cardNumberToken.replace(/\d(?=\d{4})/g,"*"):e.maskdCardNumber="",e.expiration&&e.expiration.indexOf("/")>-1&&(e.expirationMonth=e.expiration.split("/")[0],e.expirationYear=e.expiration.split("/")[1]),e}),be=(0,E.createAction)("CLEAR_CARD_NUMBER"),ye=(0,E.createAction)("TOKENIZATION_ERROR"),Ce=(0,E.createAction)("TOKENIZATION_SUCCESS"),Ne=(0,E.createAction)("GET_REDIRECT_URL"),Ae=(0,E.createAction)("GET_REDIRECT_URL_SUCCESS"),Te=(0,E.createAction)("GET_REDIRECT_URL_FAILED"),Oe=(0,E.createAction)("GET_INTERAC_BANK_INFO"),Re=(0,E.createAction)("GET_INTERAC_BANK_INFO_SUCCESS"),Se=(0,E.createAction)("GET_INTERAC_BANK_INFO_FAILED"),xe=(0,E.createAction)("SET_IS_LOADING"),Ie=(0,E.createAction)("RESET_FAILED_INTERACT_BANK_INFO"),Le=(0,E.createAction)("OMNITURE_ON_LOAD"),we=(0,E.createAction)("OMNITURE_ON_PAYMENT_SELECT"),De=(0,E.createAction)("OMNITURE_ON_CURRENT_BALANCE"),Me=(0,E.createAction)("OMNITURE_ON_REVIEW"),Pe=(0,E.createAction)("CANCEL_PREAUTH"),ke=(0,E.createAction)("CANCEL_PREAUTH_SUCCESS"),Be=(0,E.createAction)("CANCEL_PREAUTH_FAILED"),Ue=(0,E.createAction)("OMNITURE_ON_CONFIRMATION_FAILURE"),Fe=(0,E.createAction)("OMNITURE_ON_CANCELATION_PARTIALLY_COMPLETED"),je=(0,E.createAction)("OMNITURE_ON_CANCELATION_FAILED"),He=(0,E.createAction)("OMNITURE_ON_CANCELATION_PARTIALLY_FAILED"),Ye=(0,E.createAction)("OMNITURE_ON_CANCELATION_COMPLETED"),ze=(0,E.createAction)("OMNITURE_ON_VALIDATION_FAILURE"),Ve=(0,E.createAction)("OMNITURE_ON_INTERAC_FAILURE"),function(e){e[e.IDLE=0]="IDLE",e[e.PENDING=1]="PENDING",e[e.COMPLETED=2]="COMPLETED",e[e.FAILED=3]="FAILED"}(Ge||(Ge={})),qe=e(7),We=function(e,t){return e},Ke=function(e,t){return t.payload},Xe=function(e,t){var n=t.payload;return e&&n?c(c({},e),{CreditCardNumber:n.CreditCardNumber}):e},$e=function(e,t){var n=t.payload;return e&&n?c(c({},e),{CardholderName:n.CardholderName}):e},Qe=function(e,t){var n=t.payload;return e&&n?c(c({},e),{SecurityCode:n.SecurityCode}):e},Ze=function(e,t){var n=t.payload;return e&&n?c(c({},e),{ExpireMonth:n.ExpireMonth,ExpireYear:n.ExpireYear}):e},Je=function(e,t){switch(t.type){case j.SET_CREDIT_CARD_VALIDATION:return e.errors.map(function(e){return!!t.payload.errors.some(function(t){return t.field===e.field})}).filter(function(e){return!0===e}).length>0?(e.errors.map(function(e){return t.payload.errors.find(function(t){return t.field===e.field})?c(c({},e),t.payload.errors):e}),e):c(c({},e),{errors:a(a([],o(e.errors),!1),o(t.payload.errors),!1)});case j.RESET_CREDIT_CARD_VALIDATION:return c(c({},e),{errors:[]});default:return e}},et=function(){return function(e,t){var n=t.payload;return c(c({},e),n)}},tt=function(e){return function(t,n){return n.payload,e||Ge.IDLE}},nt=function(){return function(e,t){var n=t.payload;return c(c({},e),n)}},rt=function(e){return function(t,n){return n.payload,e||Ge.IDLE}},it=function(){return function(e,t){var n=t.payload;return c(c({},e),n)}},ot=function(e){return function(t,n){return n.payload,e||Ge.IDLE}},at=function(){return function(e,t){var n=t.payload;return c(c({},e),n)}},ut=function(){return function(e,t){var n=t.payload;return c(c({},e),n)}},lt=function(e,t){return t.payload},ct=function(){return function(e,t){var n=t.payload;return c(c({},e),n)}},st=function(e){return function(t,n){return n.payload,e||Ge.IDLE}},dt=function(){return function(e,t){var n=t.payload;return c(c({},e),n)}},mt=function(e){return function(t,n){return n.payload,e||Ge.IDLE}},vt=function(){return function(e,t){var n=t.payload;return c(c({},e),n)}},ft=function(e){return function(t,n){return n.payload,e||Ge.IDLE}},pt=function(){return function(e,t){var n=t.payload;return c(c({},e),n)}},Et=function(){return function(e,t){var n=t.payload;return c(c({},e),n)}},gt=function(e){return function(t,n){return n.payload,e||Ge.IDLE}},ht=e(8),_t=f.CommonFeatures.BaseClient,bt=function(e){function t(t,n){var r=e.call(this,t)||this;return r.config=n,r}return n(t,e),Object.defineProperty(t.prototype,"options",{get:function(){return{cache:!1,credentials:"include",headers:{"Content-Type":"application/json",brand:this.config.brand,channel:this.config.channel,Province:this.config.province,userID:this.config.userID,"accept-language":this.config.language,"X-CSRF-TOKEN":this.config.CSRFToken}}},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"optionsOneBill",{get:function(){return{cache:!1,credentials:"include",headers:{"Content-Type":"application/json",brand:this.config.brand,channel:this.config.channel,Province:this.config.province,userID:this.config.userID,"accept-language":this.config.language,"X-CSRF-TOKEN":this.config.CSRFToken,PM:!0}}},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"optionsPMEnabled",{get:function(){return{cache:!1,credentials:"include",headers:{"Content-Type":"application/json",brand:this.config.brand,channel:this.config.channel,Province:this.config.province,userID:this.config.userID,"accept-language":this.config.language,"X-CSRF-TOKEN":this.config.CSRFToken,PM:!0}}},enumerable:!1,configurable:!0}),t.prototype.createOrderFormData=function(e,t,n){var r=t?this.config.createPaymentURL.replace("Onebill","".concat(e)):this.config.createPaymentURL.replace("Onebill","".concat(e,"/").concat(n)),i=this.getBanSpecificTransactionId(e),o=r+"?TransactionId=".concat(i,"&province=").concat(this.config.province),a=c({},t?this.optionsOneBill:this.options);return this.post(o,null,a)},t.prototype.validateOrderForm=function(e,t,n,r,i,o){var a=t?this.config.createPaymentURL.replace("Onebill","".concat(e)):this.config.createPaymentURL.replace("Onebill","".concat(e,"/").concat(i)),u=this.getBanSpecificTransactionId(e),l=a+"/ValidatePayment?TransactionId=".concat(u,"&province=").concat(this.config.province),s=c({},t?this.optionsOneBill:this.options);return this.post(l,r?{SelectedPaymentMethod:n.SelectedPaymentMethod,BankName:n.BankName,AccountNumber:n.AccountNumber,HolderName:n.HolderName,BankCode:n.BankCode,TransitCode:n.TransitCode}:{SelectedPaymentMethod:n.SelectedPaymentMethod,CardholderName:n.CardholderName,CreditCardToken:o,CreditCardType:n.CreditCardType,ExpiryYear:n.ExpiryYear,ExpiryMonth:n.ExpiryMonth,SecurityCode:n.SecurityCode},s)},t.prototype.submitOrderForm=function(e,t,n){var r=t?this.config.createPaymentURL.replace("Onebill","".concat(e)):this.config.createPaymentURL.replace("Onebill","".concat(e,"/").concat(n)),i=this.getBanSpecificTransactionId(e),o=r+"/Submit?TransactionId=".concat(i,"&province=").concat(this.config.province),a=c({},t?this.optionsOneBill:this.options);return this.post(o,null,a)},t.prototype.createMultiOrderFormData=function(e,t,n,r){var i=t?this.config.createMultiPaymentURL.replace("Onebill","".concat(e,"/").concat(null)):this.config.createMultiPaymentURL.replace("Onebill","".concat(e,"/").concat(r)),o=this.getBanSpecificTransactionId(e),a=i+"?TransactionId=".concat(o,"&province=").concat(this.config.province),u=c({},t?this.optionsOneBill:this.optionsPMEnabled);return this.post(a,{AccountInputValues:n},u)},t.prototype.validateMultiOrderForm=function(e,t,n,r,i,o,a){var u=t?this.config.createMultiPaymentURL.replace("Onebill","".concat(e,"/").concat(null)):this.config.createMultiPaymentURL.replace("Onebill","".concat(e,"/").concat(o)),l=this.getBanSpecificTransactionId(e),s=u+"/ValidatePayment?TransactionId=".concat(l,"&province=").concat(this.config.province),d=c({},t?this.optionsOneBill:this.optionsPMEnabled);return this.post(s,i?{AccountInputValues:r,ValidatePADPACInput:{SelectedPaymentMethod:n.SelectedPaymentMethod,BankName:n.BankName,AccountNumber:n.AccountNumber,HolderName:n.HolderName,BankCode:n.BankCode,TransitCode:n.TransitCode}}:{AccountInputValues:r,ValidatePADPACInput:{SelectedPaymentMethod:n.SelectedPaymentMethod,CardholderName:n.CardholderName,CreditCardToken:a,CreditCardType:n.CreditCardType,ExpiryYear:n.ExpiryYear,ExpiryMonth:n.ExpiryMonth,SecurityCode:n.SecurityCode}},d)},t.prototype.submitMultiOrderForm=function(e,t,n,r){var i=t?this.config.createMultiPaymentURL.replace("Onebill","".concat(e,"/").concat(null)):this.config.createMultiPaymentURL.replace("Onebill","".concat(e,"/").concat(r)),o=this.getBanSpecificTransactionId(e),a=i+"/Submit?TransactionId=".concat(o,"&province=").concat(this.config.province),u=c({},t?this.optionsOneBill:this.optionsPMEnabled);return this.post(a,{AccountInputValues:n},u)},t.prototype.getPassKeyRepsonse=function(e){var t=this.config.paymentApiUrl+"".concat(e.payload.ban,"/").concat(e.payload.sub,"/payment/CreditCard/PassKey"),n=c({},this.options);return this.get(t,null,n)},t.prototype.getBanSpecificTransactionId=function(e){var t=this.config.transactionIdArray.filter(function(t){return t.Ban===e});return t&&t[0].TransactionId},t.prototype.getRedirectUrl=function(){var e=this.config.RedirectUrl,t=c({},this.options),n=this.config.currentUrl.split("&amp;").join("&");return this.post(e,{OneTimeCode:"",RedirectUrl:n},t)},t.prototype.getInteracBankInfo=function(e){var t=this.config.BankInfoUrl,n=c({},this.options),r=this.config.currentUrl.split("&amp;").join("&");return this.post(t,{RedirectUrl:r,OneTimeCode:e},n)},t.prototype.cancelPreauth=function(e){var t=this.config.CancelApiUrl,n=c({},this.options);return this.post(t,e,n)},r([f.Injectable,i("design:paramtypes",[f.AjaxServices,C])],t)}(_t),yt=function(){function e(e,t){this.client=e,this.config=t}return e.prototype.combineEpics=function(){return(0,qe.combineEpics)(this.createPaymentEpic,this.validateOrderPaymentEpic,this.submitOrderPaymentEpic,this.createMultiPaymentEpic,this.validateMultiOrderPaymentEpic,this.submitMultiOrderPaymentEpic,this.tokenizeAndPropagateFormValuesEpic,this.fetchPassKeyEpic,this.getRedirectUrlEpic,this.getInteracBankInfoEpic,this.cancelPreauthPaymentsEpic)},Object.defineProperty(e.prototype,"tokenizeAndPropagateFormValuesEpic",{get:function(){var e=this;return function(t,n){return t.pipe((0,ht.ofType)(Ee.toString()),(0,ht.mergeMap)(function(t){return(r=e.config.DTSTokenization,i=n.value.passKey,o=new DTSTokenizationPlugin,a=r.consumerId,u=r.applicationId,l=r.systemTransactionID,c=r.userID,s=r.timeout,o.setUserID(c),o.setSystemTransactionID(l),o.setApplicationID(u),o.setConsumerID(a),o.setPassKey(i),o.setPanElementID("card-number"),o.setTimeout(s),new ht.Observable(function(e){o.setSuccessHandler(function(t,n){e.next(t),e.complete()}),o.setErrorHandler(function(t){e.error(t),e.complete()}),o.tokenize()})).pipe((0,ht.mergeMap)(function(e){return(0,ht.of)(ge({ban:t.payload.ban,sub:t.payload.sub}),Ce(e.token))}),(0,ht.catchError)(function(e){return(0,ht.of)(ye("string"==typeof e&&e.length>0?e:"TOKENIZATIONERROR"))}));var r,i,o,a,u,l,c,s}),(0,ht.catchError)(function(){return(0,ht.of)(ye("TOKENIZATIONERROR"))}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"fetchPassKeyEpic",{get:function(){var e=this;return function(t,n){return t.pipe((0,ht.ofType)(ge.toString()),(0,ht.mergeMap)(function(t){return e.client.getPassKeyRepsonse(t).pipe((0,ht.map)(function(e){var t;return he(null===(t=null==e?void 0:e.data)||void 0===t?void 0:t.PassKey)}),(0,ht.catchError)(function(){return(0,ht.of)(ye("TOKENIZATIONERROR"))}))}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"createPaymentEpic",{get:function(){var e=this;return function(t,n){return t.pipe((0,ht.ofType)(Z.toString()),(0,ht.mergeMap)(function(t){var n=t.payload;return e.client.createOrderFormData(null==n?void 0:n.ban,null==n?void 0:n.type,null==n?void 0:n.sub).pipe((0,ht.map)(function(e){var t=e.data;return J(t)}),(0,ht.catchError)(function(e){return(0,ht.of)(c(c({},ee(e)),{error:!0}))}))}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"validateOrderPaymentEpic",{get:function(){var e=this;return function(t,n){return t.pipe((0,ht.ofType)(te.toString()),(0,ht.mergeMap)(function(t){var r=t.payload;return e.client.validateOrderForm(null==r?void 0:r.ban,null==r?void 0:r.type,null==r?void 0:r.details,null==r?void 0:r.isBankPaymentSelected,null==r?void 0:r.sub,n.value.cardTokenizationSuccess).pipe((0,ht.map)(function(e){var t=e.data;return ne(t)}),(0,ht.catchError)(function(e){return(0,ht.of)(c(c({},re(e)),{error:!0}))}))}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"submitOrderPaymentEpic",{get:function(){var e=this;return function(t,n){return t.pipe((0,ht.ofType)(ie.toString()),(0,ht.mergeMap)(function(t){var n=t.payload;return e.client.submitOrderForm(null==n?void 0:n.ban,null==n?void 0:n.type,null==n?void 0:n.sub).pipe((0,ht.map)(function(e){var t=e.data;return oe(t)}),(0,ht.catchError)(function(e){return(0,ht.of)(c(c({},ae(e)),{error:!0}))}))}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"createMultiPaymentEpic",{get:function(){var e=this;return function(t,n){return t.pipe((0,ht.ofType)(ue.toString()),(0,ht.mergeMap)(function(t){var n=t.payload;return e.client.createMultiOrderFormData(null==n?void 0:n.ban,null==n?void 0:n.type,null==n?void 0:n.details,null==n?void 0:n.sub).pipe((0,ht.map)(function(e){var t=e.data;return le(t)}),(0,ht.catchError)(function(e){return(0,ht.of)(c(c({},ce(e)),{error:!0}))}))}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"validateMultiOrderPaymentEpic",{get:function(){var e=this;return function(t,n){return t.pipe((0,ht.ofType)(se.toString()),(0,ht.mergeMap)(function(t){var r=t.payload;return e.client.validateMultiOrderForm(null==r?void 0:r.ban,null==r?void 0:r.type,null==r?void 0:r.details,null==r?void 0:r.accountInputValue,null==r?void 0:r.isBankPaymentSelected,null==r?void 0:r.sub,n.value.cardTokenizationSuccess).pipe((0,ht.map)(function(e){var t=e.data;return de(t)}),(0,ht.catchError)(function(e){return(0,ht.of)(c(c({},me(e)),{error:!0}))}))}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"submitMultiOrderPaymentEpic",{get:function(){var e=this;return function(t,n){return t.pipe((0,ht.ofType)(ve.toString()),(0,ht.mergeMap)(function(t){var n=t.payload;return e.client.submitMultiOrderForm(null==n?void 0:n.ban,null==n?void 0:n.type,null==n?void 0:n.details,null==n?void 0:n.sub).pipe((0,ht.map)(function(e){var t=e.data;return t.length>0&&t.find(function(e){return"Confirmation"===e.OrderFormStatus})?fe(t):pe({error:!0})}),(0,ht.catchError)(function(e){return(0,ht.of)(c(c({},pe(e)),{error:!0}))}))}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"getRedirectUrlEpic",{get:function(){var e=this;return function(t,n){return t.pipe((0,ht.ofType)(Ne.toString()),(0,ht.mergeMap)(function(t){return e.client.getRedirectUrl().pipe((0,ht.map)(function(e){var t=e.data;return Ae(t)}),(0,ht.catchError)(function(e){return(0,ht.of)(c(c({},Te(e)),{error:!0}))}))}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"getInteracBankInfoEpic",{get:function(){var e=this;return function(t,n){return t.pipe((0,ht.ofType)(Oe.toString()),(0,ht.mergeMap)(function(t){var r=t.payload;return n.value.dispatch(xe(!0)),e.client.getInteracBankInfo(null==r?void 0:r.code).pipe((0,ht.map)(function(e){var t=e.data;return n.value.dispatch(xe(!1)),Re(t)}),(0,ht.catchError)(function(e){return n.value.dispatch(xe(!1)),(0,ht.of)(c(c({},Se(e)),{error:!0}))}))}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"cancelPreauthPaymentsEpic",{get:function(){var e=this;return function(t,n){return t.pipe((0,ht.ofType)(Pe.toString()),(0,ht.mergeMap)(function(t){var r=t.payload;return n.value.dispatch(xe(!0)),e.client.cancelPreauth(null==r?void 0:r.bans).pipe((0,ht.map)(function(e){var t,r=e.data;return r.length>0&&(t=r.filter(function(e){return e.success}))&&t.length>0?(n.value.dispatch(xe(!1)),ke(r)):(n.value.dispatch(xe(!1)),Be({error:!0}))}),(0,ht.catchError)(function(e){return n.value.dispatch(xe(!1)),(0,ht.of)(c(c({},Be(e)),{error:!0}))}))}))}},enumerable:!1,configurable:!0}),r([f.Injectable,i("design:paramtypes",[bt,C])],e)}(),Ct=f.CommonFeatures.BaseStore,Nt=(0,f.CommonFeatures.actionsToComputedPropertyName)(u),At=Nt.setConfig,Tt=Nt.getConfig,Ot=Nt.onCreditCardNumberChange,Rt=Nt.onCardHolderNameChange,St=Nt.onCreditCardExpiryDateChange,xt=Nt.onSecurityCodeChange,It=Nt.setValidationErrors,Lt=Nt.resetValidationErrors,wt=Nt.createPaymentAction,Dt=Nt.createPaymentCompleted,Mt=Nt.createPaymentFailed,Pt=Nt.validateOrderPaymentAction,kt=Nt.validateOrderPaymentActionCompleted,Bt=Nt.validateOrderPaymentActionFailed,Ut=Nt.submitOrderPaymentAction,Ft=Nt.submitOrderPaymentActionCompleted,jt=Nt.submitOrderPaymentActionFailed,Ht=Nt.createMultiPaymentAction,Yt=Nt.createMultiPaymentCompleted,zt=Nt.createMultiPaymentFailed,Vt=Nt.validateMultiOrderPaymentAction,Gt=Nt.validateMultiOrderPaymentActionCompleted,qt=Nt.validateMultiOrderPaymentActionFailed,Wt=Nt.submitMultiOrderPaymentAction,Kt=Nt.submitMultiOrderPaymentActionCompleted,Xt=Nt.submitMultiOrderPaymentActionFailed,$t=Nt.setPassKey,Qt=Nt.cardTokenizationError,Zt=Nt.cardTokenizationSuccess,Jt=Nt.redirectUrlSuccess,en=Nt.interacBankInfoSuccess,tn=Nt.setIsLoading,nn=Nt.interacBankInfoFailure,rn=Nt.cancelPreauthAction,on=Nt.cancelPreauthSuccessAction,an=Nt.cancelPreauthFailureAction,un=function(e){function t(t,n,r,i){var o=e.call(this,t)||this;return o.config=n,o.localization=r,o.epics=i,o}return n(t,e),Object.defineProperty(t.prototype,"reducer",{get:function(){var e,t,n,r,i,o,a,u,l,c,s,d,m,v,f,g,h,_,b,y,C,N,A,T;return(0,p.combineReducers)({localization:this.localization.createReducer(),config:(0,E.handleActions)((e={},e[At]=Ke,e[Tt]=We,e),this.config),creditCardDetails:(0,E.handleActions)((t={},t[Ot]=Xe,t[Rt]=$e,t[xt]=Qe,t[St]=Ze,t),F),validationErrors:(0,E.handleActions)((n={},n[It]=Je,n[Lt]=Je,n),{errors:[]}),createPaymentStatus:(0,E.handleActions)((r={},r[wt]=tt(Ge.PENDING),r[Mt]=tt(Ge.FAILED),r[Dt]=tt(Ge.COMPLETED),r),Ge.IDLE),createPayment:(0,E.handleActions)((i={},i[Dt]=et(),i),{}),validateOrderFormStatus:(0,E.handleActions)((o={},o[Pt]=rt(Ge.PENDING),o[Bt]=rt(Ge.FAILED),o[kt]=rt(Ge.COMPLETED),o),Ge.IDLE),validateOrderPayment:(0,E.handleActions)((a={},a[kt]=nt(),a),{}),submitOrderFormStatus:(0,E.handleActions)((u={},u[Ut]=ot(Ge.PENDING),u[jt]=ot(Ge.FAILED),u[Ft]=ot(Ge.COMPLETED),u),Ge.IDLE),submitOrderPayment:(0,E.handleActions)((l={},l[Ft]=it(),l),{}),createMultiPaymentStatus:(0,E.handleActions)((c={},c[Ht]=st(Ge.PENDING),c[zt]=st(Ge.FAILED),c[Yt]=st(Ge.COMPLETED),c),Ge.IDLE),createMultiPayment:(0,E.handleActions)((s={},s[Yt]=ct(),s),{}),validateMultiOrderFormStatus:(0,E.handleActions)((d={},d[Vt]=mt(Ge.PENDING),d[qt]=mt(Ge.FAILED),d[Gt]=mt(Ge.COMPLETED),d),Ge.IDLE),validateMultiOrderPayment:(0,E.handleActions)((m={},m[Gt]=dt(),m),{}),submitMultiOrderFormStatus:(0,E.handleActions)((v={},v[Wt]=ft(Ge.PENDING),v[Xt]=ft(Ge.FAILED),v[Kt]=ft(Ge.COMPLETED),v),Ge.IDLE),submitMultiOrderPayment:(0,E.handleActions)((f={},f[Kt]=vt(),f),{}),passKey:(0,E.handleActions)((g={},g[$t]=function(e,t){return t.payload||e},g),""),cardTokenizationError:(0,E.handleActions)((h={},h[Qt]=function(e,t){return t.payload},h),""),cardTokenizationSuccess:(0,E.handleActions)((_={},_[Zt]=function(e,t){return t.payload},_),""),redirectUrl:(0,E.handleActions)((b={},b[Jt]=at(),b),{status:"",externalRedirectUrl:""}),interacBankInfo:(0,E.handleActions)((y={},y[en]=ut(),y),{status:"",bankAccountNumber:"",transitNumber:"",bankCode:"",accountHolderName:"",interacLicenseId:""}),interactBankFailureInfo:(0,E.handleActions)((C={},C[nn]=pt(),C),{}),isLoading:(0,E.handleActions)((N={},N[tn]=lt,N),!1),cancelPreauthStatus:(0,E.handleActions)((A={},A[rn]=gt(Ge.PENDING),A[on]=gt(Ge.COMPLETED),A[an]=gt(Ge.FAILED),A),Ge.IDLE),cancelPreauthPayments:(0,E.handleActions)((T={},T[on]=Et(),T),[])})},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"middlewares",{get:function(){return(0,qe.combineEpics)(this.epics.combineEpics())},enumerable:!1,configurable:!0}),r([f.Injectable,i("design:paramtypes",[f.Store,C,h,yt])],t)}(Ct),ln=un,cn=e(9),sn=e(10),Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()),dn=function(e){var t,n,r,i,a,u=e.intl,l=e.paymentItem,c=e.isShow,s=(e.onCurrentSteps,e.setCurrentSection),v=e.currentSection,f=(e.createPaymentData,e.setCheckedBillItems),p=e.paymentItems,E=e.createMultiPaymentData,g=e.accountInputValues,h=e.setAccountValues,_=e.transactionIds,b=e.createOmnitureOnLoad,y=e.managePreauth,C=e.isCheckedBan,N=o((0,d.useState)(!1),2),A=N[0],T=N[1],O=o((0,d.useState)(),2),R=O[0],S=O[1],x=o((0,d.useState)([]),2),I=x[0],L=x[1],w=o((0,d.useState)([]),2),D=w[0],M=w[1],P=o((0,d.useState)(!1),2),k=P[0],B=P[1],U=o((0,d.useState)(),2),F=U[0],j=U[1],H=(0,d.useRef)([]),Y=function(){var e,t,n;null!==I&&I.length>0&&E(I[0].BanID,I[0].AccountType===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).OneBill,g,I[0].subscriberId),!1===(n=!1,D&&D.length>0&&(t=(e=H.current.filter(function(e){return null==e?void 0:e.checked})).map(function(e){return null==e?void 0:e.getAttribute("data-banDetail")}).filter(function(e){return null!=e}),S(t),n=!(e.length<=0)),n)?T(!0):(T(!1),s(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).PaymentMethod))},z=y&&null!==y,V=C&&z?new Array(C):[];return m().useEffect(function(){var e,t=sessionStorage.getItem("itemsChecked"),n=t&&JSON.parse(t);null!==n&&n.length>0?(B(!0),j(n),M(p),e=r(p),E(p[0].BanID,p[0].AccountType===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).OneBill,e,p[0].subscriberId)):(j(V),M(p))},[]),m().useEffect(function(){k&&(null==D?void 0:D.length)>0&&(Y(),sessionStorage.removeItem("itemsChecked"),B(!1))},[k]),t=function(e){return e.IsOnPreauthorizedPayments&&e.CreditCardDetails?m().createElement(m().Fragment,null,m().createElement(cn.FormattedMessage,{id:"SELECT_BILLS_CC_DESC",values:{CreditCardType:Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())(e.CreditCardDetails.CreditCardType),CCFourDigits:e.CreditCardDetails.CreditCardNumber.slice(-4),ExpiryDate:e.CreditCardDetails.ExpireMonth+"/"+e.CreditCardDetails.ExpireYear}})):e.IsOnPreauthorizedPayments&&e.BankAccountDetails?m().createElement(m().Fragment,null,m().createElement(cn.FormattedMessage,{id:"SELECT_BILLS_BANK_DESC",values:{BankName:e.BankAccountDetails.BankName,Code:e.BankAccountDetails.TransitCode,BankMaskedDigits:"**"+e.BankAccountDetails.AccountNumberMaskedDisplayView}})):m().createElement(m().Fragment,null,m().createElement(sn.Icon,{className:"payment-text-16 payment-mr-5 !payment-text-[#B4781D]",iconClass:"vi_vrui",iconName:"vi_small_warning"}),m().createElement(cn.FormattedMessage,{id:"NOT_PREAUTHORIZED_NOTIFICATION"}))},n=(null==l?void 0:l.filter(function(e){return e.IsOnPreauthorizedPayments}).length)===l.length,r=function(e){return e.map(function(e){return{accountNumber:e.BanID,subNumber:e.subscriberId,transactionID:Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())(e.BanID,_),payBalanceAmnt:0}})},m().useEffect(function(){if(l.length>1){f(I);var e=r(I);h(e),0===I.length&&(f([]),h([]))}},[I]),i={MyBill:u.formatMessage({id:"ACCOUNT_TYPENAME_MYBILL"}),Mobility:u.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY"}),OneBill:u.formatMessage({id:"ACCOUNT_TYPENAME_ONEBILL"}),TV:u.formatMessage({id:"ACCOUNT_TYPENAME_TV"}),Internet:u.formatMessage({id:"ACCOUNT_TYPENAME_INTERNET"}),HomePhone:u.formatMessage({id:"ACCOUNT_TYPENAME_HOMEPHONE"}),MobilityAndOneBill:u.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY_ONEBILL"}),SingleBan:u.formatMessage({id:"ACCOUNT_TYPENAME_SINGLEBAN"})},a=function(e){return!(!e.IsNM1Account&&e.AccountType===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).OneBill&&!e.IsOnPreauthorizedPayments||(0!==e.Due&&void 0!==e.Due||e.IsOnPreauthorizedPayments,0))},m().useEffect(function(){var e=sessionStorage.getItem("itemsChecked"),t=e&&JSON.parse(e);v===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).SelectBills&&(null!==t&&t.length>0||b(A?"BAN_SELECT_ERROR":"BAN_SELECT"))},[v,A]),m().createElement("div",{className:["payment-border-b payment-divide-gray-8",c?"":"payment-hidden"].join(" ").trim()},m().createElement("div",{id:"checkboxgroup-label",className:["payment-flex payment-flex-col",v===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).SelectBills?"":"payment-hidden"].join(" ").trim()},m().createElement(sn.HeadingStep,{autoScrollActiveStep:!1,disableSrOnlyText:!0,status:"active",subtitle:"",hideSubtitle:!0,variant:"leftAlignNoStep",title:l.filter(function(e){return null==e?void 0:e.IsOnPreauthorizedPayments}).length>1?u.formatMessage({id:"SELECT_BILLS_HEADING"}):u.formatMessage({id:"SELECT_BILLS_HEADING_SINGULAR"})}),m().createElement("p",{className:"payment-text-gray payment-text-14 payment-mt-5"},l.filter(function(e){return null==e?void 0:e.IsOnPreauthorizedPayments}).length>1?u.formatMessage({id:"SELECT_BILLS_HEADING_DESC_MANAGE"}):u.formatMessage({id:"SELECT_BILLS_HEADING_DESC"}))),A&&m().createElement(m().Fragment,null,m().createElement("div",{className:"payment-mt-30"}),m().createElement(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()),null,m().createElement(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()),null,m().createElement(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()),{label:u.formatMessage({id:"ALERT_ERROR_SELECT_BILL_INFO"}),labelDescription:u.formatMessage({id:"ALERT_ERROR_SELECT_BILL_DESC"}),variant:"errorList",id:"error-alert-2"})))),m().createElement("div",{className:["payment-pt-0",v===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).SelectBills?"":"payment-hidden"].join(" ").trim()},m().createElement("div",{className:"payment-mt-30 payment-flex payment-flex-col sm:payment-flex-row payment-flex-wrap payment-items-stretch",role:"list","aria-labelledby":"checkboxgroup-label"},D&&D.length>0&&D.map(function(e,n){var r;return m().createElement("div",{role:"listitem",className:"payment-basis-0 sm:payment-basis-[47%] md:payment-basis-[35%] lg:payment-basis-[35%] payment-w-full sm:payment-w-auto"},m().createElement(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()),{className:["payment-select-bills payment-flex payment-items-center payment-group/checkboxcard payment-mr-15 payment-mb-15"].join(" ").trim(),id:"checkboxbill-".concat(n),idIndex:n,label:"checkboxBill".concat(n,"-label-").concat(n," checkboxBillBalance-").concat(n,"-label-").concat(n),isChecked:z&&F&&F.length>0?Boolean(null==F?void 0:F.find(function(t){return t===e.BanID})):e.IsChecked,billType:Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())(e.AccountType,e.IsNM1Account,i),billAccountNumber:null!==(r=e.NickName)&&void 0!==r?r:e.BillName,text:t(e),priceSettings:e.IsOnPreauthorizedPayments?void 0:{price:e.Due},isPreAuth:e.IsOnPreauthorizedPayments,ref:function(e){H.current[n]=e},item:e,isCheckedItems:I,setIsCheckedItems:L,isShowLabel:a(e),paymentItems:D}))})),A&&m().createElement(sn.Text,{elementType:"div",className:"payment-pb-15 payment-flex payment-items-center",id:"error-alert-3"},m().createElement(sn.Icon,{className:"payment-text-15 payment-text-red payment-mr-10",iconClass:"vi_vrui",iconName:"vi_small_warning"}),m().createElement(sn.Text,{elementType:"div",className:"payment-text-red payment-text-12"},u.formatMessage({id:"ALERT_ERROR_ONE_SELECT_BILL"}))),m().createElement("div",{className:"payment-pt-15 payment-pb-40"},m().createElement(sn.Button,{variant:"solidRed",className:"vrui-py-13 vrui-px-30",onClick:Y,disabled:!z&&n},u.formatMessage({id:"CTA_NEXT"})))),m().createElement(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()),{isActive:v>Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).SelectBills,onIconLinkClick:function(e){s(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).SelectBills)},banDetails:R||[],isCheckedItems:I}))},mn=function(e){return{createPayment:e.createPayment}},vn=function(e){return{createPaymentData:function(t,n,r){return e(Z({ban:t,type:n,sub:r}))},createMultiPaymentData:function(t,n,r,i){return e(ue({ban:t,type:n,details:r,sub:i}))},createOmnitureOnLoad:function(t){return e(Le({payload:t}))}}},fn=(0,v.connect)(mn,vn)((0,cn.injectIntl)(dn)),pn=function(e){var t,n=e.intl,r=e.paymentItem,i=e.checkedBillItems,u=e.setCheckedCurrentBalanceItems,l=e.setCurrentSection,c=e.currentSection,s=e.language,v=e.accountInputValues,f=e.setAccountValues,p=(e.transactionIds,e.isBankPaymentSelected),E=e.setNotOptedBalanceItems,g=(e.checkedCurrentBalanceItems,e.createOmnitureOnCurrentBalance,o((0,d.useState)([]),2)),h=g[0],_=g[1],b={MyBill:n.formatMessage({id:"ACCOUNT_TYPENAME_MYBILL"}),Mobility:n.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY"}),OneBill:n.formatMessage({id:"ACCOUNT_TYPENAME_ONEBILL"}),TV:n.formatMessage({id:"ACCOUNT_TYPENAME_TV"}),Internet:n.formatMessage({id:"ACCOUNT_TYPENAME_INTERNET"}),HomePhone:n.formatMessage({id:"ACCOUNT_TYPENAME_HOMEPHONE"}),MobilityAndOneBill:n.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY_ONEBILL"}),SingleBan:n.formatMessage({id:"ACCOUNT_TYPENAME_SINGLEBAN"})};return m().useEffect(function(){var e,t=a([],o(v),!1);h.length>0?(u(h),e=t.map(function(e){var t=h.find(function(t){return t.BanID===e.accountNumber&&t.subscriberId===e.subNumber});return e.payBalanceAmnt=t?t.Due:0,e}),f(e),E(i.filter(function(e){return e.Due>0&&e.AccountType!==Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).OneBill&&!h.includes(e)}))):(u([]),f(t),E(i.filter(function(e){return e.Due>0&&e.AccountType!==Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).OneBill})))},[h,i]),t=n.formatMessage({id:"PAY_CURRENT_BALANCE_DESC"}),m().createElement(m().Fragment,null,m().createElement("div",{className:[c===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).CurrentBalance?"payment-border-t payment-divider-gray-8 payment-mt-15":"payment-border-b payment-divider-gray-8",c>Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).CurrentBalance?"payment-hidden":""].join(" ").trim()},m().createElement("div",null,m().createElement(sn.HeadingStep,{autoScrollActiveStep:!1,disableSrOnlyText:c===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).CurrentBalance,status:c===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).CurrentBalance?"active":"inactive",subtitle:"",hideSubtitle:!0,variant:"leftAlignNoStep",id:"pre-auth-pay_curr_bal",title:(i.filter(function(e){return!(null==e?void 0:e.IsOnPreauthorizedPayments)}).length,n.formatMessage({id:"PAY_CURRENT_BALANCE_HEADING"}))}),c===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).CurrentBalance&&m().createElement("div",{className:"payment-mt-10 payment-flex"},m().createElement(sn.Icon,{className:"payment-text-darkblue payment-text-16 payment-inline-block payment-mt-[2px]",iconClass:"vi_vrui",iconName:"vi_error_c_tk"}),m().createElement("p",{className:"payment-text-darkblue payment-text-14 payment-leading-19 payment-ml-10",dangerouslySetInnerHTML:{__html:t}}))),m().createElement("div",{className:[c===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).CurrentBalance?"":"payment-hidden"].join(" ").trim()},m().createElement("div",{className:"payment-mt-30 payment-flex payment-flex-col sm:payment-flex-row payment-flex-wrap",role:"group","aria-labelledby":"checkboxgroup-label"},i.map(function(e,t){var r;return e.Due>0&&e.AccountType!==Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).OneBill?m().createElement(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"sm:payment-px-30 payment-mb-15",id:"checkboxBill-".concat(t),idIndex:t,label:"checkboxBalance-".concat(t,"-label-").concat(t," checkboxBalance-").concat(t,"-label-").concat(t,"-info"),isDisabled:e.IsOnPreauthorizedPayments,billType:Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())(e.AccountType,e.IsNM1Account,b),billAccountNumber:null!==(r=e.NickName)&&void 0!==r?r:e.BillName,text:n.formatMessage({id:"PAY_MY_BALANCE"}),priceSettings:e.IsOnPreauthorizedPayments?void 0:{price:e.Due},currentItem:e,isCheckedBalanceItems:h,setIsCheckedBalanceItems:_}):null})),m().createElement("div",{className:"payment-text-gray-4 payment-text-12 payment-my-15"},m().createElement("p",null,n.formatMessage({id:"PAY_CURRENT_BALANCE_NOTE_1"})),m().createElement("p",{className:"payment-mt-5"},n.formatMessage({id:"PAY_CURRENT_BALANCE_NOTE_2"}))),m().createElement("div",{className:"payment-pt-15 payment-pb-40 sm:payment-pb-48"},m().createElement(sn.Button,{variant:"solidRed",className:"vrui-py-13 vrui-px-30",onClick:function(){l(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).TermsAndCondition)}},n.formatMessage({id:"CTA_NEXT"}))))),m().createElement(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()),{isActive:c>Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).CurrentBalance,onIconLinkClick:function(e){l(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).CurrentBalance)},paymentItem:r,isCheckedBalanceItems:h,checkedBillItems:i,isBankPaymentSelected:p,currentSection:c,language:s}))},En=function(e){return{createOmnitureOnCurrentBalance:function(t){return e(De({data:t}))}}},(0,v.connect)(null,En)((0,cn.injectIntl)(pn)),gn=function(e,t){var n=t;if(0===n&&(e.BankAccountDetails||e.CreditCardDetails))return n+=1,e},hn=function(e,t){var n=(null==e?void 0:e.length)>0&&e.filter(function(e){return gn(e,0)});return 0===n.length&&t&&t.length>0&&(n=t.filter(function(e){return gn(e,0)})),n&&n.length>0?n[0]:null},_n=function(e,t,n,r,i,o){if(r)return{s_oPYM:"Cancel payment",error:"",s_oAPT:"332-1-0"};var a=hn(n,o);if(e)if(t){if(n.length>0&&(null==a?void 0:a.BankAccountDetails))return{s_oPYM:"Bank payment",error:"BANKERROR",s_oAPT:"327-2-2",s_oILI:i};if(n.length>0&&(null==a?void 0:a.CreditCardDetails))return{s_oPYM:"Bank payment",error:"BANKERROR",s_oAPT:"331-2-2",s_oILI:i}}else{if(n.length>0&&(null==a?void 0:a.BankAccountDetails))return{s_oPYM:"Credit card",error:"CREDITERROR",s_oAPT:"328-2-2",s_oILI:i};if(n.length>0&&(null==a?void 0:a.CreditCardDetails))return{s_oPYM:"Credit card",error:"CREDITERROR",s_oAPT:"330-2-2",s_oILI:i}}else if(t){if(n.length>0&&(null==a?void 0:a.BankAccountDetails))return{s_oPYM:"Bank payment",error:"",s_oAPT:"327-1-0",s_oILI:i};if(n.length>0&&(null==a?void 0:a.CreditCardDetails))return{s_oPYM:"Bank payment",error:"",s_oAPT:"331-1-0",s_oILI:i}}else{if(n.length>0&&(null==a?void 0:a.BankAccountDetails))return{s_oPYM:"Credit card",error:"",s_oAPT:"328-1-0",s_oILI:i};if(n.length>0&&(null==a?void 0:a.CreditCardDetails))return{s_oPYM:"Credit card",error:"",s_oAPT:"330-1-0",s_oILI:i}}return{s_oPYM:"",error:"",s_oAPT:"",s_oILI:""}},bn=function(e,t,n,r){var i=hn(t,r);if(e){if(t.length>0&&(null==i?void 0:i.BankAccountDetails))return{s_oPYM:"Bank payment",s_oAPT:"327-2-2"};if(t.length>0&&i.CreditCardDetails)return{s_oPYM:"Bank payment",s_oAPT:"331-2-2"}}else if(!e){if(t.length>0&&i.BankAccountDetails)return{s_oPYM:"Credit card",s_oAPT:"328-2-2",s_oCCDT:{CreditCardType:n}};if(t.length>0&&i.CreditCardDetails)return{s_oPYM:"Credit card",s_oAPT:"330-2-2",s_oCCDT:{CreditCardType:n}}}return{s_oPYM:"",s_oAPT:"",s_oCCDT:""}},yn=function(e,t){var n=!1;return""!==e.bankAccountNumber&&t&&""!==t.AccountNumber&&(t.AccountHolder===e.accountHolderName&&t.BankName===e.bankCode&&t.TransitNumber===e.transitNumber&&t.AccountNumber===e.bankAccountNumber||(n=!0)),n},Cn=function(e){var t,n,r,i,o,a=!1;if(Object.values(e).length>0)try{for(i=(r=function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}(Object.values(e))).next();!i.done;i=r.next())if((null==(o=i.value)?void 0:o.errorCodeID)&&""!==(null==o?void 0:o.errorCodeID)){a=!0;break}}catch(c){t={error:c}}finally{try{i&&!i.done&&(n=r.return)&&n.call(r)}finally{if(t)throw t.error}}return a},Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()),Nn=[{bankname:"BMO"},{bankname:"CIBC"},{bankname:"Desjardins"},{bankname:"RBC"},{bankname:"Scotiabank"},{bankname:"TD"}],An=function(e){var t,n,r,i,a,u,l,c,s,m,v,f,p,E,g,h,_,b,y,C,N,A,T,O,R,S=e.intl,x=e.isHeadingStepActive,I=e.paymentItem,L=e.creditcardDetails,w=e.onCreditCardNumberChange,D=e.onCardHolderNameChange,M=e.onCreditCardExpiryDateChange,P=e.onSecurityCodeChange,k=e.resetValidationErrors,B=e.validationErrors,U=e.isSingleClickEnableForPAD,F=e.isSingleClickEnableForPACC,j=e.setHeadingSteps,H=e.setCurrentSection,Y=e.currentSection,z=e.setInputValue,V=e.inputValue,G=e.setInputBankValue,q=e.inputBankValue,W=e.setIsBankSelected,K=e.validateFormOrder,X=e.checkedBillItems,$=e.tokenizeAndPropagateFormValues,Q=e.bankList,Z=e.validatBankDetails,J=e.cardTokenizationSuccess,ee=(e.redirectUrl,e.interacBankInfo),te=e.accountInputValues,ne=e.interactBankFailureInfo,re=e.creditCardAutopayOffers,ie=e.debitCardAutopayOffers,oe=e.removedSubscriberOffers,ae=e.province,ue=e.language,le=e.setOmnitureOnPaymentSelect,ce=e.managePreauth,se=e.setCancelPreauthSectionClicked,de=e.cancelPreauthSectionClicked,me=e.cancelPreauthPaymentsAction,ve=e.cancelPreauthStatus,fe=e.setIsModalOpen,pe=e.setApiSatusIsFailed,Ee=e.isInteractEnabled,ge=e.IsAutopayCreditEnabled,he=e.setSomeCancellationFailed,_e=e.cancelPreauthPayments,be=e.setBansCancellationFailed,ye=e.setCancellationPaymentFailStatus,Ce=e.interacCode,Ne=e.setOmnitureOnCancelationCompleted,Ae=e.setOmnitureOnCancelationFailed,Te=e.setOmnitureOnCancelationPartiallyCompleted,Oe=e.setOmnitureOnCancelationPartiallyFailed,Re=e.setOmnitureOnInteracFailure,Se=e.setInteracCode,xe=(e.isMultiban,o(d.useState(null==L?void 0:L.CreditCardNumber),2)),Ie=xe[0],Le=xe[1],we=o(d.useState("default"),2),De=we[0],Me=we[1],Pe=o(d.useState(""),2),ke=Pe[0],Be=Pe[1],Ue=o(d.useState(!1),2),Fe=Ue[0],je=Ue[1],He=o(d.useState(!1),2),Ye=He[0],ze=He[1],Ve=o(d.useState(!1),2),qe=Ve[0],We=Ve[1],Ke=o(d.useState(!1),2),Xe=Ke[0],$e=Ke[1],Qe=o(d.useState(!1),2),Ze=Qe[0],Je=Qe[1],et=o(d.useState(!1),2),tt=et[0],nt=et[1],rt=o(d.useState(!1),2),it=rt[0],ot=rt[1],at=o(d.useState(!1),2),ut=at[0],lt=at[1],ct=o(d.useState(!1),2),st=ct[0],dt=ct[1],mt=o(d.useState(""),2),vt=mt[0],ft=mt[1],pt=o(d.useState(!1),2),Et=pt[0],gt=pt[1],ht=o(d.useState(!1),2),_t=ht[0],bt=ht[1],yt=o(d.useState(!0),2),Ct=yt[0],Nt=yt[1],At=o(d.useState(!1),2),Tt=At[0],Ot=At[1],Rt=o(d.useState(!1),2),St=Rt[0],xt=Rt[1],It=o(d.useState({SelectedPaymentMethod:"",CardholderName:"",CreditCardToken:"",CreditCardType:"",ExpiryYear:"",ExpiryMonth:"",SecurityCode:""}),2),Lt=It[0],wt=It[1],Dt=o(d.useState({SelectedPaymentMethod:"",BankName:"",HolderName:"",TransitCode:"",AccountNumber:"",BankCode:""}),2),Mt=Dt[0],Pt=Dt[1],kt=o(d.useState(!0),2),Bt=kt[0],Ut=kt[1],Ft=d.useRef(null),jt={debit:d.useRef(null),credit:d.useRef(null)},Ht={interac:d.useRef(null),manualDetails:d.useRef(null)},Yt={inputCreditCardNumber:d.useRef(null),inputCreditCardHolderName:d.useRef(null),inputCreditCardSecurityCode:d.useRef(null),inputCreditCardExpiryMonth:d.useRef(null),inputCreditCardExpiryYear:d.useRef(null),inputBankName:d.useRef(null),inputBankAccountHolder:d.useRef(null),inputTransitNumber:d.useRef(null),inputBankAccountNumber:d.useRef(null)},zt=function(e){ft(e.target.value)};return d.useEffect(function(){vt===S.formatMessage({id:"NEW_CREDIT_ACCOUNT_LABEL"})||vt===S.formatMessage({id:"BANK_NEW_BANK_ACCOUNT_LABEL"})?dt(!0):dt(!1)},[vt]),t=function(e){Be(e.target.value)},n={VISA:S.formatMessage({id:"VISA_CC_PNG"}),MASTERCARD:S.formatMessage({id:"MASTER_CC_PNG"}),AMEX:S.formatMessage({id:"AMEX_CC_PNG"})},r=function(){je(!1),ze(!1),$e(!1),We(!1),lt(!1),ot(!1),Je(!1),nt(!1)},i=S.formatMessage({id:"PAYMENT_METHOD_DEBIT"}),a=function(e){var t,n,o,a,l,c,s,d,m,v,f,p,E,h,b,y,C,N,A,T,O,R,S,x,I,L,k,B,U,F,j,H,Y,z,V;e.preventDefault(),r(),Bt?(Pt({SelectedPaymentMethod:i,BankName:(null===(N=Yt.inputBankName.current)||void 0===N?void 0:N.value)?null===(A=Yt.inputBankName.current)||void 0===A?void 0:A.value:(null==_?void 0:_.BankName)||"",HolderName:(null===(T=Yt.inputBankAccountHolder.current)||void 0===T?void 0:T.value)?null===(O=Yt.inputBankAccountHolder.current)||void 0===O?void 0:O.value:(null==_?void 0:_.CardHolder)||"",TransitCode:(null===(R=Yt.inputTransitNumber.current)||void 0===R?void 0:R.value)?null===(S=Yt.inputTransitNumber.current)||void 0===S?void 0:S.value:(null==_?void 0:_.TransitCode)||"",AccountNumber:(null===(x=Yt.inputBankAccountNumber.current)||void 0===x?void 0:x.value)?null===(I=Yt.inputBankAccountNumber.current)||void 0===I?void 0:I.value:(null==_?void 0:_.AccountNumber)||"",BankCode:(null===(L=Yt.inputBankName.current)||void 0===L?void 0:L.value)?null===(k=Yt.inputBankName.current)||void 0===k?void 0:k.value:""}),G({PaymentMethod:i,AccountHolder:(null===(B=Yt.inputBankAccountHolder.current)||void 0===B?void 0:B.value)?null===(U=Yt.inputBankAccountHolder.current)||void 0===U?void 0:U.value:(null==_?void 0:_.CardHolder)||"",BankName:(null===(F=Yt.inputBankName.current)||void 0===F?void 0:F.value)?null===(j=Yt.inputBankName.current)||void 0===j?void 0:j.value:(null==_?void 0:_.BankName)||"",TransitNumber:(null===(H=Yt.inputTransitNumber.current)||void 0===H?void 0:H.value)?null===(Y=Yt.inputTransitNumber.current)||void 0===Y?void 0:Y.value:(null==_?void 0:_.TransitCode)||"",AccountNumber:(null===(z=Yt.inputBankAccountNumber.current)||void 0===z?void 0:z.value)?null===(V=Yt.inputBankAccountNumber.current)||void 0===V?void 0:V.value:(null==_?void 0:_.AccountNumber)||""}),Z(u())):(wt({SelectedPaymentMethod:"CreditCard",CardholderName:(null===(t=Yt.inputCreditCardHolderName.current)||void 0===t?void 0:t.value)?null===(n=Yt.inputCreditCardHolderName.current)||void 0===n?void 0:n.value:(null==g?void 0:g.CardholderName)||"",CreditCardToken:(null===(o=Yt.inputCreditCardNumber.current)||void 0===o?void 0:o.value)?null===(a=Yt.inputCreditCardNumber.current)||void 0===a?void 0:a.value:(null==g?void 0:g.CreditCardNumber)||"",CreditCardType:(null===(l=Yt.inputCreditCardNumber.current)||void 0===l?void 0:l.value)?Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())(null===(c=Yt.inputCreditCardNumber.current)||void 0===c?void 0:c.value):((null==g?void 0:g.CreditCardType)?Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())(g.CreditCardType):"")||"",ExpiryYear:(null===(s=Yt.inputCreditCardExpiryYear.current)||void 0===s?void 0:s.value)?null===(d=Yt.inputCreditCardExpiryYear.current)||void 0===d?void 0:d.value:(null==g?void 0:g.ExpireYear)||"",ExpiryMonth:(null===(m=Yt.inputCreditCardExpiryMonth.current)||void 0===m?void 0:m.value)?null===(v=Yt.inputCreditCardExpiryMonth.current)||void 0===v?void 0:v.value:(null==g?void 0:g.ExpireMonth)||"",SecurityCode:(null===(f=Yt.inputCreditCardSecurityCode.current)||void 0===f?void 0:f.value)?null===(p=Yt.inputCreditCardSecurityCode.current)||void 0===p?void 0:p.value:(null==g?void 0:g.SecurityCode)||""}),w(null===(E=Yt.inputCreditCardNumber.current)||void 0===E?void 0:E.value),D(null===(h=Yt.inputCreditCardHolderName.current)||void 0===h?void 0:h.value),M(null===(b=Yt.inputCreditCardExpiryMonth.current)||void 0===b?void 0:b.value,null===(y=Yt.inputCreditCardExpiryYear.current)||void 0===y?void 0:y.value),P(null===(C=Yt.inputCreditCardSecurityCode.current)||void 0===C?void 0:C.value)),Nt(!1),gt(!0)},d.useEffect(function(){var e,t,n,r,i,o,a,u,c,d;Et&&(null==B||B.errors.map(function(e){switch(e.field){case Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).CardNumber:je(!0);break;case Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).CardHolderName:ze(!0);break;case Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).ExpirationDate:$e(!0);break;case Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).SecurityCode:We(!0);break;case Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).BankAccountHolderName:lt(!0);break;case Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).BankName:Je(!0);break;case Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).BankTransitCode:nt(!0);break;case Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).BankAccountNumber:ot(!0)}}),(a=!!(null===(e=null==B?void 0:B.errors)||void 0===e?void 0:e.length)&&B.errors.length>0)&&xt(!0),a?!a&&s&&!st&&f&&(a||l()):(z({cardNumber:(null===(t=Yt.inputCreditCardNumber.current)||void 0===t?void 0:t.value)||"",cardType:Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())((null===(n=Yt.inputCreditCardNumber.current)||void 0===n?void 0:n.value)||""),cardName:(null===(r=Yt.inputCreditCardHolderName.current)||void 0===r?void 0:r.value)||"",expiryDate:"".concat((null===(i=Yt.inputCreditCardExpiryMonth.current)||void 0===i?void 0:i.value)||"","/").concat((null===(o=Yt.inputCreditCardExpiryYear.current)||void 0===o?void 0:o.value)||"")}),l()),a||(u=function(){var e,t,n,r,i,o,a,u,l,c,s,d,m,v;return{cardHolderName:(null===(e=Yt.inputCreditCardHolderName.current)||void 0===e?void 0:e.value)?null===(t=Yt.inputCreditCardHolderName.current)||void 0===t?void 0:t.value:(null==g?void 0:g.CardholderName)||"",creditCardNumber:(null===(n=Yt.inputCreditCardNumber.current)||void 0===n?void 0:n.value)?null===(r=Yt.inputCreditCardNumber.current)||void 0===r?void 0:r.value:(null==g?void 0:g.CreditCardNumber)||"",creditCardToken:(null===(i=Yt.inputCreditCardNumber.current)||void 0===i?void 0:i.value)?null===(o=Yt.inputCreditCardNumber.current)||void 0===o?void 0:o.value:(null==g?void 0:g.CreditCardNumber)||"",expirationMonth:(null===(a=Yt.inputCreditCardExpiryMonth.current)||void 0===a?void 0:a.value)?null===(u=Yt.inputCreditCardExpiryMonth.current)||void 0===u?void 0:u.value:(null==g?void 0:g.ExpireMonth)||"",expirationYear:(null===(l=Yt.inputCreditCardExpiryYear.current)||void 0===l?void 0:l.value)?null===(c=Yt.inputCreditCardExpiryYear.current)||void 0===c?void 0:c.value:(null==g?void 0:g.ExpireYear)||"",securityCode:(null===(s=Yt.inputCreditCardSecurityCode.current)||void 0===s?void 0:s.value)?null===(d=Yt.inputCreditCardSecurityCode.current)||void 0===d?void 0:d.value:(null==g?void 0:g.SecurityCode)||"",cardType:(null===(m=Yt.inputCreditCardHolderName.current)||void 0===m?void 0:m.value)?Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())(null===(v=Yt.inputCreditCardHolderName.current)||void 0===v?void 0:v.value):((null==g?void 0:g.CreditCardType)?Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())(g.CreditCardType):"")||""}}(),1===m.length&&(c=m[0],Bt?K(c.BanID,c.AccountType===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).OneBill,Mt,te,Bt,c.subscriberId):$(u,c.BanID,c.AccountType===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).OneBill,Lt,Bt,c.subscriberId)),m.length>1&&X&&X.length>0&&(Bt?K(X[0].BanID,X[0].AccountType===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).OneBill,Mt,te,Bt,X[0].subscriberId):$(u,X[0].BanID,X[0].AccountType===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).OneBill,Lt,Bt,X[0].subscriberId))),d=new Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())(),k(d),gt(!1))},[Et]),d.useEffect(function(){J&&X&&X.length>0&&K(X[0].BanID,X[0].AccountType===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).OneBill,Lt,te,Bt,X[0].subscriberId)},[J]),u=function(){var e,t,n,r,i,o,a,u,l,c={isValid:!0,validationForm:{bankNameError:{isEmpty:!1,isInvalid:!1},bankAccountHolderError:{isEmpty:!1,isInvalid:!1},transitNumberError:{isEmpty:!1,isInvalid:!1},bankAccountNumberError:{isEmpty:!1,isInvalid:!1}}};return(null===(e=Yt.inputBankName.current)||void 0===e?void 0:e.value)||(c.isValid=!1,c.validationForm.bankNameError.isEmpty=!0),(null===(t=Yt.inputBankAccountHolder.current)||void 0===t?void 0:t.value)?(null===(n=Yt.inputBankAccountHolder.current)||void 0===n?void 0:n.value)&&(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).test(null===(r=Yt.inputBankAccountHolder.current)||void 0===r?void 0:r.value.trim())&&(null===(i=Yt.inputBankAccountHolder.current)||void 0===i?void 0:i.value.trim().length)<=70||(c.isValid=!1,c.validationForm.bankAccountHolderError.isInvalid=!0)):(c.isValid=!1,c.validationForm.bankAccountHolderError.isEmpty=!0),(null===(o=Yt.inputTransitNumber.current)||void 0===o?void 0:o.value)?(null===(a=Yt.inputTransitNumber.current)||void 0===a?void 0:a.value.length)<5&&(c.isValid=!1,c.validationForm.transitNumberError.isInvalid=!0):(c.isValid=!1,c.validationForm.transitNumberError.isEmpty=!0),(null===(u=Yt.inputBankAccountNumber.current)||void 0===u?void 0:u.value)?(null===(l=Yt.inputBankAccountNumber.current)||void 0===l?void 0:l.value.length)<7&&(c.isValid=!1,c.validationForm.bankAccountNumberError.isInvalid=!0):(c.isValid=!1,c.validationForm.bankAccountNumberError.isEmpty=!0),c},l=function(){F||U?H(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).CurrentBalance):(H(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).TermsAndCondition),j(!1))},c=function(){H(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).PaymentMethod)},s=null==I?void 0:I.some(function(e){var t;return null!==(t=e.IsOnPreauthorizedPayments)&&void 0!==t&&t}),m=I,v=I.find(function(e){return e.BankAccountDetails}),f=I.find(function(e){return e.CreditCardDetails}),p=ne&&ne.data?ne.data:null,E=I.filter(function(e){return!0===e.IsOnPreauthorizedPayments&&e.CreditCardDetails}),g=E.length>0?E[0].CreditCardDetails:null,h=I.filter(function(e){return!0===e.IsOnPreauthorizedPayments&&e.BankAccountDetails}),_=h.length>0?h[0].BankAccountDetails:null,b=function(e){Le(e.target.value);var t=Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())(e.target.value);Me(t)},y=function(e){var t,n;(null===(n=null===(t=Ht.manualDetails)||void 0===t?void 0:t.current)||void 0===n?void 0:n.checked)?Ot(!0):Ot(!1)},C=function(e){Ut(e),W(e),se(!1)},N=ce&&null!==ce,A=function(){H(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).PaymentMethod)},T=function(){var e,t=null===(e=X.filter(function(e){return e.IsOnPreauthorizedPayments}))||void 0===e?void 0:e.map(function(e){return e.BanID});t&&t.length>0&&me(t)},d.useEffect(function(){var e,t,n;ve===Ge.COMPLETED?_e&&Object.values(_e).length>0&&((e=Object.values(_e)).every(function(e){return!0===e.success})?(fe(!0),Ne()):(t=e.filter(function(e){return!e.success}).map(function(e){return e.banId}))&&t.length>0&&(n=m.filter(function(e){return t.includes(e.BanID)}),he(!0),be(n),ye("FAILED"),Oe(),Te())):ve===Ge.FAILED&&(pe(!0),Ae())},[ve,_e]),d.useEffect(function(){null!=ee&&"SUCCESS"===ee.status?bt(!0):bt(!1)},[ee]),d.useEffect(function(){ee&&ee.interacLicenseId&&Se(ee.interacLicenseId)},[ee]),d.useEffect(function(){Y===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).PaymentMethod&&X.length>0&&(de||(!Bt||p&&Ct?Bt||de||le(_n(!1,!1,X,!1,"",I)):""===Ce&&le(_n(!1,!0,X,!1,Ce,I))))},[Y,Bt]),d.useEffect(function(){!(Ce&&""!==Ce&&X.length>0)||p&&Ct?Ct&&p&&X.length>0&&Bt&&Y===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).PaymentMethod&&Re(_n(!0,Bt,X,!1,"",I)):Bt&&Y===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).PaymentMethod&&le(_n(!1,!0,X,!1,Ce,I))},[Ce,Y,X]),d.useEffect(function(){Y===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).PaymentMethod&&St&&(le(_n(!0,Bt,X,!1,Ce,I)),xt(!1))},[St]),O=d.useRef(null),R=X.some(function(e){return!0===e.IsOnPreauthorizedPayments}),d.useEffect(function(){var e,t;R||p||jt.debit&&jt.debit.current&&(jt.debit.current.checked=!0,Ee&&Ht.interac&&Ht.interac.current&&(Ht.interac.current.checked=!0),!Ee&&Ht.manualDetails&&Ht.manualDetails.current&&(Ht.manualDetails.current.checked=!0,(null===(t=null===(e=Ht.manualDetails)||void 0===e?void 0:e.current)||void 0===t?void 0:t.checked)?Ot(!0):Ot(!1)))},[X]),d.useEffect(function(){var e,t;null!=ne&&"error"===ne.dataType&&jt.debit&&jt.debit.current&&(jt.debit.current.checked=!0,(null===(t=null===(e=Ht.manualDetails)||void 0===e?void 0:e.current)||void 0===t?void 0:t.checked)?Ot(!0):Ot(!1))},[ne]),d.createElement(d.Fragment,null,d.createElement("div",{ref:O,className:["payment-border-b payment-divide-gray-8",Y>Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).PaymentMethod?"payment-hidden":""].join(" ").trim()},d.createElement("div",null,d.createElement(sn.HeadingStep,{autoScrollActiveStep:!1,id:"payment-method",disableSrOnlyText:"inactive"!==x,className:"focus-visible:payment-outline-none",tabIndex:-1,status:x,subtitle:"",hideSubtitle:!0,variant:"leftAlignNoStep",title:S.formatMessage({id:"UPDATE_PAYMENT_METHOD_HEADING"})})),(Fe||Ye||Xe||qe||ut||it||Ze||tt)&&Y!==Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).SelectBills?d.createElement("div",{className:"payment-pt-24"},d.createElement(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()),{isErrorCardNumber:Fe,isErrorCardName:Ye,isErrorSecurityCode:qe,isErrorExpiryDate:Xe,isErrorBankAccountHolderName:ut,isErrorBankAccountNumber:it,isErrorBankName:Ze,iserrorBankTransit:tt,inputRefs:Yt})):d.createElement(d.Fragment,null),Ct&&p&&d.createElement("div",{className:"".concat("active"===x?"":"payment-hidden"," payment-pt-30")},d.createElement(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()),{interact:p})),d.createElement("div",{role:"radiogroup","aria-labelledby":"payment-method"},d.createElement("div",{className:"".concat("active"===x?"":"payment-hidden"," payment-pt-24")},d.createElement(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()),{Checked:!0,isInteracSelected:_t,checkedBillItems:X,radioCardRef:Ht,handleBankRadioManualDetailsChange:y,isBankManualEnterDetails:Tt,isPreauth:s,hasBankAccountDetails:v,bankitems:m,handleBankRadioChange:zt,bankListInterac:Nn,isBankChecked:st,inputRefs:Yt,errorBankName:Ze,errorBankTransit:tt,errorBankAccountNumber:it,errorBankAccountHolderName:ut,radioRef:Ft,bankList:Q,onChange:function(){C(!0)},creditCardAutopayOffers:re,debitCardAutopayOffers:ie,language:ue,isInteractEnabled:Ee,IsAutopayCreditEnabled:ge,managePreauth:ce,payMethodRadioRef:jt}),d.createElement(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()),{isPreauth:s,hasCreditCardDetails:f,bankitems:m,radioRef:Ft,handleBankRadioChange:zt,isBankChecked:st,cardNumber:Ie,handleCreditCardChange:b,inputRefs:Yt,cardIcons:n,cardType:De,errorCardNumber:Fe,errorCardName:Ye,errorExpiryDate:Xe,errorSecurityCode:qe,handleMaskCVV:t,CVV:ke,onChange:function(){C(!1)},checkedBillItems:X,creditCardAutopayOffers:re,debitCardAutopayOffers:ie,language:ue,IsAutopayCreditEnabled:ge,managePreauth:ce}),N&&R&&d.createElement(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()),{Checked:!1,paymentItems:m,checkedBillItems:X,managePreauth:ce,setCancelPreauthSectionClicked:se,cancelPreauthSectionClicked:de,removedSubscriberOffers:oe,province:ae,IsAutopayCreditEnabled:ge,setOmnitureOnPaymentSelect:le}),de?d.createElement("div",{className:"payment-border-b-1 payment-border-gray-3 payment-mb-[77px] sm:payment-mb-[92px]"},d.createElement("div",{className:"payment-inline-flex payment-flex-wrap payment-items-center payment-pb-[45px] sm:payment-pb-[60px]"},d.createElement("div",{className:"payment-pr-30 payment-pt-15"},d.createElement(sn.Button,{variant:"solidRed",onClick:T},S.formatMessage({id:"CTA_CONFIRM_CANCEL"}))),d.createElement("div",{className:"payment-pt-15"},d.createElement(sn.Button,{variant:"textBlue",className:"!payment-text-14 payment-leading-18",onClick:A},S.formatMessage({id:"CTA_CANCEL"}))))):d.createElement("div",{className:"payment-pt-15 payment-pb-32 payment-mb-16"},d.createElement(sn.Button,{variant:"solidRed",onClick:a,disabled:!1},S.formatMessage({id:"CTA_NEXT"})))))),d.createElement(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()),{paymentItem:m,className:Y>Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).PaymentMethod?"payment-divide-gray-8 payment-pb-44":"payment-hidden",isPreauth:s,inputValue:V,inputBankValue:q,isNewbank:null!=st&&st,onEditClick:c,showHeading:!0,isBankPaymentSelected:Bt,isSingleClickEnable:F||U,bankList:Q,debitCardAutopayOffers:ie,creditCardAutopayOffers:re,checkedBillItems:X,bankitems:m,isConfirmation:!1,isOnManagePreauth:null!=ce,IsAutopayCreditEnabled:ge}))},Tn=function(e){return{creditcardDetails:e.creditCardDetails,validationErrors:e.validationErrors,cardTokenizationSuccess:e.cardTokenizationSuccess,redirectUrl:e.redirectUrl,interacBankInfo:e.interacBankInfo,interactBankFailureInfo:e.interactBankFailureInfo,cancelPreauthStatus:e.cancelPreauthStatus,cancelPreauthPayments:e.cancelPreauthPayments}},On=function(e){return{onCreditCardNumberChange:function(t){var n=new Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())(),r=Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())(t);n.CreditCardNumber=t,t||(r=Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())(t),e($(r))),(!r||r.length<=0||!r.errors||r.errors.length<=0)&&e(q(n))},onCardHolderNameChange:function(t){var n,r=new Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())(),i=Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())(t);r.CardholderName=t,(null===(n=null==(i=Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())(t))?void 0:i.errors)||void 0===n?void 0:n.length)>0&&e($(i)),(!i||i.length<=0||!i.errors||i.errors.length<=0)&&e(W(r))},onCreditCardExpiryDateChange:function(t,n){var r=new Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())(),i=Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())(t,n);r.ExpireMonth=t,r.ExpireYear=n,((i=Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())(t,n)).length>0||i.errors)&&e($(i)),(!i||i.length<=0||!i.errors||i.errors.length<=0)&&e(X(r))},onSecurityCodeChange:function(t){var n=new Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())(),r=Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())(t);n.SecurityCode=t,t||(r=Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())(t),e($(r))),(!r||r.length<=0||!r.errors||r.errors.length<=0)&&e(K(n))},validatBankDetails:function(t){var n=Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())(t);t.isValid?e(Q({errors:[]})):e($(n))},resetValidationErrors:function(t){e(Q(t))},validateFormOrder:function(t,n,r,i,o,a){e(se({ban:t,type:n,details:r,accountInputValue:i,isBankPaymentSelected:o,sub:a}))},tokenizeAndPropagateFormValues:function(t,n,r,i,o,a){e(Ee({form:t,ban:n,type:r,details:i,isBankPaymentSelected:o,sub:a}))},setOmnitureOnPaymentSelect:function(t){e(we({data:t}))},cancelPreauthPaymentsAction:function(t){e(Pe({bans:t}))},setCancellationPaymentFailStatus:function(t){return e(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())({type:t}))},setOmnitureOnCancelationCompleted:function(t){e(Ye(t))},setOmnitureOnCancelationPartiallyCompleted:function(t){e(Fe(t))},setOmnitureOnCancelationFailed:function(t){e(je(t))},setOmnitureOnCancelationPartiallyFailed:function(t){e(He(t))},setOmnitureOnInteracFailure:function(t){e(Ve(t))}}},Rn=(0,v.connect)(Tn,On)((0,cn.injectIntl)(An)),Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()),Sn=function(e){var t,n,r=e.isActive,i=e.intl,a=(e.onCurrentSteps,e.setCurrentSection),u=e.currentSection,l=e.checkedBillItems,c=e.submitFormOrder,s=e.paymentItem,m=e.province,v=e.language,f=e.userProfileProv,p=e.accountInputValues,E=e.setOmnitureOnReview,g=e.isBankSelected,h=e.validateMultiOrderFormStatus,_=e.tokenizeAndPropagateFormValuesStatus,b=e.setApiSatusIsFailed,y=e.setOmnitureOnValidationFailure,C=e.inputValue,N=e.interacCode,A=e.inputBankValue,T=e.interacBankInfo,O=e.validateMultiOrderPayment,R=o(d.useState(Ge.IDLE),2),S=R[0],x=R[1],I=o(d.useState(Ge.IDLE),2),L=I[0],w=I[1],D=d.useRef(null);return d.useEffect(function(){if(h===Ge.COMPLETED){var e=!Cn(O);u===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).TermsAndCondition&&e?E(g?yn(T,A)?"":N:""):e||(y(g?bn(!0,l,"",s):bn(!1,l,C.cardType,s)),b(!0))}},[u,h]),d.useEffect(function(){g||(_!==Ge.FAILED?w(Ge.PENDING):w(_))},[_]),d.useEffect(function(){g?(x(h),h===Ge.FAILED&&(b(!0),y(bn(!0,l,"",s)))):w(h)},[h]),d.useEffect(function(){L===Ge.FAILED&&(b(!0),y(bn(!1,l,C.cardType,s)))},[L]),t=i.formatMessage({id:"LOADER_PAYMENT"}),n=i.formatMessage({id:"LOADER_PAYMENT_DESC"}),d.createElement(d.Fragment,null,(S===Ge.PENDING||L===Ge.PENDING)&&(window.scrollTo({top:0,behavior:"auto"}),d.createElement(sn.Loader,{text:"<div className='vrui-font-bold'>"+t+"</div><div>"+n+"</div>"})),d.createElement(d.Fragment,null,d.createElement("div",{className:u===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).TermsAndCondition?"payment-border-t payment-border-gray-8 payment-mt-45":"",id:"TermsAndConditionsSection"},d.createElement("div",{ref:D,className:"payment-mb-32"},d.createElement("div",{id:"termsAndCondDivID",className:r?"focus-visible:payment-outline-none":""},d.createElement(sn.HeadingStep,{autoScrollActiveStep:!1,disableSrOnlyText:!!r,tabIndex:-1,className:"focus-visible:payment-outline-none",status:r?"active":"inactive",subtitle:"",hideSubtitle:!0,variant:"leftAlignNoStep",title:"QC"===m&&"en"===v?i.formatMessage({id:"TERMS_AND_CONDITION_HEADING_QC"}):i.formatMessage({id:"TERMS_AND_CONDITION_HEADING"})})),d.createElement("div",{className:["payment-pt-24",r?"":"payment-hidden"].join(" ").trim()},d.createElement(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()),{onSubmitClick:function(){s.length>1&&l&&l.length>0&&c(l[0].BanID,l[0].AccountType===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).OneBill,p,l[0].subscriberId),s&&1===s.length&&c(l[0].BanID,l[0].AccountType===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).OneBill,p,l[0].subscriberId),a(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).Confirmation)},onCancelClick:function(){a(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).PaymentMethod)},collapseHeightDynamic:{mobile:{height:"262px"},tablet:{height:"126px"},desktop:{height:"107px"}},expandHeightDynamic:{mobile:{height:"415px"},tablet:{height:"460px"},desktop:{height:"460px"}},province:m,language:v,userProfileProv:f}))))))},xn=function(e){return{submitMultiOrderPayment:e.submitMultiOrderPayment,validateMultiOrderFormStatus:e.validateMultiOrderFormStatus,tokenizeAndPropagateFormValuesStatus:e.tokenizeAndPropagateFormValuesStatus,interacBankInfo:e.interacBankInfo,validateMultiOrderPayment:e.validateMultiOrderPayment}},In=function(e){return{submitFormOrder:function(t,n,r,i){e(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())({ban:t,type:n,details:r,sub:i}))},setOmnitureOnReview:function(t){e(Me({data:t}))},setOmnitureOnValidationFailure:function(t){return e(ze({data:t}))}}},Ln=(0,v.connect)(xn,In)((0,cn.injectIntl)(Sn)),wn=function(e){var t=e.isModalOpen,n=e.setIsModalOpen,r=e.intl;return m().createElement("div",null,t&&m().createElement(sn.Modal,{id:"cancel-preauth-success-modal","aria-labelledby":"cancel-preauth-success-modal-title",onEscapeKeyPressed:function(){n(!1)}},m().createElement(sn.ModalContent,{useDefaultRadius:!0,className:"payment-rounded-16 payment-max-w-[304px]"},m().createElement(sn.ModalBody,{isDefaultPadding:!0,className:"payment-px-15 sm:payment-px-15 payment-py-15"},m().createElement("div",{className:["payment-flex payment-gap-15 payment-mt-8"].join(" ").trim()},m().createElement(sn.Icon,{className:"payment-text-green payment-text-16",iconClass:"vi_vrui",iconName:"vi_check_c_tk"}),m().createElement("div",{className:"payment-flex payment-flex-col payment-text-12 payment-leading-14","aria-hidden":"true"},m().createElement(sn.Text,{elementType:"p",className:"payment-text-[#131C35] payment-font-normal"}," ",r.formatMessage({id:"SUCCESS_TOAST_MESSAGE"}))),m().createElement("button",{onClick:function(){n(!1),window.location.href="/"},type:"button","aria-label":"Close dialog box",className:"payment-flex payment-rounded-2 focus:payment-outline-blue focus:payment-outline focus:payment-outline-2 focus:payment-outline-offset-3 payment-h-12 payment-ml-auto ",id:"no-name-on-card-close-button"},m().createElement("span",{className:"vi_close vi_vrui vrui-text-16 payment-text-[#131C35]",role:"img","aria-hidden":"true","aria-label":" "}),m().createElement("span",{className:"payment-sr-only"},r.formatMessage({id:"CTA_CLOSE"}))))))))},Dn=(0,cn.injectIntl)(wn),Mn=function(e){var t=e.intl.formatMessage({id:"ALERT_CANCEL_PAYMENT_SUCCESS"});return d.createElement(sn.Alert,{variant:"success",className:"payment-block sm:payment-flex sm:payment-items-center payment-border payment-relative payment-p-16 sm:payment-p-24 payment-rounded-16",iconSize:"32",id:"alert-cancel-confirm-success"},d.createElement(sn.Text,{elementType:"div",className:"payment-pl-0 sm:payment-pl-16 payment-pt-16 sm:payment-pt-0"},d.createElement(sn.Heading,{level:"h2",variant:"xs",className:"payment-font-poppins-Regular payment-leading-19"},d.createElement("span",{className:"payment-text-14 payment-leading-19",dangerouslySetInnerHTML:{__html:t}}))))},Pn=(0,cn.injectIntl)(Mn),kn=function(e){var t=e.intl,n=e.hasMultipleFailure,r=e.children,i=n?t.formatMessage({id:"ALERT_CANCEL_PAYMENT_FAILURES"}):t.formatMessage({id:"ALERT_CANCEL_PAYMENT_FAILURE"}),o=t.formatMessage({id:"ALERT_CANCEL_TRY_AGAIN"});return d.createElement(sn.Alert,{variant:"error",className:"payment-block sm:payment-flex payment-border-none payment-relative payment-p-16 sm:payment-p-24 payment-rounded-16",iconSize:"32",id:"alert-cancel-confirm-fail"},d.createElement(sn.Text,{elementType:"div",className:"payment-pl-0 payment-pt-12 sm:payment-pl-16 sm:payment-pt-0 payment-flex-1"},d.createElement(sn.Heading,{level:"h2",variant:"xs",className:"payment-mb-5 payment-font-poppins-Semibold payment-text-darkblue "},d.createElement("strong",null,i)),d.createElement("div",{className:"payment-mt-12"},r),d.createElement(sn.Button,{onClick:function(){window.location.reload()},className:"payment-mt-24",variant:"solidRed",size:"regular"},o)))},Bn=(0,cn.injectIntl)(kn),Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()),Un=function(e){var t=e.intl,n=e.cancelledBansFailed,r=(e.language,function(){window.location.href="/"}),i=!!(n&&n.length>1),o={MyBill:t.formatMessage({id:"ACCOUNT_TYPENAME_MYBILL"}),Mobility:t.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY"}),OneBill:t.formatMessage({id:"ACCOUNT_TYPENAME_ONEBILL"}),TV:t.formatMessage({id:"ACCOUNT_TYPENAME_TV"}),Internet:t.formatMessage({id:"ACCOUNT_TYPENAME_INTERNET"}),HomePhone:t.formatMessage({id:"ACCOUNT_TYPENAME_HOMEPHONE"}),MobilityAndOneBill:t.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY_ONEBILL"}),SingleBan:t.formatMessage({id:"ACCOUNT_TYPENAME_SINGLEBAN"})};return m().createElement(m().Fragment,null,m().createElement("div",{className:"payment-mt-[60px]"},m().createElement(Bn,{hasMultipleFailure:i},m().createElement(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()),null,n&&n.map(function(e){return m().createElement(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()),{label:"".concat(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())(e.AccountType,e.IsNM1Account,o)),labelDescription:e.NickName,variant:"regErrorBanList"})})))),m().createElement("div",{className:"payment-mt-16"},m().createElement(Pn,null)),m().createElement("div",{className:"payment-my-32 sm:payment-mt-40 payment-pb-[45px]"},m().createElement(sn.Button,{onClick:r,variant:"outlinedBlack",size:"regular"},t.formatMessage({id:"CANCEL_PAYMENT_BACK_TO_MY_ACCOUNT"}))))},Fn=(0,cn.injectIntl)(Un),Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()),jn=function(e){var t,n,r,i,a,u,l,c,s,v,f,p,E,g,h,_=(null===(n=null===(t=e.Config)||void 0===t?void 0:t.getPaymentItem)||void 0===n?void 0:n.length)>1,b=o((0,d.useState)(_?"inactive":"active"),2),y=b[0],C=b[1],N=o((0,d.useState)(":"),2),A=N[0],T=N[1],O=o((0,d.useState)(_?Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).SelectBills:Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).PaymentMethod),2),R=O[0],S=O[1],x=o((0,d.useState)(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())),2),I=x[0],L=x[1],w=o((0,d.useState)(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())),2),D=w[0],M=w[1],P=o((0,d.useState)(!0),2),k=P[0],B=P[1],U=o((0,d.useState)(!1===_?null===(r=e.Config)||void 0===r?void 0:r.getPaymentItem:[]),2),F=U[0],j=U[1],H=o((0,d.useState)(!1===_?[{accountNumber:null===(a=null===(i=e.Config)||void 0===i?void 0:i.getPaymentItem[0])||void 0===a?void 0:a.BanID,subNumber:null===(l=null===(u=e.Config)||void 0===u?void 0:u.getPaymentItem[0])||void 0===l?void 0:l.subscriberId,transactionID:Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())(null===(s=null===(c=e.Config)||void 0===c?void 0:c.getPaymentItem[0])||void 0===s?void 0:s.BanID,null===(v=e.Config)||void 0===v?void 0:v.transactionIdArray),payBalanceAmnt:0}]:[]),2),Y=H[0],z=H[1],V=o((0,d.useState)(!1),2),G=V[0],q=V[1],W=o((0,d.useState)(!1),2),K=W[0],X=W[1],$=o((0,d.useState)(!1),2),Q=$[0],Z=$[1],J=o((0,d.useState)(!1),2),ee=J[0],te=J[1],ne=o((0,d.useState)([]),2),re=ne[0],ie=ne[1],oe=o((0,d.useState)(""),2),ae=oe[0],ue=oe[1],le=e.localization,ce=e.Config,se=e.isLoading,de=function(e){A!==e&&T(e)},me=function(){var e=sessionStorage.getItem("itemsChecked");e&&e.length>0&&sessionStorage.removeItem("itemsChecked")};return(0,d.useEffect)(function(){var t,n,r,i,o,a,u;e.setWidgetstatus("LOADED"),(i=new URLSearchParams(document.location.search.substring(1)).get("code"))&&null!=i?(o=(null===(t=null==ce?void 0:ce.currentUrl)||void 0===t?void 0:t.substring(0,(null===(n=null==ce?void 0:ce.currentUrl)||void 0===n?void 0:n.lastIndexOf("/"))+1))+"PreAuthEdit",window.history.pushState({path:o},"",o),e.getInteracBankInfoAction(i),1===(null===(r=null==ce?void 0:ce.getPaymentItem)||void 0===r?void 0:r.length)&&me()):me(),a=document.getElementById("container"),u=document.getElementsByClassName("myvirgin-preauth-manage"),a&&u.length>0&&Array.from(u).forEach(function(e){e.classList.contains("myvirgin-preauth-manage")&&e.setAttribute("role","presentation")})},[]),(0,d.useEffect)(function(){e.redirectUrlAction({})},[]),p=(0,d.useRef)(A),(0,d.useEffect)(function(){p.current=A}),(0,d.useEffect)(function(){var e,t=ce.pagetitle||"";R===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).Confirmation?(e="".concat(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).Confirmation," | ").concat(t),document.title=h(e)||""):document.title=h(t)||""},[A,R]),E=null===(f=null==ce?void 0:ce.getPaymentItem)||void 0===f?void 0:f.some(function(e){var t;return null!==(t=e.IsOnPreauthorizedPayments)&&void 0!==t&&t}),g="ON"===(null==ce?void 0:ce.IsAutopayCreditEnabled),h=function(e){return(new DOMParser).parseFromString(e,"text/html").documentElement.textContent},m().createElement(cn.IntlProvider,{locale:le.locale,messages:le.messages},m().createElement(m().Fragment,null,se&&m().createElement(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()),null),m().createElement("div",{className:se?"payment-hidden":"",role:"presentation"},m().createElement(sn.Container,null,!G&&!ee&&m().createElement(m().Fragment,null,R!==Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).Confirmation&&m().createElement(m().Fragment,null,m().createElement(fn,{paymentItem:ce.getPaymentItem,isShow:_,onCurrentSteps:de,setCurrentSection:S,currentSection:R,setCheckedBillItems:j,paymentItems:ce.getPaymentItem,setAccountValues:z,accountInputValues:Y,transactionIds:ce.transactionIdArray,managePreauth:ce.selectedUpdatePaymentMethod,isCheckedBan:null==ce?void 0:ce.isCheckedBan}),m().createElement(Rn,{paymentItem:ce.getPaymentItem,isHeadingStepActive:R===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).PaymentMethod?"active":"inactive",isSingleClickEnableForPACC:!1,isSingleClickEnableForPAD:!1,onCurrentSteps:de,setHeadingSteps:C,paymentHeadingStepState:y,setInputValue:L,inputValue:I,setInputBankValue:M,inputBankValue:D,setIsBankSelected:B,setCurrentSection:S,currentSection:R,checkedBillItems:F,bankList:ce.getBankList,accountInputValues:Y,province:ce.province,language:ce.language,isBankSelected:k,creditCardAutopayOffers:ce.creditCardAutopayOffers,debitCardAutopayOffers:ce.debitCardAutopayOffers,removedSubscriberOffers:ce.removedSubscriberOffers,managePreauth:ce.selectedUpdatePaymentMethod,setCancelPreauthSectionClicked:X,cancelPreauthSectionClicked:K,setIsModalOpen:Z,setApiSatusIsFailed:q,isInteractEnabled:ce.IsInteracEnabled,IsAutopayCreditEnabled:g,setSomeCancellationFailed:te,setBansCancellationFailed:ie,interacCode:ae,setInteracCode:ue,isMultiban:_}),!K&&m().createElement(Ln,{isActive:R===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).TermsAndCondition,onCurrentSteps:de,setCurrentSection:S,currentSection:R,checkedBillItems:F,paymentItem:ce.getPaymentItem,province:ce.province,language:ce.language,userProfileProv:ce.userProfileProvince,accountInputValues:Y,setApiSatusIsFailed:q,inputValue:I,isBankSelected:k,interacCode:ae,inputBankValue:D})),R===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).Confirmation&&m().createElement(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()),{paymentItem:ce.getPaymentItem,checkedBillItems:F,checkedCurrentBalanceItems:[],showPaymentSummary:!0,isNewbank:!1,isPreauth:E,inputValue:I,isShow:R===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).Confirmation,inputBankValue:D,isBankPaymentSelected:k,BankList:ce.getBankList,showCurrentBalance:!1,language:ce.language,accountInputValues:Y,currentSection:R,notOptedBalanceItems:[],IsAutopayCreditEnabled:g,creditCardAutopayOffers:ce.creditCardAutopayOffers,debitCardAutopayOffers:ce.debitCardAutopayOffers,bankitems:[],setApiSatusIsFailed:q,apiSatusIsFailed:G,managePreauth:null==ce?void 0:ce.selectedUpdatePaymentMethod,interacCode:ae})),G&&m().createElement(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()),null),m().createElement(Dn,{isModalOpen:Q,setIsModalOpen:Z}),ee&&re&&re.length>0&&m().createElement(Fn,{cancelledBansFailed:re,language:ce.language})))))},Hn=function(e,t){return{localization:e.localization,Config:t.Config,isLoading:e.isLoading,cancelPreauthStatus:e.cancelPreauthStatus,cancelPreauthPayments:e.cancelPreauthPayments}},Yn=function(e){return{redirectUrlAction:function(){e(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())({}))},getInteracBankInfoAction:function(t){e(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())({code:t}))},setWidgetstatus:function(t){e(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())({type:t}))}}},zn=(0,v.connect)(Hn,Yn)(jn),Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()),Vn=function(e){function t(t,n,r){var i=e.call(this)||this;return i.store=t,i.config=n,i.logger=r,i}return n(t,e),t.prototype.init=function(){var e,t;this.config.setConfig(f.LoggerConfigKeys.SeverityLevel,this.config.logLevel),this.store.dispatch(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())({ban:this.config.getPaymentItem[0].BanID,sub:this.config.getPaymentItem[0].subscriberId})),null!=this.config.getPaymentItem&&1===this.config.getPaymentItem.length&&(t=[{accountNumber:(e=this.config.getPaymentItem[0]).BanID,subNumber:e.subscriberId,transactionID:Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())(e.BanID,this.config.transactionIdArray),payBalanceAmnt:0}],this.store.dispatch(Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}())({ban:e.BanID,type:e.AccountType===Object(function(){var e=new Error("Cannot find module 'myvirgin-preauth-common'");throw e.code="MODULE_NOT_FOUND",e}()).OneBill,details:t,sub:e.subscriberId})))},t.prototype.destroy=function(){this.store.destroy()},t.prototype.render=function(e){var t=this.store,n=this.config;e.render(d.createElement(v.Provider,{store:t},d.createElement(zn,{Config:n})))},r([(0,f.Widget)({namespace:"Preauth/Manage"}),i("design:paramtypes",[ln,C,f.Logger])],t)}(f.ViewWidget),Gn=Vn}(),t}())},function(e){"use strict";e.exports=r},function(e){"use strict";e.exports=i},function(e){"use strict";e.exports=o},function(e){"use strict";e.exports=a},function(e){"use strict";e.exports=u},function(e){"use strict";e.exports=l}],m={};return c.d=function(e,t){for(var n in t)c.o(t,n)&&!c.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},c.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},c.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},s={},function(){"use strict";function e(e,t,r){var u=n(n({},e),{"loader.staticWidgetMappings":{"myvirgin-preauth-manage":{factory:function(){return c(4)},namespace:"Preauth/Manage"}}});(0,a.Init)(u),o.render(i.createElement(a.WidgetLoader,{widget:"myvirgin-preauth-manage"}),document.getElementById(t))}var t,n,r,i,o,a;c.r(s),c.d(s,{initialize:function(){return e}}),t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},t(e,n)},n=function(){return n=Object.assign||function(e){var t,n,r,i;for(n=1,r=arguments.length;n<r;n++)for(i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},n.apply(this,arguments)},Object.create,Object.create,r=function(e){return r=Object.getOwnPropertyNames||function(e){var t,n=[];for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[n.length]=t);return n},r(e)},"function"==typeof SuppressedError&&SuppressedError,i=c(1),o=c(2),a=c(3)}(),s}()},"object"==typeof exports&&"object"==typeof module?module.exports=factory(require("react"),require("react-dom"),require("bwtk"),require("react-redux"),require("redux"),require("redux-actions"),require("redux-observable"),require("rxjs"),require("react-intl")):"function"==typeof define&&define.amd?define("Bundle",["react","react-dom","bwtk","react-redux","redux","redux-actions","redux-observable","rxjs","react-intl"],factory):"object"==typeof exports?exports.Bundle=factory(require("react"),require("react-dom"),require("bwtk"),require("react-redux"),require("redux"),require("redux-actions"),require("redux-observable"),require("rxjs"),require("react-intl")):this.Bundle=factory(this.React,this.ReactDOM,this.bwtk,this.ReactRedux,this.Redux,this.ReduxActions,this.ReduxObservable,this.rxjs,this.ReactIntl);
//# sourceMappingURL=myvirgin-preauth-manage-bundle-bundle.min.js.map