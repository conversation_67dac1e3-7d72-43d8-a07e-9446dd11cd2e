import * as React from "react";
import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ton, Link, Icon, Text, Price} from "@virgin/virgin-react-ui-library";
import AlertNotificationListItem from "./AlertNotificationListItem";
import AlertNotificationList from "./AlertNotificationList";
import { injectIntl } from "react-intl";
// import { AccountInputValues, PaymentItem } from "../../models";


// import { Alert, Heading, Button } from "@bell/@virgin/virgin-react-ui-library";


const alertErrorFormList = [
  { label: "Account holder name", labelDescription: "This information is required." }, 
  { label: "Transit number", labelDescription: "This information is required." },
  { label: "Bank name", labelDescription: "This information is required." },
  { label: "Account number", labelDescription: "This information is required." },
];


interface AlertNotificationProps {
  intl: any;
  children?: React.ReactNode;

} 

/* Sample Components usecases to use in other alerts*/
/* ERROR ALERTS*/

export const AlertErrorFormList =  injectIntl(({ intl }:AlertNotificationProps) => (
  <Alert 
    variant="error" 
    className="payment-bg-pink payment-block sm:payment-flex payment-px-16 sm:payment-px-24 payment-py-16 payment-border-x-0 sm:payment-border-x-1 payment-border-y-1 payment-relative payment-rounded-16 sm:payment-rounded-16" 
    iconSize="36"
    id="alert-3">
    <Text elementType="div" className="payment-pl-0 payment-pt-10 sm:payment-pl-15 sm:payment-pt-0">
      <Heading level="h3" variant="xs" className="payment-text-darkbluepayment-mb-15 payment-leading-22">
        <span aria-hidden="true">{intl.formatMessage({id: "ALERT_ERROR_HEADING"})}</span>
        <span className="payment-sr-only">{intl.formatMessage({id: "ALERT_ERROR_HEADING_SR"})}</span>
      </Heading>
      <div>
        <AlertNotificationList>
          {alertErrorFormList.map((item) => (
            <AlertNotificationListItem label={item.label} labelDescription={item.labelDescription} variant="errorList"/> 
          ))}
        </AlertNotificationList>
      </div>
    </Text>
  </Alert>
));

export const AlertErrorWithAccountPrice = injectIntl(({children, intl}:AlertNotificationProps) => (
  <Alert
    variant="error" 
    className="payment-border payment-relative sm:payment-py-30 sm:payment-px-30 payment-rounded-16 payment-p-16 payment-px-15 payment-py-30 sm:payment-flex payment-block" 
    iconSize="36"
    id="alert-2">
    <Text elementType="div" className="payment-pl-0 payment-pt-15 sm:payment-pl-16 sm:payment-pt-0 payment-flex-1">
      <Heading level="h2" variant="xs" className="payment-mb-5 payment-font-sans payment-text-red ">        
        {intl.formatMessage({id: "ALERT_ERROR_HEADING_SOME_BALANCE"})}                
      </Heading>
      <div>
        {children}
      </div>
      <Button className="payment-mt-30" variant="solidRed" size="regular">
        {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_BUTTON_DESC"})}
      </Button>          
    </Text>
  </Alert>
));


export const AlertErrorForm = injectIntl(({children, intl}:AlertNotificationProps) => (
  <Alert 
    variant="error" 
    className="payment-bg-pink payment-block sm:payment-flex payment-px-16 sm:payment-px-24 sm:payment-py-24 payment-py-16 payment-relative payment-rounded-16 sm:payment-rounded-16" 
    iconSize="32"
    id="alert-3"
    aria-labelledby="error-alert-1 error-alert-2 error-alert-3">
    <Text elementType="div" className="payment-pl-0 payment-pt-10 sm:payment-pl-15 sm:payment-pt-0">
      <Heading id="error-alert-1" level="h3" variant="xs" className="payment-mb-15 payment-leading-22">
        <span aria-hidden="true">{intl.formatMessage({id: "ALERT_ERROR_HEADING"})}</span>
        <span className="payment-sr-only">{intl.formatMessage({id: "ALERT_ERROR_HEADING_SR"})}</span>
      </Heading>
      <div>
        {children}
      </div>
    </Text>
  </Alert>
));


export const AlertErrorPACCSucessOTPFail = injectIntl(({intl}:AlertNotificationProps) =>  (
  <Alert
    variant="error" 
    className="payment-border payment-relative sm:payment-py-30 sm:payment-px-30 payment-rounded-16 payment-p-16 payment-px-15 payment-py-30 sm:payment-flex payment-block" 
    iconSize="36"
    id="alert-2">
    <Text elementType="div" className="payment-pl-0 payment-pt-15 sm:payment-pl-16 sm:payment-pt-0">
      <Heading level="h2" variant="xs" className="payment-mb-12 payment-text-red payment-font-sans">
        <strong>Your balance of $195.45 was not paid</strong> due to an error processing your request. 
               
      </Heading>  
      <p className="payment-text-14 payment-my-15 payment-text-gray">
        A separate one-time payment must be made to pay this balance, or risk late fees.
      </p>     
      <Button variant="solidRed" size="regular">
        Make a Payment
      </Button>
    </Text>
  </Alert>
));


/* INFO ALERTS*/

export const AlertInfoSingleButton = injectIntl(({intl}:AlertNotificationProps) => (
  <Alert 
    variant="info" 
    className="payment-border payment-relative sm:payment-py-30 sm:payment-px-30 payment-rounded-16 payment-p-16 payment-px-15 payment-py-30 sm:payment-flex payment-block" 
    iconSize="36"
    id="alert-3">
    <Text elementType="div" className="payment-pl-0 payment-pt-15 sm:payment-pl-16 sm:payment-pt-0">
      <Heading level="h2" variant="xs" className="payment-mb-12 payment-font-sans payment-font-bold payment-leading-22">               
        {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_HEADING"})}
      </Heading>
      <p className="payment-text-14 payment-my-15 payment-text-gray">               
        {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_DESC"})}            
      </p>
      <Text elementType="div" className="sm:payment-flex payment-block">
        <Text elementType="div" className="payment-pr-0 sm:payment-pr-10">
          <Button variant="solidRed" size="regular">               
            {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_BUTTON_DESC"})}     
          </Button>
        </Text>
      </Text>
    </Text>
  </Alert>
));



/* SUCCESS ALERTS*/


export const AlertSuccessWithCheckIcon = injectIntl(({intl}:AlertNotificationProps) => (
        
  <Alert
    variant="success" 
    className="payment-border payment-relative sm:payment-py-30 sm:payment-px-30 payment-rounded-16 payment-p-16 payment-px-15 payment-py-30 sm:payment-flex payment-block" 
    iconSize="36"
    id="alert-3">
    <Text elementType="div" className="payment-pl-0 payment-pt-15 sm:payment-pl-16 sm:payment-pt-0">
      <Heading level="h2" variant="xs" className="payment-mb-15 payment-font-sans payment-leading-22">             
        {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_HEADING"})}
      </Heading>
      <Text elementType="div" role="list" className="payment-text-14 payment-mb-30 payment-text-black payment-ml-5">
        <Text elementType="div" role="listitem" className="payment-flex payment-items-start payment-mb-15"> 
          <Icon className="payment-text-20 payment-text-blue" iconClass="bi_check_light" iconName="icon.bi_check_small_flat_fin" />                   
          <span className="payment-text-16 payment-leading-20 payment-ml-10">{intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_MESSAGE"})}</span>
        </Text>
        <Text elementType="div" role="listitem" className="payment-flex payment-items-start payment-mb-15 payment-text-black"> 
          <Icon className="payment-text-20 payment-text-blue" iconClass="bi_check_light" iconName="icon.bi_check_small_flat_fin" /> 
          <span className="payment-text-16 payment-leading-20 payment-ml-10">{intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})}</span>
        </Text>
      </Text>
      <p className="payment-text-14 payment-mb-5 payment-text-gray payment-leading-18">
        {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})} <strong>000011</strong>
      </p>
      <p className="payment-text-14 payment-text-gray payment-leading-18">              
        {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_DESC"})}
        <Link variant="textBlue" size="small" href="https://www.bell.ca/" className=""> {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_DESC_LINK"})}</Link>
      </p>      
    </Text>
  </Alert>
));

export const AlertSuccessWithCheckIconPreAuth = injectIntl(({intl}:AlertNotificationProps) => (
  <Alert
    variant="success" 
    className="payment-border payment-relative sm:payment-py-30 sm:payment-px-30 payment-rounded-16 payment-p-16 payment-px-15 payment-py-30 sm:payment-flex payment-block" 
    iconSize="36"
    id="alert-3">
    <Text elementType="div" className="payment-pl-0 payment-pt-15 sm:payment-pl-16 sm:payment-pt-0">
      <Heading level="h2" variant="xs" className="payment-mb-15 payment-font-sans payment-leading-22">             
        {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_HEADING"})}
      </Heading>
      <Text elementType="div" role="list" className="payment-text-14 payment-mb-30 payment-text-black payment-ml-5">
        <Text elementType="div" role="listitem" className="payment-flex payment-items-start payment-mb-15"> 
          <Icon className="payment-text-20 payment-text-blue" iconClass="bi_check_light" iconName="icon.bi_check_small_flat_fin" />                   
          <span className="payment-text-16 payment-leading-20 payment-ml-10">{intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_MESSAGE"})}</span>
        </Text>
      </Text>
      <p className="payment-text-14 payment-mb-5 payment-text-gray payment-leading-18">
        {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})} <strong>000011</strong>
      </p>
      <p className="payment-text-14 payment-text-gray payment-leading-18">              
        {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_DESC"})}
        <Link variant="textBlue" size="small" href="https://www.bell.ca/" className=""> {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_DESC_LINK"})}</Link>
      </p>      
    </Text>
  </Alert>
));

export const AlertSuccessWithCheckIconOptedIn = injectIntl(({intl}:AlertNotificationProps) => (
  <Alert
    variant="success" 
    className="payment-border payment-relative sm:payment-py-30 sm:payment-px-30 payment-rounded-16 payment-p-16 payment-px-15 payment-py-30 sm:payment-flex payment-block" 
    iconSize="36"
    id="alert-3">
    <Text elementType="div" className="payment-pl-0 payment-pt-15 sm:payment-pl-16 sm:payment-pt-0">
      <Heading level="h2" variant="xs" className="payment-mb-15 payment-font-sans payment-leading-22">             
        {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_HEADING"})}
      </Heading>
      <Text elementType="div" role="list" className="payment-text-14 payment-mb-30 payment-text-black payment-ml-5">
        <Text elementType="div" role="listitem" className="payment-flex payment-items-start payment-mb-15"> 
          <Icon className="payment-text-12 payment-text-blue payment-mt-3" iconClass="bi_check_light" iconName="bi_check_light" /> 
          <span className="payment-text-16 payment-leading-20 payment-ml-10">{intl.formatMessage({id: "ALERT_CONFIRMATION_CURRENT_BALANCE"})}
          </span>
        </Text>
        <Text elementType="div" role="listitem" className="payment-flex payment-items-start payment-mb-15"> 
          <Icon className="payment-text-20 payment-text-blue" iconClass="bi_check_light" iconName="icon.bi_check_small_flat_fin" />                   
          <span className="payment-text-16 payment-leading-20 payment-ml-10">{intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_MESSAGE"})}</span>
        </Text>
      </Text>
      <p className="payment-text-14 payment-mb-5 payment-text-gray payment-leading-18">
        {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})} <strong>000011</strong>
      </p>
      <p className="payment-text-14 payment-text-gray payment-leading-18">              
        {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_DESC"})}
        <Link variant="textBlue" size="small" href="https://www.bell.ca/" className=""> {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_DESC_LINK"})}</Link>
      </p>         
    </Text>
  </Alert>
));

export const AlertSuccessWithCheckIconCreditIncreased = injectIntl(({intl}:AlertNotificationProps) => (
  <Alert
    variant="success" 
    className="payment-border payment-relative sm:payment-py-30 sm:payment-px-30 payment-rounded-16 payment-p-16 payment-px-15 payment-py-30 sm:payment-flex payment-block" 
    iconSize="36"
    id="alert-3">
    <Text elementType="div" className="payment-pl-0 payment-pt-15 sm:payment-pl-16 sm:payment-pt-0">
      <Heading level="h2" variant="xs" className="payment-mb-15 payment-font-sans payment-leading-22">             
        {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_HEADING"})}
      </Heading>
      <Text elementType="div" role="list" className="payment-text-14 payment-mb-30 payment-text-black payment-ml-5">
        <Text elementType="div" role="listitem" className="payment-flex payment-items-start payment-mb-15"> 
          <Icon className="payment-text-20 payment-text-blue" iconClass="bi_check_light" iconName="icon.bi_check_small_flat_fin" />                   
          <span className="payment-text-16 payment-leading-20 payment-ml-10">{intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_MESSAGE"})}</span>
        </Text>
        <Text elementType="div" role="listitem" className="payment-flex payment-items-start payment-mb-15 payment-text-black"> 
          <Icon className="payment-text-20 payment-text-blue" iconClass="bi_check_light" iconName="icon.bi_check_small_flat_fin" /> 
          <span className="payment-text-16 payment-leading-20 payment-ml-10">{intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})}</span>
        </Text>
      </Text>
      <p className="payment-text-14 payment-mb-5 payment-text-gray payment-leading-18">
        {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})} <strong>000011</strong>
      </p>
      <p className="payment-text-14 payment-text-gray payment-leading-18">              
        {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_DESC"})}
        <Link variant="textBlue" size="small" href="https://www.bell.ca/" className=""> {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_DESC_LINK"})}</Link>
      </p>      
    </Text>
  </Alert>
));


/* WARNING ALERTS*/


export const AlertWarningWithAccountPrice = injectIntl(({intl,children}:AlertNotificationProps) =>  (
  <Alert
    variant="warning" 
    className="payment-border payment-relative sm:payment-py-30 sm:payment-px-30 payment-rounded-16 payment-p-16 payment-px-15 payment-py-30 sm:payment-flex payment-block" 
    iconSize="36">
    <Text elementType="div" className="payment-pl-0 payment-pt-15 sm:payment-pl-16 sm:payment-pt-0">
      <Heading level="h2" variant="xs" className="payment-mb-5 payment-font-sans payment-font-bold">
        {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_HEADING"})}
      </Heading>
      {/* <p className="payment-text-14 payment-mb-10 payment-text-gray">
                Pre-authorized payments will only begin on your next billing period. You must pay the following balance(s) in a separate and final one-time payment, or risk late fees:
                </p> */}
      <div>{children}</div>   
      <Button className="payment-mt-30" variant="solidRed" size="regular">
        {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_BUTTON_DESC"})}  
      </Button>
    </Text>
  </Alert>
));


export const AlertWarningWithPrice = injectIntl(({intl}:AlertNotificationProps) =>  (
  <Alert
    variant="warning" 
    className="payment-border payment-relative sm:payment-py-30 sm:payment-px-30 payment-rounded-16 payment-p-16 payment-px-15 payment-py-30 sm:payment-flex payment-block" 
    iconSize="36">
    <Text elementType="div" className="payment-pl-0 payment-pt-15 sm:payment-pl-16 sm:payment-pt-0">
      <Heading level="h2" variant="xs" className="payment-mb-0 sm:payment-mb-12 payment-font-sans payment-font-bold payment-leading-22">
        {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_HEADING"})}
      </Heading>
      <p className="payment-leading-18 payment-text-14 payment-mt-5 payment-mb-15 sm:payment-mt-0 sm:payment-mb-0 sm:payment-my-15 payment-text-gray">
        {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_DESC_1"})} 
        <Price 
          language="en"
          showZeroDecimalPart
          price={195.45}
          variant="defaultPrice"
          className="!payment-text-14 payment-leading-14 payment-m-5 payment-font-normal payment-inline-block"/>
        <span className="payment-sr-only">195 point 45 dollars</span> {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_DESC_2"})}  
      </p>
      <Text elementType="div" className="sm:payment-flex payment-block">
        <Text elementType="div" className="payment-pr-0 sm:payment-pr-10">
          <Button variant="solidRed" size="regular">
            {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_BUTTON_DESC"})}   
          </Button>
        </Text>
      </Text>
    </Text>
  </Alert>
));


export const AlertWarningWithSomeBalancesPaid = injectIntl(({intl}:AlertNotificationProps) =>  (
  <Alert
    variant="warning" 
    className="payment-border payment-relative sm:payment-py-30 sm:payment-px-30 payment-rounded-16 payment-p-16 payment-px-15 payment-py-30 sm:payment-flex payment-block" 
    iconSize="36">
    <Text elementType="div" className="payment-pl-0 payment-pt-15 sm:payment-pl-16 sm:payment-pt-0">
      <Heading level="h2" variant="xs" className="payment-mb-15 payment-font-sans payment-font-bold">
        {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_HEADING"})}
      </Heading>
      <p className="payment-text-14 payment-mb-10 payment-text-gray">                
        {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_DESC"})}              
      </p>
      <Text elementType="div" className="payment-mb-30 payment-mt-15">
        <Text elementType="div" className="payment-flex payment-justify-between sm:payment-justify-normal">
          <label className="payment-text-14 sm:payment-basis-1/4">
            <strong>1234567890</strong>
          </label>
          <Price 
            language="en"
            showZeroDecimalPart
            price={206.98}
            variant="defaultPrice"
            className="!payment-text-14 payment-leading-14 payment-m-5 payment-font-normal"/>
        </Text>      
      </Text>                     
      <Button variant="solidRed" size="regular">               
        {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_BUTTON_DESC"})}     
      </Button>
    </Text>
  </Alert>
));
