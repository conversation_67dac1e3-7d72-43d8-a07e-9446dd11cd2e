{
  "extends": "../node_modules/webpack-common/lib/tsconfig/widget.json",
  "compilerOptions": {
    "baseUrl": "../node_modules/@types",
    "skipLibCheck": true,
    "strict": false,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "noImplicitAny": false,
  },
  "exclude": [
    "../dist",
    "node_modules",
    "./node_modules",
    "./node_modules/*",
    "./node_modules/@types/prop-types/index.d.ts",
  ],
 "include": [
    "./**/*.tsx",
    "./**/*.ts",
    "../**/*.d.ts"
  ]
}