import React, {useState, useEffect, useRef} from "react";

import { LocalizationState } from "bwtk";
import { IntlProvider } from "react-intl";
import { IStoreState } from "./store/Store";
import { connect } from "react-redux";
import {Container} from "@virgin/virgin-react-ui-library";
import { SubscriberOffersWithBan } from "./models";
import {CurrentBalance, SelectBills
  // CurrentBalance
} from "./views/CheckboxCard";
import {PaymentMethod} from "./views/PaymentMethod";
import {TermsAndCondition} from "./views/TermsAndCondition";
import { IAppOwnProps } from "./models/App";
import Config from "./Config";
import defaultBankInputValue, {
  // AccountInputValues,
  Confirmation,
  CurrentSection,
  getBanSpecificTransactionId,
  PaymentItem,
  PaymentItemAccountType,
  PageTitleCurrentSection,
  defaultCreditCardInputValue,
  getInteracBankInfo, 
  getRedirectUrl,
  Loader,
  APIFailure,
  widgetStatusAction
} from "myvirgin-preauth-common";



interface IAppState {
  error: string | null;
  paymentItem: PaymentItem[];
  currentSteps: any;
  isLoading: boolean;
  bankitems: any[];
  language: "en" | "fr";
  localization: LocalizationState;
  Config: Config;
  redirectUrlAction: Function;
  getInteracBankInfoAction: Function;
  setWidgetstatus: Function;
  creditCardAutopayOffers: SubscriberOffersWithBan[];
  debitCardAutopayOffers: SubscriberOffersWithBan[];
}


export interface IAppDispatchToProps {

}


export interface IAppMergedProps extends IAppOwnProps, IAppState, IAppDispatchToProps {

}

const AppComponent: React.FC<IAppMergedProps> = (props: IAppMergedProps) => {
  const enableSelectBills = props.Config?.getPaymentItem?.length >1 ? true : false;
  const [paymentHeadingStepState, setPaymentHeadingStepState] = useState(enableSelectBills ? "inactive" : "active");
  const [currentSteps, setCurrentSteps] = useState(":");
  const [currentSection, setCurrentSection] = useState(enableSelectBills ? CurrentSection.SelectBills : CurrentSection.PaymentMethod);
  const [creditCardInputValue, setCreditCardInputValue] = useState(defaultCreditCardInputValue);
  const [BankInputValue, setBankInputValue] = useState(defaultBankInputValue);
  const [isBankSelected, setIsBankSelected] = useState(true);
  const [checkedBillItems, setCheckedBillItems] = useState(enableSelectBills === false ? props.Config?.getPaymentItem : []);
  const [accountInputValues, setAccountInputValues] = useState(enableSelectBills === false
    ? [{
      accountNumber: props.Config?.getPaymentItem[0]?.BanID,
      subNumber: props.Config?.getPaymentItem[0]?.subscriberId, 
      transactionID: getBanSpecificTransactionId(props.Config?.getPaymentItem[0]?.BanID, props.Config?.transactionIdArray),
      payBalanceAmnt: 0,
    }]
    : [],
  );

  const [interacCode, setInteracCode] = useState("");
  const [checkedCurrentBalanceItems, setCheckedCurrentBalanceItems] = useState([]);
  const [notOptedBalanceItems, setNotOptedBalanceItems] = useState([]);
  const [apiSatusIsFailed, setApiSatusIsFailed] = useState(false);

  const {localization, Config, isLoading} = props;
  
  const onCurrentSteps = (step: any) => {
    if (currentSteps !== step) {
      setCurrentSteps(step);
    }
  };

  const getQueryParameter = (param: string) => new URLSearchParams(document.location.search.substring(1)).get(param);

  const removeSessionStorageCheckedItems = () => {
    const items = sessionStorage.getItem("itemsChecked");
    if (items && items.length > 0) {
      sessionStorage.removeItem("itemsChecked");
    }
  };
 
  useEffect(() => {
    props.setWidgetstatus("LOADED");

    const code = getQueryParameter("code");
    if (code && code != null) {
      const newUrl = Config?.currentUrl?.split("&amp;").join("&") || '';
      window.history.pushState({ path: newUrl }, '', newUrl);
      props.getInteracBankInfoAction(code);
      
      if (Config?.getPaymentItem?.length === 1) {
        removeSessionStorageCheckedItems();
      }
    }
    else {
      removeSessionStorageCheckedItems();
      props.redirectUrlAction({});
    }
    const container = document.getElementById("container");
    const children = document.getElementsByClassName("myvirgin-preauth-setup");
    
    if (container && children.length > 0) {
      Array.from(children).forEach(child => {
        // Ensure we're adding the class to the <span> itself, not its children
        if (child.classList.contains("myvirgin-preauth-setup")) {
          child.setAttribute("role", "presentation");
        }
      });
    }
  }, []);

  const prevStepsRef = useRef(currentSteps);

  useEffect(() => {
    prevStepsRef.current = currentSteps;
  });

  useEffect(() => {
    const pageTitle = Config.pagetitle || "";

    if (currentSection === CurrentSection.Confirmation) {
      const pageConfimationTitle = `${PageTitleCurrentSection.Confirmation} | ${pageTitle}`;
      document.title = parseDOMString(pageConfimationTitle) || "";
    } else {
      document.title = parseDOMString(pageTitle) || "";
    }

  }, [currentSteps, currentSection]);
  const IsSingleClickEnabled = Config?.IsSingleClickEnabled === "ON" ? true :false ;
  const IsAutopayCreditEnabled = Config?.IsAutopayCreditEnabled === "ON" ? true : false ;
  let enableSingleClickForPAD = Config?.getPaymentItem?.some(item => item.Due > 0 && item.isOneTimePaymentEligible && item.AccountType !== PaymentItemAccountType.OneBill && !item.isLastBillOnPreauth) ?? false;
  let enableSingleClickForPACC = Config?.getPaymentItem?.some(item => item.Due > 0 && item.isOneTimeCreditCardPaymentEnabled && item.AccountType !== PaymentItemAccountType.OneBill && !item.isLastBillOnPreauth) ?? false;
  const isPreauth: boolean = Config?.getPaymentItem?.some(item => item.IsOnPreauthorizedPayments ?? false);
  const checkedBillItemsHasBalance = checkedBillItems?.some((x: PaymentItem) => x.Due > 0 && x.AccountType !== PaymentItemAccountType.OneBill && !x.isLastBillOnPreauth) ?? false;
  
  enableSingleClickForPAD = enableSingleClickForPAD && checkedBillItemsHasBalance && IsSingleClickEnabled;
  enableSingleClickForPACC = enableSingleClickForPACC && checkedBillItemsHasBalance && IsSingleClickEnabled;
  
  const parseDOMString = (e: string) =>  // Convert Spacial Characters specially for French word.
    new DOMParser().parseFromString(
      e,
      "text/html"
    ).documentElement.textContent;
  

  return (
    <IntlProvider
      locale={localization.locale}
      messages={localization.messages}
    >
      {isLoading ? <Loader /> :
        <Container>
          {!apiSatusIsFailed &&
            <>
              {currentSection !== CurrentSection.Confirmation &&
                <>
                
                  <SelectBills
                    paymentItem={Config.getPaymentItem}
                    isShow={enableSelectBills}
                    onCurrentSteps={onCurrentSteps}
                    setCurrentSection={setCurrentSection}
                    currentSection={currentSection}
                    setCheckedBillItems={setCheckedBillItems}
                    paymentItems={Config.getPaymentItem}
                    setAccountValues={setAccountInputValues}
                    accountInputValues={accountInputValues}
                    transactionIds={Config.transactionIdArray}
                  />

                  <PaymentMethod paymentItem={Config.getPaymentItem}
                    isHeadingStepActive={currentSection === CurrentSection.PaymentMethod ? "active" : "inactive"}
                    isSingleClickEnableForPACC={enableSingleClickForPAD}
                    isSingleClickEnableForPAD={enableSingleClickForPACC}
                    onCurrentSteps={onCurrentSteps}
                    setHeadingSteps={setPaymentHeadingStepState}
                    paymentHeadingStepState={paymentHeadingStepState}
                    setInputValue={setCreditCardInputValue}
                    inputValue={creditCardInputValue}
                    setInputBankValue={setBankInputValue}
                    inputBankValue={BankInputValue}
                    setIsBankSelected={setIsBankSelected}
                    setCurrentSection={setCurrentSection}
                    currentSection={currentSection}
                    checkedBillItems={checkedBillItems}
                    bankList={Config.getBankList}
                    accountInputValues={accountInputValues}
                    language={Config.language as "en" | "fr"}
                    isBankSelected={isBankSelected}
                    interacCode={interacCode}
                    creditCardAutopayOffers={Config.creditCardAutopayOffers}
                    debitCardAutopayOffers={Config.debitCardAutopayOffers}
                    IsAutopayCreditEnabled={IsAutopayCreditEnabled}
                    isInteractEnabled = {Config.IsInteracEnabled}
                    setInteracCode={setInteracCode}
                    isMultiban={enableSelectBills}
                  />

                  {(enableSingleClickForPAD || enableSingleClickForPACC) && 
                  <CurrentBalance 
                    paymentItem={Config.getPaymentItem} 
                    checkedBillItems={checkedBillItems}
                    checkedCurrentBalanceItems={checkedCurrentBalanceItems}
                    setCheckedCurrentBalanceItems={setCheckedCurrentBalanceItems}
                    onCurrentSteps={onCurrentSteps}
                    setCurrentSection={setCurrentSection}
                    currentSection={currentSection}
                    language={Config.language as "en" | "fr"}
                    setAccountValues={setAccountInputValues}
                    accountInputValues={accountInputValues}
                    transactionIds={Config.transactionIdArray}
                    isBankPaymentSelected={isBankSelected}
                    setNotOptedBalanceItems={setNotOptedBalanceItems}
                    inputBankValue = {BankInputValue}
                    interacCode={interacCode}
                    apiSatusIsFailed={apiSatusIsFailed}
                  />
                  }
                  
                  <TermsAndCondition 
                    isActive={currentSection === CurrentSection.TermsAndCondition}  
                    onCurrentSteps={onCurrentSteps}
                    setCurrentSection={setCurrentSection}
                    currentSection={currentSection}
                    checkedBillItems={checkedBillItems}
                    paymentItem={Config.getPaymentItem}
                    province={Config.province}
                    language={Config.language}
                    userProfileProv={Config.userProfileProvince}
                    accountInputValues={accountInputValues}
                    interacCode={interacCode}
                    inputBankValue = {BankInputValue}
                    isBankSelected={isBankSelected}
                    setApiSatusIsFailed={setApiSatusIsFailed}
                  />
                </>  
              }

              {currentSection === CurrentSection.Confirmation &&
                <Confirmation
                  paymentItem={Config.getPaymentItem} 
                  checkedBillItems={checkedBillItems} 
                  checkedCurrentBalanceItems={checkedCurrentBalanceItems}
                  showPaymentSummary={true}
                  isNewbank={false} 
                  isPreauth={isPreauth}
                  inputValue={creditCardInputValue} 
                  isShow={currentSection === CurrentSection.Confirmation}
                  inputBankValue = {BankInputValue}
                  isBankPaymentSelected = {isBankSelected}
                  BankList={Config.getBankList}
                  showCurrentBalance={enableSingleClickForPAD || enableSingleClickForPACC}
                  language={Config.language as "en" | "fr"}
                  accountInputValues={accountInputValues}
                  currentSection={currentSection}
                  notOptedBalanceItems={notOptedBalanceItems}
                  setApiSatusIsFailed={setApiSatusIsFailed}  
                  creditCardAutopayOffers={Config.creditCardAutopayOffers}
                  debitCardAutopayOffers={Config.debitCardAutopayOffers}
                  bankitems={[]}   
                  apiSatusIsFailed={apiSatusIsFailed} 
                  interacCode={interacCode}             
                />
              }
              
            </>
          }
          {apiSatusIsFailed &&
            <APIFailure></APIFailure>
          }
        </Container>
         
      }
    </IntlProvider >
  );
};


const mapStateToProps = (state: IStoreState, ownProps: IAppOwnProps) => ({
  localization: state.localization,
  Config: ownProps.Config,
  isLoading: state.isLoading
});

const mapDispatchToProps = (dispatch: React.Dispatch<any>) => ({
  redirectUrlAction: () => { dispatch(getRedirectUrl({})); },
  getInteracBankInfoAction: (code: string) => { dispatch(getInteracBankInfo({ code })); },
  setWidgetstatus: (type: string) => { dispatch(widgetStatusAction({ type })); }
});

export const App = connect<
  IStoreState,
  IAppOwnProps
>(
  mapStateToProps as any,
  mapDispatchToProps as any
)(AppComponent);
