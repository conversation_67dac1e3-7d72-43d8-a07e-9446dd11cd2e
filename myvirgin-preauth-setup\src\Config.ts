
import { Injectable, CommonFeatures, LoggerSeverityLevel } from "bwtk";
import { PaymentItem } from "myvirgin-preauth-common";
import { DTSTokenization } from "./models/DTSTokenization";
import { SelectListItem, TransactionIdItems, SubscriberOffersWithBan } from "./models";



const { BaseConfig, configProperty } = CommonFeatures;
@Injectable
export default class Config extends BaseConfig<Config> {
  @configProperty("en") 
  language: string;

  @configProperty(LoggerSeverityLevel.All)
  logLevel: LoggerSeverityLevel;

  @configProperty("/UXP.Services/eCare/Ordering/Mobility/Onebill/OrderForm/PreAuthorizePayment")
  createPaymentURL: string;

  @configProperty("/UXP.Services/eCare/Ordering/Mobility/Onebill/OrderMultiForm/PreAuthorizePayment")
  createMultiPaymentURL: string;

  @configProperty("B")
  brand: string;

  @configProperty("BELLCAEXT")
  channel: string;

  @configProperty("ON")
  province: string;

  @configProperty("b14bc3rcGo")
  userID: string;

  @configProperty("")
  CSRFToken: string;

  @configProperty("")
  getPaymentItem: PaymentItem[];

  @configProperty("")
  pagetitle: string;

  @configProperty("")
  transactionId: string;

  @configProperty("")
  DTSTokenization: DTSTokenization;

  @configProperty("")
  paymentApiUrl: string;

  @configProperty("")
  getBankList: SelectListItem[];

  @configProperty("")
  transactionIdArray: TransactionIdItems[];

  @configProperty("")
  RedirectUrl: string;

  @configProperty("")
  BankInfoUrl: string;

  @configProperty("")
  currentUrl: string;

  @configProperty("")
  creditCardAutopayOffers: SubscriberOffersWithBan[];

  @configProperty("")
  debitCardAutopayOffers: SubscriberOffersWithBan[];

  @configProperty("")
  IsInteracEnabled: string;

  @configProperty("")
  IsSingleClickEnabled: string;

  @configProperty("")
  IsAutopayCreditEnabled: string;

  @configProperty()
  userProfileProvince: string;
}
