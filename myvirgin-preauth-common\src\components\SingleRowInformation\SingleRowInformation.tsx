import * as React from "react";
import {Text} from "@virgin/virgin-react-ui-library";

export interface SingleRowInformationProps {
  label: string | React.ReactNode,
  value: React.ReactNode,
  className?: string,
  needSRText?: boolean,
  srText?: string,
  role?: string,
  isMultiBan?: boolean,
}

export const SingleRowInformation = ({ label, value, className, needSRText, srText, role, isMultiBan }: SingleRowInformationProps) => (
  <div className={[className,"payment-mb-5 last:payment-mb-0"].join(" ").trim()} role={role}>
    <Text elementType="div"
      className={isMultiBan
        ? "vrui-flex payment-justify-between sm:payment-justify-normal vrui-text-14 vrui-leading-18 vrui-ml-10"
        : "vrui-flex payment-justify-between sm:payment-justify-normal vrui-text-14 vrui-leading-18"
      }
    >
      <label className={isMultiBan ? "sm:payment-w-[165px] payment-mr-30" : "sm:payment-w-[183px] payment-mr-30 sm:payment-mr-[60px]"}>
        {isMultiBan
          ? label
          : <strong className="payment-leading-18">{label}</strong>
        }
      </label>
      <Text elementType="div"
        className="vrui-text-gray vrui-text-right sm:vrui-text-left"
        aria-hidden={needSRText}
      >
        {value}
      </Text>
      {needSRText && (
        <span className="vrui-sr-only">
          {srText}
        </span>
      )}
    </Text>
  </div>
);

export default SingleRowInformation;
