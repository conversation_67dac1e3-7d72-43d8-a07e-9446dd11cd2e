import * as React from "react";
import { RadioButton } from "@virgin/virgin-react-ui-library";



export interface PaymentAlreadyExistRadioProps {
  name?: string;
  value: string;
  hasError?: boolean;
  showBankFieldsOnChange?: boolean;
  showOnChange?: (showBankFieldsOnChange: boolean) => void;
  childIndex?: number;
  label: string;
  idPrefix?: string;
  defaultChecked?: boolean;
  className?: string;
  onChange?: React.ChangeEventHandler<HTMLInputElement> | undefined;
}

export const PaymentAlreadyExistRadio = (
  {
    name = "bank-payment-radio",
    value,
    hasError,
    showOnChange,
    showBankFieldsOnChange = false,
    childIndex,
    defaultChecked,
    className,
    label,
    onChange,
    idPrefix = ""
  }:PaymentAlreadyExistRadioProps) => ( 
  <fieldset>
    <RadioButton
      className={[className,"vrui-flex vrui-items-center vrui-absolute vrui-size-full vrui-opacity-0 enabled:vrui-cursor-pointer disabled:vrui-cursor-default vrui-z-10"].join(" ").trim()}
      id={idPrefix + "bank-payment-radio-id-" + childIndex}
      name={name}
      value={value}
      variant="boxedInMobile"
      hasError={hasError}
      onChange={() => showOnChange? showOnChange(showBankFieldsOnChange): {onChange}}
      defaultChecked={defaultChecked}
    >
      <div className="vrui-text-14 vrui-leading-18 vrui-mt-3" dangerouslySetInnerHTML={{__html: label}}></div>
    </RadioButton>
  </fieldset>
);
