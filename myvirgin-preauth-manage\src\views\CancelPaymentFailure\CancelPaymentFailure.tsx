import React from "react";
import { AlertConfirmationSuccessCancel, AlertErrorFailureCancel } from "../Alert";
import { Button } from "@virgin/virgin-react-ui-library";
import { AlertNotificationList, AlertNotificationListItem, getItemAccountTypeName, PaymentItemAccountTypeName } from "myvirgin-preauth-common";
import { PaymentItem } from "myvirgin-preauth-common/src/models";
import { injectIntl } from "react-intl";

interface CancelPaymentFailureProps {
  intl: any;
  cancelledBansFailed: PaymentItem[];
  language: "en" | "fr";
}

const CancelPaymentFailureComponent = ({intl, cancelledBansFailed, language}: CancelPaymentFailureProps) => {
    
  const handleOnClick = () => {
    window.location.href = '/';
  };

  const hasMultipleFailure = (cancelledBansFailed && cancelledBansFailed.length > 1) ? true : false;

  const accountTypename : PaymentItemAccountTypeName = {
    MyBill: intl.formatMessage({id: "ACCOUNT_TYPENAME_MYBILL"}),
    Mobility: intl.formatMessage({id: "ACCOUNT_TYPENAME_MOBILITY"}),
    OneBill: intl.formatMessage({id: "ACCOUNT_TYPENAME_ONEBILL"}),
    TV: intl.formatMessage({id: "ACCOUNT_TYPENAME_TV"}),
    Internet: intl.formatMessage({id: "ACCOUNT_TYPENAME_INTERNET"}),
    HomePhone: intl.formatMessage({id: "ACCOUNT_TYPENAME_HOMEPHONE"}),
    MobilityAndOneBill: intl.formatMessage({id: "ACCOUNT_TYPENAME_MOBILITY_ONEBILL"}),
    SingleBan: intl.formatMessage({id: "ACCOUNT_TYPENAME_SINGLEBAN"})
  };

  return (
    <>
      <div className="payment-mt-[60px]">
        <AlertErrorFailureCancel hasMultipleFailure={hasMultipleFailure}>
          <AlertNotificationList>
            {cancelledBansFailed && cancelledBansFailed.map(item => (
              <AlertNotificationListItem 
                label={`${getItemAccountTypeName(item.AccountType, item.IsNM1Account, accountTypename)}`}
                labelDescription={item.NickName}
                variant={"regErrorBanList"}
              />
            ))}
          </AlertNotificationList>
        </AlertErrorFailureCancel>
      </div>
      <div className="payment-mt-16">
        <AlertConfirmationSuccessCancel />
      </div>
      <div className="payment-my-32 sm:payment-mt-40 payment-pb-[45px]">
        <Button 
          onClick={handleOnClick}
          variant="outlinedBlack"
          size="regular">
          {intl.formatMessage({id: "CANCEL_PAYMENT_BACK_TO_MY_ACCOUNT"})}
        </Button>
      </div>
    </>
  );
};

export const CancelPaymentFailure = injectIntl(CancelPaymentFailureComponent);
