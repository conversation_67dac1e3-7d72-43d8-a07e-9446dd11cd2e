import React from "react";
import { DivProps } from "@virgin/virgin-react-ui-library/dist/types/src/_types";
import {Text, Link, Price} from "@virgin/virgin-react-ui-library";


const variants = {
  priceList: "payment-flex payment-justify-between sm:payment-justify-normal payment-mb-5 last:payment-mb-0",
  errorList: "payment-mb-5 last:payment-mb-0 payment-text-red payment-list-item",
  accountList: "payment-mb-5 last:payment-mb-0 payment-text-gray payment-p-15 payment-pt-10 sm:payment-pt-15 payment-bg-gray-3 payment-rounded-[10px]",
  accountCancelList: "payment-mb-5 last:payment-mb-0 payment-text-gray payment-p-15 payment-pt-10 sm:payment-pt-15 payment-bg-gray-5 payment-rounded-[10px]",
  regErrorBanList: "payment-flex payment-justify-between sm:payment-justify-normal payment-mb-5 last:payment-mb-0"
};

export interface AlertNotificationListItemProps extends DivProps {
  cardDetails?: string,
  label?: string;
  labelDescription?: string | number;
  withPrice?: boolean;
  variant: "priceList" | "errorList" | "accountList" | "accountCancelList" | "regErrorBanList";
  priceSettings?: {
    language?: "en" | "fr",
    showZeroDecimalPart?: boolean,
    price?: number | null | undefined
  };
  inputRef?: React.RefObject<HTMLInputElement | HTMLSelectElement | null>;
}

export const AlertNotificationListItem = (
  { 
    cardDetails,
    label, 
    labelDescription, 
    priceSettings = {
      showZeroDecimalPart: true,
      price: 0.00
    }, 
    variant, 
    className, 
    children, 
    inputRef,
    ...rest 

  }: AlertNotificationListItemProps) => {
  const srText = label + " - " + labelDescription;

  const handleOnClick = () => {

    if(inputRef?.current instanceof HTMLSelectElement) {
      let sibling = inputRef?.current?.previousElementSibling as HTMLButtonElement;
      while (sibling) {
        if (sibling.tagName === 'BUTTON') {
          const button = sibling as HTMLButtonElement;
          scrollToElement(button);
          button.focus();
          return;
        }
        sibling = sibling.previousElementSibling as HTMLButtonElement;
      }
    } else {
      scrollToElement(inputRef?.current);
      inputRef?.current?.focus();
    }
  };

  const scrollToElement = (el: HTMLButtonElement | HTMLInputElement | undefined | null) => {
    if (el) {
      el.scrollIntoView({
        behavior: 'smooth',
        block: 'center', 
      });
    }
  };

  return (
    <div role="listitem"
      className={["payment-list-none",variants[variant],className].join(" ").trim()}
      {...rest}
    >
      {variant === "priceList" &&
        <>
          <Text className="payment-text-14 payment-leading-18 sm:payment-min-w-[153px]">
            <strong>{label}</strong>
          </Text>
          <Price 
            language={priceSettings.language ? priceSettings.language: "en"}
            showZeroDecimalPart={priceSettings.showZeroDecimalPart}
            price={typeof labelDescription === "number" ? labelDescription : 0.00}
            variant="defaultPrice"
            className="!payment-text-14 payment-leading-18  payment-font-normal"
          /> 
        </>   
      } 

      {variant === "errorList" &&
        <>
          <Link variant="textBlue" size="small" href="javascript:void(0)" aria-label={srText} className="payment-font-bold" onClick={handleOnClick}>{label} </Link>
          <span className="payment-text-gray payment-text-14" aria-hidden="true"> - {labelDescription}</span>
        </>
      }

      {variant === "accountList" &&
        <div className="payment-flex payment-flex-wrap payment-justify-between">
          <Text className="payment-mr-5 payment-mt-5 sm:payment-mt-0" aria-hidden="true">
            <strong className="payment-text-black">{label}</strong>&nbsp;{labelDescription}
          </Text>
          <Text elementType="span" className="payment-mt-5 sm:payment-mt-0">
            {cardDetails}
          </Text>
        </div>
      }
      {variant === "accountCancelList" &&
        <div className="payment-flex payment-flex-wrap payment-justify-between">
          <Text className="payment-mr-5 payment-mt-5 sm:payment-mt-0" aria-hidden="true">
            <strong className="payment-text-black">{label}</strong>&nbsp;{labelDescription}
          </Text>
          <Text elementType="span" className="payment-mt-5 sm:payment-mt-0">
            {cardDetails}
          </Text>
        </div>
      }

      {variant === "regErrorBanList" &&
        <>
          <Text className="payment-text-14 payment-leading-18 payment-mr-16">
            <strong>{label}</strong>
          </Text>
          <Text className="!payment-text-14 payment-leading-18  payment-font-normal"> 
            {labelDescription}
          </Text>
        </>   
      } 
    </div>
  );
};

export default AlertNotificationListItem;
