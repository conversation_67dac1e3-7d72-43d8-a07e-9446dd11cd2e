import * as React from "react";
import * as ReactDOM from "react-dom";
import { WidgetLoader, Init } from "bwtk";


export function initialize(config: any, root: string, debug: any) {
  const configFile = { ...config, "loader.staticWidgetMappings": {
    "myvirgin-preauth-setup": {
      factory: () => require("myvirgin-preauth-setup"),
      namespace: "Preauth/Setup"
    }
  }};
  Init(configFile);

  ReactDOM.render(
    <WidgetLoader widget="myvirgin-preauth-setup" />,
    document.getElementById(root));
}
