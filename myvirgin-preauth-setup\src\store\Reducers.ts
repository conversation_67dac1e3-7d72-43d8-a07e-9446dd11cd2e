import Config from "../Config";
import { CCDetails, CreditCardDetailsAction, IBankInfoRes, IGetRedirectUrl } from "../models";
import { ValidationErrors } from "../models/Error";
import { IRequestStatus } from "../models/App";
// import {IBankInfoFailure} from "../models"
export const getConfigReducer = (state: Config, action: any) => state;
export const setConfigReducer = (state: Config, action: any) => action.payload;

export const onCreditCardNumberChangeReducer = (state:CCDetails, { payload: newCreditCardDetails }: ReduxActions.Action<any>) => state && newCreditCardDetails ? { ...state, CreditCardNumber: newCreditCardDetails.CreditCardNumber } : state;
export const onCardHolderNameChangeReducer = (state:CCDetails, { payload: newCreditCardDetails }: ReduxActions.Action<any>) => state && newCreditCardDetails ? { ...state, CardholderName: newCreditCardDetails.CardholderName } : state;
export const onSecurityCodeChangeReducer = (state:CCDetails, { payload: newCreditCardDetails }: ReduxActions.Action<any>) => state && newCreditCardDetails ? { ...state, SecurityCode: newCreditCardDetails.SecurityCode } : state;
export const onCreditCardExpiryDateChangeReducer = (state:CCDetails, { payload: newCreditCardDetails }: ReduxActions.Action<any>) => state && newCreditCardDetails 
  ? { 
    ...state, 
    ExpireMonth: newCreditCardDetails.ExpireMonth,
    ExpireYear: newCreditCardDetails.ExpireYear,
  }
  : state;

export const setValidationErrorsReducer = (state: ValidationErrors, action: ReduxActions.Action<ValidationErrors>): ValidationErrors => {
  switch (action.type) {
    case CreditCardDetailsAction.SET_CREDIT_CARD_VALIDATION:
      const isErrorExisting = state.errors.map(error => 
        action.payload.errors.some(payloadError => payloadError.field === error.field) ? true : false
      ).filter(filterError => filterError === true);
        
      if (isErrorExisting.length > 0) {
        state.errors.map(error => 
          action.payload.errors.find(payloadError => payloadError.field === error.field)
            ? {...error, ...action.payload.errors}
            : error
        );
        return state;
      };

      return {
        ...state,
        errors: [...state.errors, ...action.payload.errors]
      };
      break;
    case CreditCardDetailsAction.RESET_CREDIT_CARD_VALIDATION:
      return {
        ...state,
        errors: []
      };
      break;
    default:
      return state;
  }
};

export const setcreatePaymentReducer = () => (state: any, { payload: createPayment }: ReduxActions.Action<any>) => ({ ...state, ...createPayment });
export const setcreatePaymentStatusReducer = (status: IRequestStatus) => (state: IRequestStatus, { payload: createPaymentStatus }: ReduxActions.Action<IRequestStatus>) => (status || IRequestStatus.IDLE);

export const setValidateOrderPaymentReducer = () => (state: any, { payload: validateOrderPayment }: ReduxActions.Action<any>) => ({ ...state, ...validateOrderPayment });
export const setValidateOrderPaymentStatusReducer = (status: IRequestStatus) => (state: IRequestStatus, { payload: validateOrderFormStatus }: ReduxActions.Action<IRequestStatus>) => (status || IRequestStatus.IDLE);

export const setsubmitOrderPaymentReducer = () => (state: any, { payload: submitOrderPayment }: ReduxActions.Action<any>) => ({ ...state, ...submitOrderPayment });
export const setsubmitOrderPaymentStatusReducer = (status: IRequestStatus) => (state: IRequestStatus, { payload: submitOrderFormStatus }: ReduxActions.Action<IRequestStatus>) => (status || IRequestStatus.IDLE);
export const setPasskeyReducer = () => (state: any, { payload: setPassKey }:  ReduxActions.Action<any>) => setPassKey || state;

export const setGetRedirectUrlReducer = () => (state: IGetRedirectUrl, { payload: redirectUrl }: ReduxActions.Action<IGetRedirectUrl>) => ({ ...state, ...redirectUrl });
export const setInteracBankInfoReducer = () => (state: IBankInfoRes, { payload: interacBankInfo }: ReduxActions.Action<IBankInfoRes>) => ({ ...state, ...interacBankInfo });

export const setIsLoadingReducer = (state: boolean, action: ReduxActions.Action<boolean>) => action.payload!;

export const setcreateMultiPaymentReducer = () => (state: any, { payload: createPayment }: ReduxActions.Action<any>) => ({ ...state, ...createPayment });
export const setcreateMultiPaymentStatusReducer = (status: IRequestStatus) => (state: IRequestStatus, { payload: createPaymentStatus }: ReduxActions.Action<IRequestStatus>) => (status || IRequestStatus.IDLE);

export const setValidateMultiOrderPaymentReducer = () => (state: any, { payload: validateMultiOrderPayment }: ReduxActions.Action<any>) => ({ ...state, ...validateMultiOrderPayment });
export const setValidateMultiOrderPaymentStatusReducer = (status: IRequestStatus) => (state: IRequestStatus, { payload: validateOrderFormStatus }: ReduxActions.Action<IRequestStatus>) => (status || IRequestStatus.IDLE);

export const setsubmitMultiOrderPaymentReducer = () => (state: any, { payload: submitMultiOrderForm }: ReduxActions.Action<any>) => ({ ...state, ...submitMultiOrderForm });
export const setsubmitMultiOrderPaymentStatusReducer = (status: IRequestStatus) => (state: IRequestStatus, { payload: submitOrderFormStatus }: ReduxActions.Action<IRequestStatus>) => (status || IRequestStatus.IDLE);
export const setInteractFailureReducer = () => (state:any , { payload: interactBankFailureInfo }: ReduxActions.Action<any>) => ({...state, ...interactBankFailureInfo});
export const setFailureReducer = () => (state:any , { payload: interactBankFailureInfo }: ReduxActions.Action<any>) => ({...state, ...interactBankFailureInfo});
